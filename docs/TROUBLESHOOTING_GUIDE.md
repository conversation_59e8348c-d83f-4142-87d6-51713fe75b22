# Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered when operating the RAG Legal Chatbot system. Issues are organized by component and severity.

## Quick Diagnostic Commands

```bash
# System health overview
./scripts/health-check.sh quick

# Check all pods status
kubectl get pods -n rag-chatbot

# Check recent events
kubectl get events -n rag-chatbot --sort-by='.lastTimestamp' | tail -20

# Check resource usage
kubectl top pods -n rag-chatbot
kubectl top nodes

# Check logs for errors
kubectl logs -l app=rag-legal-chatbot -n rag-chatbot --since=1h | grep -i error
```

## Application Issues

### API Service Not Responding

**Symptoms:**
- Health check endpoints return 5xx errors
- API requests timeout
- No response from service

**Diagnosis:**
```bash
# Check pod status
kubectl get pods -n rag-chatbot -l component=api

# Check pod logs
kubectl logs -f deployment/rag-chatbot-api -n rag-chatbot

# Check service endpoints
kubectl get endpoints rag-chatbot-api-service -n rag-chatbot

# Test internal connectivity
kubectl exec -it deployment/rag-chatbot-api -n rag-chatbot -- curl http://localhost:8000/health
```

**Solutions:**

1. **Restart API pods:**
```bash
kubectl rollout restart deployment/rag-chatbot-api -n rag-chatbot
kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot
```

2. **Check resource limits:**
```bash
kubectl describe pod -l component=api -n rag-chatbot | grep -A 5 "Limits\|Requests"
```

3. **Scale up if resource constrained:**
```bash
kubectl scale deployment/rag-chatbot-api --replicas=5 -n rag-chatbot
```

4. **Check configuration:**
```bash
kubectl get configmap app-config -n rag-chatbot -o yaml
kubectl get secret rag-chatbot-secrets -n rag-chatbot
```

### Slow Response Times

**Symptoms:**
- API responses take > 5 seconds
- Timeout errors
- High CPU/memory usage

**Diagnosis:**
```bash
# Check response times
curl -w "@curl-format.txt" -o /dev/null -s http://your-domain.com/api/health

# Check resource usage
kubectl top pods -n rag-chatbot

# Check HPA status
kubectl get hpa -n rag-chatbot

# Check database performance
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli --latency-history
```

**Solutions:**

1. **Scale horizontally:**
```bash
kubectl scale deployment/rag-chatbot-api --replicas=8 -n rag-chatbot
```

2. **Optimize database queries:**
```bash
# Check Redis slow log
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli SLOWLOG GET 10

# Clear cache if needed
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli FLUSHDB
```

3. **Check external API limits:**
```bash
# Test Google AI API
curl -H "Authorization: Bearer $GOOGLE_API_KEY" https://generativelanguage.googleapis.com/v1beta/models

# Test Cohere API
curl -H "Authorization: Bearer $COHERE_API_KEY" https://api.cohere.ai/v1/models
```

### Worker Service Issues

**Symptoms:**
- Background tasks not processing
- Queue backlog growing
- Worker pods crashing

**Diagnosis:**
```bash
# Check worker pods
kubectl get pods -n rag-chatbot -l component=worker

# Check worker logs
kubectl logs -f deployment/rag-chatbot-worker -n rag-chatbot

# Check Celery queue status
kubectl exec -it deployment/rag-chatbot-worker -n rag-chatbot -- celery -A chatbot-engine.src.shared.celery_app inspect active
```

**Solutions:**

1. **Restart workers:**
```bash
kubectl rollout restart deployment/rag-chatbot-worker -n rag-chatbot
```

2. **Scale workers:**
```bash
kubectl scale deployment/rag-chatbot-worker --replicas=5 -n rag-chatbot
```

3. **Clear stuck tasks:**
```bash
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli FLUSHDB
```

## Database Issues

### Redis Connection Problems

**Symptoms:**
- Connection refused errors
- Redis timeouts
- Cache misses

**Diagnosis:**
```bash
# Check Redis pod status
kubectl get pods -n rag-chatbot -l component=redis

# Test Redis connectivity
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli ping

# Check Redis logs
kubectl logs -f deployment/redis -n rag-chatbot

# Check Redis configuration
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli CONFIG GET "*"
```

**Solutions:**

1. **Restart Redis:**
```bash
kubectl rollout restart deployment/redis -n rag-chatbot
```

2. **Check memory usage:**
```bash
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli INFO memory
```

3. **Optimize Redis configuration:**
```bash
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli CONFIG SET maxmemory-policy allkeys-lru
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli CONFIG SET tcp-keepalive 300
```

### Milvus Vector Database Issues

**Symptoms:**
- Vector search failures
- Milvus connection errors
- Slow vector queries

**Diagnosis:**
```bash
# Check Milvus pod status
kubectl get pods -n rag-chatbot -l component=milvus

# Test Milvus health
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- curl http://localhost:9091/healthz

# Check Milvus logs
kubectl logs -f deployment/milvus-standalone -n rag-chatbot

# Check collection status
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- python3 -c "
from pymilvus import connections, utility
connections.connect('default', host='localhost', port='19530')
print('Collections:', utility.list_collections())
"
```

**Solutions:**

1. **Restart Milvus:**
```bash
kubectl rollout restart deployment/milvus-standalone -n rag-chatbot
```

2. **Check dependencies (etcd, MinIO):**
```bash
kubectl get pods -n rag-chatbot -l component=etcd
kubectl get pods -n rag-chatbot -l component=minio
```

3. **Rebuild indexes if corrupted:**
```bash
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- python3 -c "
from pymilvus import connections, Collection
connections.connect('default', host='localhost', port='19530')
collection = Collection('legal_docs_v1')
collection.drop_index()
collection.create_index(field_name='embedding', index_params={'metric_type': 'COSINE', 'index_type': 'IVF_FLAT', 'params': {'nlist': 1024}})
"
```

## Infrastructure Issues

### Pod Scheduling Problems

**Symptoms:**
- Pods stuck in Pending state
- Resource allocation failures
- Node capacity issues

**Diagnosis:**
```bash
# Check pod status and events
kubectl describe pod -l app=rag-legal-chatbot -n rag-chatbot

# Check node resources
kubectl describe nodes | grep -A 5 "Allocated resources"

# Check resource quotas
kubectl describe resourcequota -n rag-chatbot
```

**Solutions:**

1. **Scale cluster nodes:**
```bash
# For managed clusters, increase node count
# For self-managed, add more nodes
```

2. **Adjust resource requests:**
```bash
kubectl patch deployment rag-chatbot-api -n rag-chatbot -p '{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "api",
          "resources": {
            "requests": {
              "memory": "512Mi",
              "cpu": "250m"
            }
          }
        }]
      }
    }
  }
}'
```

### Network Connectivity Issues

**Symptoms:**
- Service discovery failures
- DNS resolution errors
- Network timeouts

**Diagnosis:**
```bash
# Check service endpoints
kubectl get endpoints -n rag-chatbot

# Test DNS resolution
kubectl exec -it deployment/rag-chatbot-api -n rag-chatbot -- nslookup redis-service.rag-chatbot.svc.cluster.local

# Check network policies
kubectl get networkpolicy -n rag-chatbot

# Test connectivity between pods
kubectl exec -it deployment/rag-chatbot-api -n rag-chatbot -- curl http://redis-service:6379
```

**Solutions:**

1. **Restart CoreDNS:**
```bash
kubectl rollout restart deployment/coredns -n kube-system
```

2. **Check network policies:**
```bash
kubectl describe networkpolicy -n rag-chatbot
```

3. **Verify service configuration:**
```bash
kubectl get services -n rag-chatbot -o wide
```

### Storage Issues

**Symptoms:**
- PVC mounting failures
- Disk space errors
- I/O performance issues

**Diagnosis:**
```bash
# Check PVC status
kubectl get pvc -n rag-chatbot

# Check storage class
kubectl get storageclass

# Check disk usage
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- df -h

# Check I/O performance
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- iostat -x 1 5
```

**Solutions:**

1. **Expand PVC if supported:**
```bash
kubectl patch pvc milvus-data-pvc -n rag-chatbot -p '{"spec":{"resources":{"requests":{"storage":"200Gi"}}}}'
```

2. **Clean up old data:**
```bash
# Clean up old logs
kubectl exec -it deployment/rag-chatbot-api -n rag-chatbot -- find /app/logs -name "*.log" -mtime +7 -delete

# Clean up temporary files
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- find /tmp -type f -mtime +1 -delete
```

## Security Issues

### Authentication Failures

**Symptoms:**
- 401 Unauthorized errors
- API key validation failures
- JWT token errors

**Diagnosis:**
```bash
# Check secret configuration
kubectl get secret rag-chatbot-secrets -n rag-chatbot -o yaml

# Check API key in logs
kubectl logs -f deployment/rag-chatbot-api -n rag-chatbot | grep -i "auth\|token\|unauthorized"

# Test API key
curl -H "Authorization: Bearer $API_KEY" http://your-domain.com/api/health
```

**Solutions:**

1. **Rotate API keys:**
```bash
./scripts/secrets-manager.sh set API_KEY "new-api-key"
./scripts/secrets-manager.sh export
kubectl rollout restart deployment/rag-chatbot-api -n rag-chatbot
```

2. **Check secret mounting:**
```bash
kubectl describe pod -l component=api -n rag-chatbot | grep -A 10 "Mounts\|Volumes"
```

### SSL/TLS Certificate Issues

**Symptoms:**
- HTTPS connection failures
- Certificate validation errors
- Browser security warnings

**Diagnosis:**
```bash
# Check certificate status
kubectl get certificates -n rag-chatbot

# Check certificate details
kubectl describe certificate rag-chatbot-tls -n rag-chatbot

# Check cert-manager logs
kubectl logs -f deployment/cert-manager -n cert-manager

# Test certificate
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

**Solutions:**

1. **Force certificate renewal:**
```bash
kubectl delete certificate rag-chatbot-tls -n rag-chatbot
kubectl apply -f security/tls-certificates.yaml
```

2. **Check DNS validation:**
```bash
kubectl describe challenge -n rag-chatbot
```

## Monitoring and Alerting Issues

### Metrics Collection Problems

**Symptoms:**
- Missing metrics in Grafana
- Prometheus targets down
- Alert manager not firing

**Diagnosis:**
```bash
# Check Prometheus targets
kubectl port-forward service/prometheus 9090:9090 -n monitoring
# Visit http://localhost:9090/targets

# Check Grafana datasources
kubectl port-forward service/grafana 3000:3000 -n monitoring
# Visit http://localhost:3000

# Check metrics endpoints
curl http://your-domain.com/metrics
```

**Solutions:**

1. **Restart monitoring stack:**
```bash
kubectl rollout restart deployment/prometheus -n monitoring
kubectl rollout restart deployment/grafana -n monitoring
```

2. **Check service discovery:**
```bash
kubectl get servicemonitor -n rag-chatbot
kubectl describe servicemonitor -n rag-chatbot
```

## Performance Issues

### High Memory Usage

**Symptoms:**
- OOMKilled pods
- Memory pressure warnings
- Slow garbage collection

**Diagnosis:**
```bash
# Check memory usage
kubectl top pods -n rag-chatbot

# Check memory limits
kubectl describe pod -l app=rag-legal-chatbot -n rag-chatbot | grep -A 5 "Limits\|Requests"

# Check for memory leaks
kubectl exec -it deployment/rag-chatbot-api -n rag-chatbot -- ps aux --sort=-%mem | head -10
```

**Solutions:**

1. **Increase memory limits:**
```bash
kubectl patch deployment rag-chatbot-api -n rag-chatbot -p '{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "api",
          "resources": {
            "limits": {
              "memory": "4Gi"
            }
          }
        }]
      }
    }
  }
}'
```

2. **Restart pods to clear memory:**
```bash
kubectl rollout restart deployment/rag-chatbot-api -n rag-chatbot
```

### High CPU Usage

**Symptoms:**
- CPU throttling
- Slow response times
- High load averages

**Diagnosis:**
```bash
# Check CPU usage
kubectl top pods -n rag-chatbot

# Check CPU limits
kubectl describe pod -l app=rag-legal-chatbot -n rag-chatbot | grep -A 5 "Limits\|Requests"

# Check HPA scaling
kubectl get hpa -n rag-chatbot
```

**Solutions:**

1. **Scale horizontally:**
```bash
kubectl scale deployment/rag-chatbot-api --replicas=6 -n rag-chatbot
```

2. **Increase CPU limits:**
```bash
kubectl patch deployment rag-chatbot-api -n rag-chatbot -p '{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "api",
          "resources": {
            "limits": {
              "cpu": "2"
            }
          }
        }]
      }
    }
  }
}'
```

## Emergency Procedures

### Complete System Recovery

If the entire system is down:

1. **Check cluster status:**
```bash
kubectl cluster-info
kubectl get nodes
```

2. **Restore from backup:**
```bash
./scripts/backup-restore.sh restore latest-backup
```

3. **Redeploy if necessary:**
```bash
./k8s/deploy.sh deploy
```

4. **Verify system health:**
```bash
./scripts/health-check.sh comprehensive
```

### Data Corruption Recovery

If data corruption is detected:

1. **Stop all write operations:**
```bash
kubectl scale deployment/rag-chatbot-api --replicas=0 -n rag-chatbot
kubectl scale deployment/rag-chatbot-worker --replicas=0 -n rag-chatbot
```

2. **Assess corruption scope:**
```bash
# Check Redis data integrity
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli DBSIZE

# Check Milvus collections
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- python3 -c "
from pymilvus import connections, utility
connections.connect('default', host='localhost', port='19530')
for collection in utility.list_collections():
    print(f'{collection}: {Collection(collection).num_entities} entities')
"
```

3. **Restore from backup:**
```bash
./scripts/backup-restore.sh restore production-backup-YYYYMMDD
```

4. **Verify data integrity:**
```bash
# Run data validation scripts
# Verify critical collections exist
# Test sample queries
```

5. **Resume operations:**
```bash
kubectl scale deployment/rag-chatbot-api --replicas=3 -n rag-chatbot
kubectl scale deployment/rag-chatbot-worker --replicas=2 -n rag-chatbot
```

## Getting Help

### Log Collection

When reporting issues, collect these logs:

```bash
# System overview
kubectl get all -n rag-chatbot > system-overview.txt

# Pod logs
kubectl logs -l app=rag-legal-chatbot -n rag-chatbot --since=1h > application-logs.txt

# Events
kubectl get events -n rag-chatbot --sort-by='.lastTimestamp' > events.txt

# Resource usage
kubectl top pods -n rag-chatbot > resource-usage.txt
kubectl top nodes >> resource-usage.txt
```

### Contact Information

- **Level 1 Support**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **Emergency Hotline**: +1-XXX-XXX-XXXX

### Escalation Matrix

| Issue Severity | Response Time | Contact |
|----------------|---------------|---------|
| P1 (Critical)  | 15 minutes    | On-call Engineer |
| P2 (High)      | 1 hour        | DevOps Team |
| P3 (Medium)    | 4 hours       | Engineering Team |
| P4 (Low)       | 24 hours      | Support Team |

---

**Last Updated**: December 2024  
**Next Review**: March 2025
