# Disaster Recovery Configuration for RAG Legal Chatbot

# Multi-Region Backup Replication
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-config
  namespace: rag-chatbot
data:
  recovery-plan.yaml: |
    disaster_recovery:
      rto: 15  # Recovery Time Objective in minutes
      rpo: 60  # Recovery Point Objective in minutes
      
      backup_locations:
        primary: "s3://rag-chatbot-backups-us-west-2"
        secondary: "s3://rag-chatbot-backups-us-east-1"
        tertiary: "s3://rag-chatbot-backups-eu-west-1"
      
      recovery_procedures:
        - name: "Database Recovery"
          priority: 1
          steps:
            - "Restore Milvus data from latest backup"
            - "Restore Redis data from latest backup"
            - "Verify data integrity"
        
        - name: "Application Recovery"
          priority: 2
          steps:
            - "Deploy application containers"
            - "Restore configuration and secrets"
            - "Verify application health"
        
        - name: "Traffic Recovery"
          priority: 3
          steps:
            - "Update DNS records"
            - "Enable load balancer"
            - "Verify end-to-end functionality"
---
# Disaster Recovery Automation
apiVersion: batch/v1
kind: Job
metadata:
  name: disaster-recovery-test
  namespace: rag-chatbot
spec:
  template:
    spec:
      serviceAccountName: backup-service-account
      containers:
      - name: dr-test
        image: alpine/k8s:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "Starting Disaster Recovery Test..."
          
          # Test 1: Backup Availability
          echo "Testing backup availability..."
          aws s3 ls s3://rag-chatbot-backups/velero/ || exit 1
          aws s3 ls s3://rag-chatbot-backups/milvus-backups/ || exit 1
          aws s3 ls s3://rag-chatbot-backups/redis-backups/ || exit 1
          
          # Test 2: Recovery Simulation
          echo "Simulating recovery process..."
          
          # Create test namespace
          kubectl create namespace dr-test-$(date +%s) || true
          
          # Test Velero restore (dry-run)
          velero restore create dr-test-$(date +%s) \
            --from-backup daily-backup-$(date +%Y%m%d) \
            --namespace-mappings rag-chatbot:dr-test-$(date +%s) \
            --dry-run
          
          # Test database restore scripts
          echo "Testing database restore procedures..."
          
          # Verify backup integrity
          LATEST_MILVUS_BACKUP=$(aws s3 ls s3://rag-chatbot-backups/milvus-backups/ | sort | tail -n 1 | awk '{print $4}')
          aws s3 cp "s3://rag-chatbot-backups/milvus-backups/$LATEST_MILVUS_BACKUP" /tmp/
          tar -tzf "/tmp/$LATEST_MILVUS_BACKUP" > /dev/null || exit 1
          
          LATEST_REDIS_BACKUP=$(aws s3 ls s3://rag-chatbot-backups/redis-backups/ | sort | tail -n 1 | awk '{print $4}')
          aws s3 cp "s3://rag-chatbot-backups/redis-backups/$LATEST_REDIS_BACKUP" /tmp/
          tar -tzf "/tmp/$LATEST_REDIS_BACKUP" > /dev/null || exit 1
          
          echo "Disaster Recovery Test Completed Successfully"
        env:
        - name: AWS_DEFAULT_REGION
          value: "us-west-2"
      restartPolicy: Never
---
# Cross-Region Backup Replication
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cross-region-replication
  namespace: rag-chatbot
spec:
  schedule: "0 6 * * *"  # Daily at 6 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-service-account
          containers:
          - name: replication
            image: amazon/aws-cli:latest
            command:
            - /bin/sh
            - -c
            - |
              echo "Starting cross-region backup replication..."
              
              # Replicate to secondary region
              aws s3 sync s3://rag-chatbot-backups-us-west-2/ s3://rag-chatbot-backups-us-east-1/ \
                --delete --storage-class STANDARD_IA
              
              # Replicate to tertiary region
              aws s3 sync s3://rag-chatbot-backups-us-west-2/ s3://rag-chatbot-backups-eu-west-1/ \
                --delete --storage-class GLACIER
              
              # Verify replication
              PRIMARY_COUNT=$(aws s3 ls s3://rag-chatbot-backups-us-west-2/ --recursive | wc -l)
              SECONDARY_COUNT=$(aws s3 ls s3://rag-chatbot-backups-us-east-1/ --recursive | wc -l)
              
              if [ "$PRIMARY_COUNT" -ne "$SECONDARY_COUNT" ]; then
                echo "ERROR: Backup replication failed"
                exit 1
              fi
              
              echo "Cross-region replication completed successfully"
            env:
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
          restartPolicy: OnFailure
---
# Recovery Runbook ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: recovery-runbook
  namespace: rag-chatbot
data:
  runbook.md: |
    # Disaster Recovery Runbook
    
    ## Emergency Contacts
    - On-call Engineer: +1-XXX-XXX-XXXX
    - DevOps Team Lead: +1-XXX-XXX-XXXX
    - Security Team: <EMAIL>
    
    ## Recovery Procedures
    
    ### 1. Assess the Situation (5 minutes)
    - Determine scope of outage
    - Identify affected components
    - Estimate recovery time
    - Notify stakeholders
    
    ### 2. Database Recovery (10 minutes)
    ```bash
    # Restore Milvus
    kubectl apply -f backup/milvus-restore.yaml
    
    # Restore Redis
    kubectl apply -f backup/redis-restore.yaml
    
    # Verify data integrity
    kubectl exec -it milvus-pod -- python verify_data.py
    ```
    
    ### 3. Application Recovery (5 minutes)
    ```bash
    # Restore from Velero backup
    velero restore create emergency-restore \
      --from-backup daily-backup-latest \
      --wait
    
    # Verify pods are running
    kubectl get pods -n rag-chatbot
    ```
    
    ### 4. Traffic Recovery (5 minutes)
    ```bash
    # Update DNS (if needed)
    # Enable load balancer
    kubectl patch service nginx-service -p '{"spec":{"type":"LoadBalancer"}}'
    
    # Verify health
    curl -f http://your-domain.com/health
    ```
    
    ### 5. Post-Recovery Validation
    - Run smoke tests
    - Verify all endpoints
    - Check monitoring dashboards
    - Update incident log
    
    ## Rollback Procedures
    If recovery fails, rollback to previous stable state:
    ```bash
    velero restore create rollback-restore \
      --from-backup weekly-full-backup-latest \
      --wait
    ```
---
# Backup Monitoring and Alerting
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: backup-monitoring
  namespace: rag-chatbot
spec:
  groups:
  - name: backup-alerts
    rules:
    - alert: BackupFailed
      expr: kube_job_status_failed{job_name=~".*backup.*"} > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Backup job failed"
        description: "Backup job {{ $labels.job_name }} has failed"
    
    - alert: BackupMissing
      expr: time() - kube_job_status_completion_time{job_name=~".*backup.*"} > 86400
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "Backup is overdue"
        description: "No successful backup in the last 24 hours"
    
    - alert: BackupStorageFull
      expr: kubelet_volume_stats_available_bytes{persistentvolumeclaim="backup-storage-pvc"} / kubelet_volume_stats_capacity_bytes{persistentvolumeclaim="backup-storage-pvc"} < 0.1
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "Backup storage almost full"
        description: "Backup storage is {{ $value | humanizePercentage }} full"
---
# Backup Verification Job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-verification
  namespace: rag-chatbot
spec:
  schedule: "0 8 * * *"  # Daily at 8 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-service-account
          containers:
          - name: verification
            image: alpine/k8s:latest
            command:
            - /bin/sh
            - -c
            - |
              echo "Starting backup verification..."
              
              # Check Velero backups
              VELERO_BACKUPS=$(velero backup get --output json | jq '.items | length')
              if [ "$VELERO_BACKUPS" -lt 7 ]; then
                echo "WARNING: Less than 7 Velero backups found"
              fi
              
              # Check database backups
              MILVUS_BACKUPS=$(aws s3 ls s3://rag-chatbot-backups/milvus-backups/ | wc -l)
              REDIS_BACKUPS=$(aws s3 ls s3://rag-chatbot-backups/redis-backups/ | wc -l)
              
              if [ "$MILVUS_BACKUPS" -lt 7 ] || [ "$REDIS_BACKUPS" -lt 7 ]; then
                echo "WARNING: Insufficient database backups"
              fi
              
              # Test restore capability (sample)
              LATEST_BACKUP=$(velero backup get --output json | jq -r '.items[0].metadata.name')
              velero restore create test-restore-$(date +%s) \
                --from-backup "$LATEST_BACKUP" \
                --include-resources pods \
                --dry-run
              
              echo "Backup verification completed"
            env:
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
          restartPolicy: OnFailure
