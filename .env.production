# Production Environment Configuration for RAG Legal Chatbot
# Copy this file to .env and update with your actual values

# =============================================================================
# API KEYS (REQUIRED - Replace with actual values)
# =============================================================================
GOOGLE_API_KEY=your_google_api_key_here
COHERE_API_KEY=your_cohere_api_key_here

# =============================================================================
# SECURITY (REQUIRED - Generate secure values)
# =============================================================================
SECRET_KEY=your_super_secret_key_change_this_in_production_min_32_chars
ADMIN_API_KEY=your_admin_api_key_for_admin_endpoints

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Milvus Vector Database
MILVUS_HOST=milvus-service
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=legal_docs_v1
MILVUS_USER=
MILVUS_PASSWORD=

# Redis Cache and Message Broker
REDIS_HOST=redis-service
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=100

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
ENVIRONMENT=production
DEBUG=false
PYTHONPATH=/app
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_RELOAD=false
API_ACCESS_LOG=true

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================
CELERY_BROKER_URL=redis://redis-service:6379/0
CELERY_RESULT_BACKEND=redis://redis-service:6379/0
CELERY_WORKER_CONCURRENCY=2
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_TASK_ACKS_LATE=true
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
CELERY_TASK_TIME_LIMIT=300
CELERY_TASK_SOFT_TIME_LIMIT=240

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# CORS Configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=*

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=30
RATE_LIMIT_BURST=10

# Authentication
ADMIN_ENDPOINTS_REQUIRE_AUTH=true
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# Metrics
ENABLE_METRICS=true
METRICS_PORT=9090
PROMETHEUS_MULTIPROC_DIR=/tmp/prometheus_multiproc

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/app/chatbot-engine/logs/app.log
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_BACKUP_COUNT=5
LOG_FILE_ROTATION=true

# Structured Logging
STRUCTLOG_ENABLED=true
STRUCTLOG_TIMESTAMP_FORMAT=iso

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
# Request Handling
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=60
KEEP_ALIVE_TIMEOUT=5
GRACEFUL_TIMEOUT=30

# Connection Pooling
HTTP_POOL_CONNECTIONS=10
HTTP_POOL_MAXSIZE=10
HTTP_MAX_RETRIES=3

# Memory Management
MAX_MEMORY_USAGE=2048  # MB
MEMORY_CHECK_INTERVAL=60  # seconds

# =============================================================================
# LLM CONFIGURATION
# =============================================================================
# Google AI (Gemini)
GOOGLE_MODEL_NAME=gemini-1.5-flash
GOOGLE_EMBEDDING_MODEL=text-embedding-004
GOOGLE_TEMPERATURE=0.1
GOOGLE_MAX_TOKENS=2048
GOOGLE_TOP_P=0.9
GOOGLE_TOP_K=40

# Cohere
COHERE_MODEL_NAME=rerank-multilingual-v3.0
COHERE_MAX_CHUNKS_PER_DOC=10
COHERE_TOP_N=5

# =============================================================================
# RETRIEVAL CONFIGURATION
# =============================================================================
# Vector Search
VECTOR_SEARCH_K=20
VECTOR_SEARCH_SIMILARITY_THRESHOLD=0.75
VECTOR_SEARCH_METRIC_TYPE=COSINE

# Keyword Search
KEYWORD_SEARCH_K=50
KEYWORD_SEARCH_BOOST=1.2

# Hybrid Search
HYBRID_SEARCH_ALPHA=0.7  # Weight for vector search (0.0 = keyword only, 1.0 = vector only)

# Reranking
RERANKING_ENABLED=true
RERANKING_TOP_N=5
RERANKING_MODEL=rerank-multilingual-v3.0

# =============================================================================
# TEXT PROCESSING
# =============================================================================
# Chunking
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
CHUNK_SEPARATORS=\n\n,\n,. ,? ,! ,; ,:

# Language Processing
DEFAULT_LANGUAGE=de
SUPPORTED_LANGUAGES=de,en
TEXT_ENCODING=utf-8

# =============================================================================
# CRAWLING CONFIGURATION
# =============================================================================
# Web Crawling
CRAWL_DELAY_BETWEEN_REQUESTS=2.0
CRAWL_MAX_CONCURRENT_REQUESTS=3
CRAWL_TIMEOUT=60
CRAWL_RESPECT_ROBOTS_TXT=true
CRAWL_USER_AGENT=RAG-Legal-Chatbot/1.0
CRAWL_MAX_RETRIES=3

# PDF Processing
PDF_MAX_PAGES=1000
PDF_EXTRACT_IMAGES=false
PDF_EXTRACT_TABLES=true

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# File Storage
DATA_DIR=/app/chatbot-engine/data
UPLOAD_DIR=/app/chatbot-engine/data/uploads
TEMP_DIR=/tmp
MAX_UPLOAD_SIZE=100MB

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/app/chatbot-engine/backups

# =============================================================================
# HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Service Health Checks
MILVUS_HEALTH_CHECK=true
REDIS_HEALTH_CHECK=true
EXTERNAL_API_HEALTH_CHECK=true

# =============================================================================
# DEVELOPMENT/TESTING (Set to false in production)
# =============================================================================
ENABLE_SWAGGER_UI=false
ENABLE_REDOC=false
ENABLE_DEBUG_ENDPOINTS=false
ENABLE_TEST_ENDPOINTS=false

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# MinIO (for Milvus)
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_ENDPOINT=minio-service:9000
MINIO_SECURE=false

# Notification Services (Optional)
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_FROM_ADDRESS=<EMAIL>

# Webhook Notifications
WEBHOOK_ENABLED=false
WEBHOOK_URL=
WEBHOOK_SECRET=

# =============================================================================
# TIMEZONE AND LOCALIZATION
# =============================================================================
TIMEZONE=Europe/Berlin
LOCALE=de_DE.UTF-8
DATE_FORMAT=%Y-%m-%d
TIME_FORMAT=%H:%M:%S
DATETIME_FORMAT=%Y-%m-%d %H:%M:%S
