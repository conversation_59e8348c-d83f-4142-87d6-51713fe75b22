version: '3.8'

services:
  # Milvus Vector Database
  etcd:
    container_name: milvus-etcd-dev
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data_dev:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3

  minio:
    container_name: milvus-minio-dev
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data_dev:/data
    command: minio server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  milvus:
    container_name: milvus-standalone-dev
    image: milvusdb/milvus:v2.3.0
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data_dev:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

  # Redis for caching and job queue
  redis:
    container_name: rag-redis-dev
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RAG Chatbot API (Development mode with hot reload)
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: rag-chatbot-api-dev
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DEBUG=true
    volumes:
      - ./chatbot-engine:/app/chatbot-engine
      - ./requirements.txt:/app/requirements.txt
    depends_on:
      milvus:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: unless-stopped
    command: ["uvicorn", "chatbot-engine.src.online_pipeline.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # Celery Worker for background tasks (Development)
  worker:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.worker
    container_name: rag-chatbot-worker-dev
    environment:
      - ENVIRONMENT=development
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./chatbot-engine:/app/chatbot-engine
    depends_on:
      milvus:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: unless-stopped
    command: ["celery", "-A", "chatbot-engine.src.shared.celery_app", "worker", "--loglevel=debug", "--concurrency=1"]

  # Flower for Celery monitoring (Development only)
  flower:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.worker
    container_name: rag-chatbot-flower-dev
    ports:
      - "5555:5555"
    environment:
      - ENVIRONMENT=development
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
      - worker
    env_file:
      - .env
    restart: unless-stopped
    command: ["celery", "-A", "chatbot-engine.src.shared.celery_app", "flower", "--port=5555"]

volumes:
  etcd_data_dev:
  minio_data_dev:
  milvus_data_dev:
  redis_data_dev:
