#!/usr/bin/env python3
"""
<PERSON>ript to fix relative imports in the RAG Chatbot codebase.
This script converts relative imports to absolute imports.
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Fix relative imports in a single Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix imports like "from ...shared.config import"
        content = re.sub(r'from \.\.\.shared\.', 'from shared.', content)
        
        # Fix imports like "from ..shared.config import"
        content = re.sub(r'from \.\.shared\.', 'from shared.', content)
        
        # Fix imports like "from ...offline_pipeline."
        content = re.sub(r'from \.\.\.offline_pipeline\.', 'from offline_pipeline.', content)
        
        # Fix imports like "from ..offline_pipeline."
        content = re.sub(r'from \.\.offline_pipeline\.', 'from offline_pipeline.', content)
        
        # Fix imports like "from ...online_pipeline."
        content = re.sub(r'from \.\.\.online_pipeline\.', 'from online_pipeline.', content)
        
        # Fix imports like "from ..online_pipeline."
        content = re.sub(r'from \.\.online_pipeline\.', 'from online_pipeline.', content)
        
        # Fix imports like "from ...admin_interface."
        content = re.sub(r'from \.\.\.admin_interface\.', 'from admin_interface.', content)
        
        # Fix imports like "from ..admin_interface."
        content = re.sub(r'from \.\.admin_interface\.', 'from admin_interface.', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed imports in: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix imports in all Python files."""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print(f"Source directory {src_dir} not found!")
        return
    
    fixed_count = 0
    total_count = 0
    
    # Find all Python files
    for py_file in src_dir.rglob("*.py"):
        total_count += 1
        if fix_imports_in_file(py_file):
            fixed_count += 1
    
    print(f"\nProcessed {total_count} Python files")
    print(f"Fixed imports in {fixed_count} files")

if __name__ == "__main__":
    main()
