#!/usr/bin/env python3
"""
RAG Chatbot API Usage Examples

This script demonstrates how to interact with the RAG chatbot API
for data ingestion, chat queries, and system monitoring.
"""

import requests
import json
import time
import asyncio
import aiohttp
from typing import Dict, List, Optional, AsyncGenerator
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class RAGChatbotClient:
    """Client for interacting with the RAG Chatbot API."""
    
    def __init__(self, base_url: str = "http://localhost:8000", admin_token: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.admin_token = admin_token or os.getenv('ADMIN_API_KEY', 'your_admin_token_here')
        self.session = requests.Session()
        
    def _get_headers(self, admin: bool = False) -> Dict[str, str]:
        """Get request headers."""
        headers = {"Content-Type": "application/json"}
        if admin and self.admin_token:
            headers["Authorization"] = f"Bearer {self.admin_token}"
        return headers
    
    def health_check(self) -> Dict:
        """Check API health."""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def admin_health_check(self) -> Dict:
        """Check detailed system health (admin)."""
        response = self.session.get(
            f"{self.base_url}/admin/health",
            headers=self._get_headers(admin=True)
        )
        response.raise_for_status()
        return response.json()
    
    def create_session(self, metadata: Optional[Dict] = None) -> Dict:
        """Create a new chat session."""
        data = {"metadata": metadata or {}}
        response = self.session.post(
            f"{self.base_url}/chat/sessions",
            headers=self._get_headers(),
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def chat_query(self, query: str, session_id: Optional[str] = None, 
                   max_results: int = 5, include_sources: bool = True) -> Dict:
        """Send a chat query and get complete response."""
        data = {
            "query": query,
            "session_id": session_id,
            "max_results": max_results,
            "include_sources": include_sources
        }
        response = self.session.post(
            f"{self.base_url}/chat/query",
            headers=self._get_headers(),
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def add_data_source(self, name: str, source_type: str, url: Optional[str] = None,
                       path: Optional[str] = None, crawl_depth: int = 2,
                       enabled: bool = True, metadata: Optional[Dict] = None) -> Dict:
        """Add a new data source (admin)."""
        data = {
            "name": name,
            "source_type": source_type,
            "url": url,
            "path": path,
            "crawl_depth": crawl_depth,
            "enabled": enabled,
            "metadata": metadata or {}
        }
        response = self.session.post(
            f"{self.base_url}/admin/sources",
            headers=self._get_headers(admin=True),
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def list_data_sources(self) -> List[Dict]:
        """List all data sources (admin)."""
        response = self.session.get(
            f"{self.base_url}/admin/sources",
            headers=self._get_headers(admin=True)
        )
        response.raise_for_status()
        return response.json()
    
    def trigger_reindex(self, source_ids: Optional[List[str]] = None, force: bool = False) -> Dict:
        """Trigger reindexing of data sources (admin)."""
        data = {
            "source_ids": source_ids,
            "force": force
        }
        response = self.session.post(
            f"{self.base_url}/admin/reindex",
            headers=self._get_headers(admin=True),
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def get_metrics(self) -> Dict:
        """Get system metrics (admin)."""
        response = self.session.get(
            f"{self.base_url}/admin/metrics",
            headers=self._get_headers(admin=True)
        )
        response.raise_for_status()
        return response.json()
    
    def stream_chat(self, query: str, session_id: Optional[str] = None):
        """Stream chat response (generator)."""
        data = {
            "query": query,
            "session_id": session_id
        }
        response = self.session.post(
            f"{self.base_url}/chat/stream",
            headers=self._get_headers(),
            json=data,
            stream=True
        )
        response.raise_for_status()
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]  # Remove 'data: ' prefix
                    if data_str == '[DONE]':
                        break
                    try:
                        yield json.loads(data_str)
                    except json.JSONDecodeError:
                        continue


def example_basic_usage():
    """Example: Basic API usage."""
    print("🚀 Basic API Usage Example")
    print("=" * 50)
    
    client = RAGChatbotClient()
    
    # 1. Health check
    print("1. Checking API health...")
    health = client.health_check()
    print(f"   Status: {health.get('status', 'unknown')}")
    
    # 2. Create session
    print("\n2. Creating chat session...")
    session = client.create_session({"user_type": "legal_professional"})
    session_id = session["session_id"]
    print(f"   Session ID: {session_id}")
    
    # 3. Send chat query
    print("\n3. Sending chat query...")
    query = "What are the basic requirements for forming a company?"
    response = client.chat_query(query, session_id=session_id)
    
    print(f"   Query: {query}")
    print(f"   Answer: {response['answer'][:200]}...")
    print(f"   Sources: {len(response.get('sources', []))} found")
    print(f"   Processing time: {response.get('processing_time', 0):.2f}s")


def example_data_ingestion():
    """Example: Data ingestion workflow."""
    print("\n🔄 Data Ingestion Example")
    print("=" * 50)
    
    client = RAGChatbotClient()
    
    # 1. Check current sources
    print("1. Checking current data sources...")
    try:
        sources = client.list_data_sources()
        print(f"   Current sources: {len(sources)}")
        for source in sources[:3]:  # Show first 3
            print(f"   - {source.get('name', 'Unknown')}: {source.get('status', 'unknown')}")
    except requests.exceptions.HTTPError as e:
        print(f"   Error: {e} (Admin token may be required)")
        return
    
    # 2. Add new source
    print("\n2. Adding new data source...")
    try:
        new_source = client.add_data_source(
            name="Example Legal Website",
            source_type="website",
            url="https://www.gesetze-im-internet.de/gg/",
            crawl_depth=1,
            metadata={"category": "constitutional_law", "language": "de"}
        )
        print(f"   Added source: {new_source.get('name')}")
        print(f"   Source ID: {new_source.get('id')}")
    except requests.exceptions.HTTPError as e:
        print(f"   Error adding source: {e}")
    
    # 3. Trigger reindexing
    print("\n3. Triggering reindexing...")
    try:
        reindex_result = client.trigger_reindex()
        print(f"   Reindex task ID: {reindex_result.get('task_id')}")
        print("   Note: Reindexing runs in background. Check logs for progress.")
    except requests.exceptions.HTTPError as e:
        print(f"   Error triggering reindex: {e}")


def example_streaming_chat():
    """Example: Streaming chat response."""
    print("\n💬 Streaming Chat Example")
    print("=" * 50)
    
    client = RAGChatbotClient()
    
    # Create session
    session = client.create_session()
    session_id = session["session_id"]
    
    query = "Explain the concept of intellectual property rights"
    print(f"Query: {query}")
    print("Streaming response:")
    print("-" * 30)
    
    try:
        full_response = ""
        for chunk in client.stream_chat(query, session_id=session_id):
            if chunk.get("type") == "chunk":
                content = chunk.get("content", "")
                print(content, end="", flush=True)
                full_response += content
            elif chunk.get("type") == "sources":
                sources = chunk.get("sources", [])
                print(f"\n\n📚 Sources found: {len(sources)}")
            elif chunk.get("type") == "end":
                print("\n" + "-" * 30)
                print("✅ Streaming complete")
                break
    except Exception as e:
        print(f"\n❌ Streaming error: {e}")


def example_monitoring():
    """Example: System monitoring."""
    print("\n📊 System Monitoring Example")
    print("=" * 50)
    
    client = RAGChatbotClient()
    
    # 1. Basic health
    print("1. Basic health check...")
    health = client.health_check()
    print(f"   Status: {health.get('status')}")
    
    # 2. Detailed health (admin)
    print("\n2. Detailed health check...")
    try:
        detailed_health = client.admin_health_check()
        print(f"   Overall status: {detailed_health.get('status')}")
        components = detailed_health.get('components', {})
        for component, status in components.items():
            print(f"   - {component}: {status}")
    except requests.exceptions.HTTPError as e:
        print(f"   Error: {e} (Admin token required)")
    
    # 3. System metrics
    print("\n3. System metrics...")
    try:
        metrics = client.get_metrics()
        print(f"   Total documents: {metrics.get('total_documents', 'N/A')}")
        print(f"   Total chunks: {metrics.get('total_chunks', 'N/A')}")
        print(f"   Active sources: {metrics.get('active_sources', 'N/A')}")
    except requests.exceptions.HTTPError as e:
        print(f"   Error: {e} (Admin token required)")


def example_test_queries():
    """Example: Test various query types."""
    print("\n🧪 Test Queries Example")
    print("=" * 50)
    
    client = RAGChatbotClient()
    session = client.create_session()
    session_id = session["session_id"]
    
    test_queries = [
        "What is a trademark?",
        "How do I register a company in Germany?",
        "What are the requirements for a valid contract?",
        "Explain copyright law basics",
        "What is the difference between civil and criminal law?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Testing query: '{query}'")
        try:
            response = client.chat_query(query, session_id=session_id, max_results=3)
            answer_preview = response['answer'][:150] + "..." if len(response['answer']) > 150 else response['answer']
            print(f"   Answer: {answer_preview}")
            print(f"   Sources: {len(response.get('sources', []))}")
            print(f"   Time: {response.get('processing_time', 0):.2f}s")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Small delay between queries
        time.sleep(1)


def main():
    """Run all examples."""
    print("🤖 RAG Chatbot API Usage Examples")
    print("=" * 60)
    
    try:
        # Run examples
        example_basic_usage()
        example_data_ingestion()
        example_streaming_chat()
        example_monitoring()
        example_test_queries()
        
        print("\n🎉 All examples completed!")
        print("\n📝 Next steps:")
        print("   1. Configure your data sources in sources.yaml")
        print("   2. Set up your admin API token")
        print("   3. Add your legal content URLs")
        print("   4. Monitor the indexing process")
        print("   5. Test with domain-specific queries")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("   Make sure the API server is running on http://localhost:8000")


if __name__ == "__main__":
    main()
