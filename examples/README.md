# RAG Chatbot API Examples

This directory contains practical examples and scripts for using the RAG Chatbot API.

## 📁 Files

### `api_usage_examples.py`
Comprehensive Python script demonstrating all major API functionality:

- **Basic Usage**: Health checks, session creation, chat queries
- **Data Ingestion**: Adding sources, triggering reindexing
- **Streaming Chat**: Real-time response streaming
- **System Monitoring**: Health checks, metrics, statistics
- **Test Queries**: Various legal query examples

**Usage:**
```bash
python examples/api_usage_examples.py
```

## 🚀 Quick Start

1. **Ensure your system is running:**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **Set up environment variables:**
   ```bash
   # Edit .env file with your API keys
   ADMIN_API_KEY=your_admin_token_here
   GOOGLE_API_KEY=your_google_ai_key
   COHERE_API_KEY=your_cohere_key
   ```

3. **Run the examples:**
   ```bash
   python examples/api_usage_examples.py
   ```

## 📋 Example Outputs

### Basic Chat Query
```json
{
  "answer": "A trademark is a sign capable of distinguishing...",
  "sources": [
    {
      "document_id": "doc_123",
      "title": "Trademark Law Basics",
      "excerpt": "Relevant text excerpt...",
      "score": 0.95
    }
  ],
  "session_id": "session_abc123",
  "processing_time": 1.25
}
```

### Streaming Response
```
Query: Explain intellectual property rights
Streaming response:
------------------------------
Intellectual property rights are legal protections granted to creators and inventors for their original works and innovations. These rights include:

1. **Copyright** - Protects original works of authorship...
2. **Patents** - Protect inventions and discoveries...
3. **Trademarks** - Protect brand names and logos...

📚 Sources found: 5
------------------------------
✅ Streaming complete
```

### System Metrics
```json
{
  "total_documents": 1250,
  "total_chunks": 15600,
  "active_sources": 8,
  "last_update": "2024-12-14T10:30:00Z"
}
```

## 🔧 Customization

### Modify API Base URL
```python
client = RAGChatbotClient(base_url="http://your-server:8000")
```

### Set Admin Token
```python
client = RAGChatbotClient(admin_token="your_secure_token")
```

### Custom Query Parameters
```python
response = client.chat_query(
    query="Your legal question",
    session_id="custom_session",
    max_results=10,
    include_sources=True
)
```

## 🧪 Testing Different Scenarios

### Test Legal Queries
```python
test_queries = [
    "What is a contract?",
    "How to register a trademark?",
    "What are intellectual property rights?",
    "Explain corporate law basics",
    "What is constitutional law?"
]
```

### Test Data Source Management
```python
# Add website source
client.add_data_source(
    name="Legal Resource Site",
    source_type="website",
    url="https://legal-site.com",
    crawl_depth=2
)

# Add PDF source
client.add_data_source(
    name="Legal Document",
    source_type="pdf",
    path="/path/to/document.pdf"
)
```

## 📊 Monitoring Examples

### Health Monitoring
```python
# Basic health
health = client.health_check()
print(f"Status: {health['status']}")

# Detailed health (admin)
detailed = client.admin_health_check()
print(f"Components: {detailed['components']}")
```

### Performance Monitoring
```python
metrics = client.get_metrics()
print(f"Documents: {metrics['total_documents']}")
print(f"Chunks: {metrics['total_chunks']}")
print(f"Sources: {metrics['active_sources']}")
```

## 🚨 Error Handling

The examples include comprehensive error handling:

```python
try:
    response = client.chat_query("Your question")
    print(f"Answer: {response['answer']}")
except requests.exceptions.HTTPError as e:
    print(f"API Error: {e}")
except requests.exceptions.ConnectionError:
    print("Connection failed - is the server running?")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## 📝 Next Steps

1. **Run the examples** to understand the API
2. **Modify the scripts** for your specific use case
3. **Add your own data sources** using the admin API
4. **Test with your domain-specific queries**
5. **Monitor system performance** using the metrics endpoints

## 🔗 Related Files

- `../COMPREHENSIVE_USAGE_GUIDE.md` - Complete usage documentation
- `../test_knowledge_graph.py` - Knowledge graph validation
- `../setup_and_test.py` - Complete setup script
- `../test_infrastructure.py` - Infrastructure testing

## 💡 Tips

- **Start with basic examples** before trying advanced features
- **Check API documentation** at http://localhost:8000/docs
- **Monitor logs** with `docker-compose logs -f api`
- **Use admin endpoints** for data management and monitoring
- **Test incrementally** - add one source at a time
