#!/bin/bash
# Deployment Validation Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="rag-chatbot"
TIMEOUT=300

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment validation prerequisites..."
    
    local required_commands=("kubectl" "curl" "jq" "python3")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        error "Namespace $NAMESPACE does not exist"
    fi
    
    log "Prerequisites check completed"
}

# Validate Kubernetes deployment
validate_kubernetes_deployment() {
    log "Validating Kubernetes deployment..."
    
    local validation_errors=()
    
    # Check if all required deployments exist
    local required_deployments=("rag-chatbot-api" "rag-chatbot-worker" "redis" "milvus-standalone")
    
    for deployment in "${required_deployments[@]}"; do
        if ! kubectl get deployment "$deployment" -n "$NAMESPACE" &> /dev/null; then
            validation_errors+=("Deployment $deployment not found")
        else
            # Check if deployment is ready
            local ready_replicas=$(kubectl get deployment "$deployment" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}')
            local desired_replicas=$(kubectl get deployment "$deployment" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}')
            
            if [[ "$ready_replicas" != "$desired_replicas" ]]; then
                validation_errors+=("Deployment $deployment not ready: $ready_replicas/$desired_replicas")
            fi
        fi
    done
    
    # Check if all required services exist
    local required_services=("rag-chatbot-api-service" "redis-service" "milvus-service")
    
    for service in "${required_services[@]}"; do
        if ! kubectl get service "$service" -n "$NAMESPACE" &> /dev/null; then
            validation_errors+=("Service $service not found")
        fi
    done
    
    # Check if all pods are running
    local pod_count=$(kubectl get pods -n "$NAMESPACE" --field-selector=status.phase=Running --no-headers | wc -l)
    local total_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | wc -l)
    
    if [[ "$pod_count" -lt "$total_pods" ]]; then
        validation_errors+=("Not all pods are running: $pod_count/$total_pods")
    fi
    
    # Check persistent volumes
    local pvc_count=$(kubectl get pvc -n "$NAMESPACE" --no-headers | wc -l)
    if [[ "$pvc_count" -eq 0 ]]; then
        validation_errors+=("No persistent volume claims found")
    fi
    
    if [[ ${#validation_errors[@]} -eq 0 ]]; then
        log "Kubernetes deployment validation passed"
        return 0
    else
        error "Kubernetes deployment validation failed: ${validation_errors[*]}"
        return 1
    fi
}

# Wait for deployment to be ready
wait_for_deployment() {
    log "Waiting for deployment to be ready..."
    
    local deployments=("rag-chatbot-api" "rag-chatbot-worker" "redis" "milvus-standalone")
    
    for deployment in "${deployments[@]}"; do
        log "Waiting for deployment $deployment..."
        if ! kubectl wait --for=condition=available deployment/"$deployment" -n "$NAMESPACE" --timeout="${TIMEOUT}s"; then
            error "Deployment $deployment failed to become ready within $TIMEOUT seconds"
        fi
    done
    
    # Wait for all pods to be ready
    log "Waiting for all pods to be ready..."
    if ! kubectl wait --for=condition=ready pod -l app=rag-legal-chatbot -n "$NAMESPACE" --timeout="${TIMEOUT}s"; then
        error "Pods failed to become ready within $TIMEOUT seconds"
    fi
    
    log "All deployments are ready"
}

# Test API endpoints
test_api_endpoints() {
    log "Testing API endpoints..."
    
    # Get API service endpoint
    local api_endpoint
    if kubectl get service nginx-service -n "$NAMESPACE" &> /dev/null; then
        api_endpoint=$(kubectl get service nginx-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [[ -z "$api_endpoint" ]]; then
            # Use port forwarding
            kubectl port-forward service/nginx-service 8080:80 -n "$NAMESPACE" &
            local port_forward_pid=$!
            sleep 5
            api_endpoint="localhost:8080"
        fi
    else
        # Fallback to API service directly
        kubectl port-forward service/rag-chatbot-api-service 8000:8000 -n "$NAMESPACE" &
        local port_forward_pid=$!
        sleep 5
        api_endpoint="localhost:8000"
    fi
    
    log "Testing API endpoint: http://$api_endpoint"
    
    # Test health endpoint
    if ! curl -f "http://$api_endpoint/health" &> /dev/null; then
        error "Health endpoint is not responding"
    fi
    
    # Test API documentation
    if ! curl -f "http://$api_endpoint/docs" &> /dev/null; then
        warn "API documentation endpoint is not responding"
    fi
    
    # Test metrics endpoint
    if ! curl -f "http://$api_endpoint/metrics" &> /dev/null; then
        warn "Metrics endpoint is not responding"
    fi
    
    # Test chat endpoint
    local chat_response=$(curl -s -X POST "http://$api_endpoint/api/chat" \
        -H "Content-Type: application/json" \
        -d '{"query": "Test query", "session_id": "validation_test"}')
    
    if [[ -z "$chat_response" ]]; then
        error "Chat endpoint is not responding"
    fi
    
    # Check if response is valid JSON
    if ! echo "$chat_response" | jq . &> /dev/null; then
        error "Chat endpoint returned invalid JSON"
    fi
    
    # Cleanup port forwarding
    if [[ -n "${port_forward_pid:-}" ]]; then
        kill $port_forward_pid 2>/dev/null || true
    fi
    
    log "API endpoint tests passed"
}

# Test database connectivity
test_database_connectivity() {
    log "Testing database connectivity..."
    
    # Test Redis connectivity
    local redis_pod=$(kubectl get pods -n "$NAMESPACE" -l component=redis -o jsonpath='{.items[0].metadata.name}')
    if [[ -n "$redis_pod" ]]; then
        if ! kubectl exec -n "$NAMESPACE" "$redis_pod" -- redis-cli ping | grep -q "PONG"; then
            error "Redis is not responding"
        fi
        log "Redis connectivity test passed"
    else
        warn "Redis pod not found"
    fi
    
    # Test Milvus connectivity
    local milvus_pod=$(kubectl get pods -n "$NAMESPACE" -l component=milvus -o jsonpath='{.items[0].metadata.name}')
    if [[ -n "$milvus_pod" ]]; then
        if ! kubectl exec -n "$NAMESPACE" "$milvus_pod" -- curl -f http://localhost:9091/healthz &> /dev/null; then
            error "Milvus is not responding"
        fi
        log "Milvus connectivity test passed"
    else
        warn "Milvus pod not found"
    fi
}

# Test auto-scaling configuration
test_autoscaling() {
    log "Testing auto-scaling configuration..."
    
    # Check if HPA exists
    if kubectl get hpa -n "$NAMESPACE" &> /dev/null; then
        local hpa_count=$(kubectl get hpa -n "$NAMESPACE" --no-headers | wc -l)
        log "Found $hpa_count HPA configurations"
        
        # Check HPA status
        kubectl get hpa -n "$NAMESPACE"
    else
        warn "No HPA configurations found"
    fi
    
    # Check if VPA exists (if using VPA)
    if kubectl get vpa -n "$NAMESPACE" &> /dev/null 2>&1; then
        local vpa_count=$(kubectl get vpa -n "$NAMESPACE" --no-headers | wc -l)
        log "Found $vpa_count VPA configurations"
    fi
}

# Test monitoring integration
test_monitoring() {
    log "Testing monitoring integration..."
    
    # Check if monitoring namespace exists
    if kubectl get namespace monitoring &> /dev/null; then
        log "Monitoring namespace found"
        
        # Check Prometheus
        if kubectl get deployment prometheus -n monitoring &> /dev/null; then
            log "Prometheus deployment found"
        else
            warn "Prometheus deployment not found"
        fi
        
        # Check Grafana
        if kubectl get deployment grafana -n monitoring &> /dev/null; then
            log "Grafana deployment found"
        else
            warn "Grafana deployment not found"
        fi
        
        # Check AlertManager
        if kubectl get deployment alertmanager -n monitoring &> /dev/null; then
            log "AlertManager deployment found"
        else
            warn "AlertManager deployment not found"
        fi
    else
        warn "Monitoring namespace not found"
    fi
}

# Test security configuration
test_security() {
    log "Testing security configuration..."
    
    # Check network policies
    local netpol_count=$(kubectl get networkpolicy -n "$NAMESPACE" --no-headers | wc -l)
    if [[ "$netpol_count" -gt 0 ]]; then
        log "Found $netpol_count network policies"
    else
        warn "No network policies found"
    fi
    
    # Check pod security policies
    if kubectl get psp &> /dev/null 2>&1; then
        local psp_count=$(kubectl get psp --no-headers | wc -l)
        log "Found $psp_count pod security policies"
    fi
    
    # Check RBAC
    local role_count=$(kubectl get role -n "$NAMESPACE" --no-headers | wc -l)
    local rolebinding_count=$(kubectl get rolebinding -n "$NAMESPACE" --no-headers | wc -l)
    
    if [[ "$role_count" -gt 0 && "$rolebinding_count" -gt 0 ]]; then
        log "RBAC configuration found: $role_count roles, $rolebinding_count role bindings"
    else
        warn "RBAC configuration incomplete"
    fi
    
    # Check secrets
    local secret_count=$(kubectl get secrets -n "$NAMESPACE" --no-headers | wc -l)
    if [[ "$secret_count" -gt 0 ]]; then
        log "Found $secret_count secrets"
    else
        warn "No secrets found"
    fi
}

# Run performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    # Install Python dependencies if needed
    pip3 install --user requests pytest &> /dev/null || true
    
    # Run Python validation script
    if [[ -f "$PROJECT_ROOT/tests/deployment/test_production_deployment.py" ]]; then
        python3 "$PROJECT_ROOT/tests/deployment/test_production_deployment.py" \
            --base-url "http://localhost:8000" \
            --namespace "$NAMESPACE" \
            --output "$PROJECT_ROOT/validation-results.json"
        
        if [[ $? -eq 0 ]]; then
            log "Performance tests passed"
        else
            error "Performance tests failed"
        fi
    else
        warn "Performance test script not found"
    fi
}

# Generate validation report
generate_validation_report() {
    log "Generating validation report..."
    
    local report_file="$PROJECT_ROOT/deployment-validation-report-$(date +%Y%m%d_%H%M%S).md"
    
    {
        echo "# Deployment Validation Report"
        echo "Generated: $(date)"
        echo ""
        
        echo "## Kubernetes Deployment Status"
        echo "### Deployments"
        kubectl get deployments -n "$NAMESPACE"
        echo ""
        
        echo "### Pods"
        kubectl get pods -n "$NAMESPACE"
        echo ""
        
        echo "### Services"
        kubectl get services -n "$NAMESPACE"
        echo ""
        
        echo "### Persistent Volume Claims"
        kubectl get pvc -n "$NAMESPACE"
        echo ""
        
        echo "## Resource Usage"
        kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "Metrics server not available"
        echo ""
        
        echo "## Auto-scaling Configuration"
        kubectl get hpa -n "$NAMESPACE" 2>/dev/null || echo "No HPA found"
        echo ""
        
        echo "## Security Configuration"
        echo "### Network Policies"
        kubectl get networkpolicy -n "$NAMESPACE" 2>/dev/null || echo "No network policies found"
        echo ""
        
        echo "### RBAC"
        kubectl get role,rolebinding -n "$NAMESPACE" 2>/dev/null || echo "No RBAC found"
        echo ""
        
        echo "## Monitoring Integration"
        kubectl get pods -n monitoring 2>/dev/null || echo "Monitoring namespace not found"
        echo ""
        
        echo "## Validation Summary"
        echo "- Kubernetes deployment: ✓ Validated"
        echo "- API endpoints: ✓ Tested"
        echo "- Database connectivity: ✓ Verified"
        echo "- Security configuration: ✓ Checked"
        echo "- Monitoring integration: ✓ Validated"
        echo ""
        
        echo "## Next Steps"
        echo "1. Monitor system performance for 24 hours"
        echo "2. Run load tests to validate scalability"
        echo "3. Test disaster recovery procedures"
        echo "4. Verify backup and restore functionality"
        echo "5. Conduct security penetration testing"
        
    } > "$report_file"
    
    log "Validation report generated: $report_file"
}

# Comprehensive validation
run_comprehensive_validation() {
    log "Starting comprehensive deployment validation..."
    
    check_prerequisites
    validate_kubernetes_deployment
    wait_for_deployment
    test_api_endpoints
    test_database_connectivity
    test_autoscaling
    test_monitoring
    test_security
    run_performance_tests
    generate_validation_report
    
    log "Comprehensive deployment validation completed successfully!"
    
    info "Deployment is ready for production use"
    echo ""
    echo "Summary:"
    echo "✓ Kubernetes deployment validated"
    echo "✓ API endpoints tested"
    echo "✓ Database connectivity verified"
    echo "✓ Auto-scaling configured"
    echo "✓ Monitoring integrated"
    echo "✓ Security hardened"
    echo "✓ Performance validated"
    echo ""
    echo "The RAG Legal Chatbot is production-ready!"
}

# Quick validation
run_quick_validation() {
    log "Running quick deployment validation..."
    
    check_prerequisites
    validate_kubernetes_deployment
    test_api_endpoints
    
    log "Quick validation completed"
}

# Script usage
usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  comprehensive  - Run comprehensive validation (default)"
    echo "  quick         - Run quick validation"
    echo "  k8s           - Validate Kubernetes deployment only"
    echo "  api           - Test API endpoints only"
    echo "  database      - Test database connectivity only"
    echo "  performance   - Run performance tests only"
    echo "  report        - Generate validation report only"
    echo ""
    echo "Options:"
    echo "  --namespace   - Kubernetes namespace (default: rag-chatbot)"
    echo "  --timeout     - Timeout in seconds (default: 300)"
    echo ""
    echo "Examples:"
    echo "  $0 comprehensive"
    echo "  $0 quick"
    echo "  $0 k8s --namespace my-namespace"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            COMMAND="$1"
            shift
            ;;
    esac
done

# Main script logic
case "${COMMAND:-comprehensive}" in
    comprehensive)
        run_comprehensive_validation
        ;;
    quick)
        run_quick_validation
        ;;
    k8s)
        check_prerequisites
        validate_kubernetes_deployment
        ;;
    api)
        check_prerequisites
        test_api_endpoints
        ;;
    database)
        check_prerequisites
        test_database_connectivity
        ;;
    performance)
        check_prerequisites
        run_performance_tests
        ;;
    report)
        check_prerequisites
        generate_validation_report
        ;;
    *)
        usage
        exit 1
        ;;
esac
