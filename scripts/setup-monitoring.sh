#!/bin/bash
# Monitoring Setup Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITORING_DIR="$PROJECT_ROOT/monitoring"
COMPOSE_FILE="$MONITORING_DIR/docker-compose.monitoring.yml"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check required commands
    local required_commands=("docker" "docker-compose" "curl")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
    fi
    
    log "Prerequisites check completed"
}

# Create monitoring directories
create_directories() {
    log "Creating monitoring directories..."
    
    local directories=(
        "$MONITORING_DIR/prometheus/data"
        "$MONITORING_DIR/grafana/data"
        "$MONITORING_DIR/grafana/dashboards/system"
        "$MONITORING_DIR/grafana/dashboards/application"
        "$MONITORING_DIR/grafana/dashboards/database"
        "$MONITORING_DIR/grafana/dashboards/business"
        "$MONITORING_DIR/alertmanager/data"
        "$MONITORING_DIR/loki/data"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        chmod 755 "$dir"
    done
    
    # Set proper permissions for Grafana
    sudo chown -R 472:472 "$MONITORING_DIR/grafana/data" 2>/dev/null || true
    
    log "Monitoring directories created"
}

# Generate Grafana dashboards
generate_dashboards() {
    log "Generating Grafana dashboards..."
    
    # System Overview Dashboard
    cat > "$MONITORING_DIR/grafana/dashboards/system/system-overview.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "System Overview",
    "tags": ["system", "overview"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "CPU Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 90}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Memory Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 80},
                {"color": "red", "value": 95}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "30s"
  }
}
EOF

    # Application Dashboard
    cat > "$MONITORING_DIR/grafana/dashboards/application/api-metrics.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "RAG Chatbot API Metrics",
    "tags": ["application", "api"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"rag-chatbot-api\"}[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ],
        "yAxes": [
          {"label": "Requests/sec", "min": 0}
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"rag-chatbot-api\"}[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"rag-chatbot-api\"}[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {"label": "Seconds", "min": 0}
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "30s"
  }
}
EOF

    log "Grafana dashboards generated"
}

# Setup monitoring stack
setup_monitoring() {
    log "Setting up monitoring stack..."
    
    cd "$PROJECT_ROOT"
    
    # Start monitoring services
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log "Waiting for monitoring services to be ready..."
    
    local services=("prometheus:9090" "grafana:3000" "alertmanager:9093" "loki:3100")
    
    for service in "${services[@]}"; do
        local host_port=(${service//:/ })
        local host=${host_port[0]}
        local port=${host_port[1]}
        
        local retries=30
        while [[ $retries -gt 0 ]]; do
            if curl -s "http://localhost:$port" > /dev/null 2>&1; then
                log "$host is ready"
                break
            fi
            retries=$((retries - 1))
            sleep 10
        done
        
        if [[ $retries -eq 0 ]]; then
            error "$host failed to start"
        fi
    done
    
    log "Monitoring stack setup completed"
}

# Configure Grafana
configure_grafana() {
    log "Configuring Grafana..."
    
    # Wait for Grafana to be fully ready
    sleep 30
    
    # Import additional dashboards
    local dashboard_urls=(
        "https://grafana.com/api/dashboards/1860/revisions/27/download"  # Node Exporter Full
        "https://grafana.com/api/dashboards/763/revisions/1/download"    # Redis Dashboard
        "https://grafana.com/api/dashboards/893/revisions/1/download"    # Docker and system monitoring
    )
    
    for url in "${dashboard_urls[@]}"; do
        local dashboard_file="/tmp/dashboard_$(date +%s).json"
        if curl -s "$url" -o "$dashboard_file"; then
            # Import dashboard via Grafana API
            curl -X POST \
                -H "Content-Type: application/json" \
                -d @"$dashboard_file" \
                "*********************************/api/dashboards/db" \
                > /dev/null 2>&1 || warn "Failed to import dashboard from $url"
            rm -f "$dashboard_file"
        fi
    done
    
    log "Grafana configuration completed"
}

# Setup alerting
setup_alerting() {
    log "Setting up alerting..."
    
    # Test AlertManager configuration
    if curl -s "http://localhost:9093/-/healthy" > /dev/null; then
        log "AlertManager is healthy"
    else
        warn "AlertManager health check failed"
    fi
    
    # Test alert rules
    if curl -s "http://localhost:9090/api/v1/rules" | grep -q "groups"; then
        log "Alert rules loaded successfully"
    else
        warn "Alert rules not loaded properly"
    fi
    
    log "Alerting setup completed"
}

# Generate monitoring documentation
generate_documentation() {
    log "Generating monitoring documentation..."
    
    cat > "$MONITORING_DIR/README.md" << 'EOF'
# RAG Legal Chatbot Monitoring

This directory contains the complete monitoring setup for the RAG Legal Chatbot system.

## Services

- **Prometheus** (http://localhost:9090): Metrics collection and alerting
- **Grafana** (http://localhost:3000): Visualization and dashboards (admin/admin)
- **AlertManager** (http://localhost:9093): Alert routing and notifications
- **Loki** (http://localhost:3100): Log aggregation
- **Jaeger** (http://localhost:16686): Distributed tracing

## Quick Start

```bash
# Start monitoring stack
docker-compose -f monitoring/docker-compose.monitoring.yml up -d

# Stop monitoring stack
docker-compose -f monitoring/docker-compose.monitoring.yml down

# View logs
docker-compose -f monitoring/docker-compose.monitoring.yml logs -f
```

## Configuration

- Prometheus: `prometheus/prometheus.yml`
- Alert Rules: `prometheus/alert.rules.yml`
- AlertManager: `alertmanager/alertmanager.yml`
- Grafana Datasources: `grafana/provisioning/datasources/`
- Grafana Dashboards: `grafana/provisioning/dashboards/`

## Dashboards

- System Overview: CPU, Memory, Disk usage
- API Metrics: Request rate, response time, error rate
- Database Metrics: Redis and Milvus performance
- Business Metrics: Query success rate, user activity

## Alerts

- Critical: Service down, high error rate
- Warning: High resource usage, slow response
- Security: Failed logins, suspicious activity
- Business: Low success rate, no queries

## Troubleshooting

1. Check service status: `docker-compose ps`
2. View logs: `docker-compose logs [service]`
3. Test connectivity: `curl http://localhost:[port]/health`
4. Validate config: `promtool check config prometheus.yml`
EOF

    log "Monitoring documentation generated"
}

# Validate monitoring setup
validate_monitoring() {
    log "Validating monitoring setup..."
    
    local validation_errors=()
    
    # Check Prometheus targets
    local prometheus_targets=$(curl -s "http://localhost:9090/api/v1/targets" | grep -o '"health":"[^"]*"' | grep -c '"health":"up"' || echo "0")
    if [[ $prometheus_targets -lt 3 ]]; then
        validation_errors+=("Prometheus has fewer than 3 healthy targets")
    fi
    
    # Check Grafana datasources
    local grafana_datasources=$(curl -s -u admin:admin "http://localhost:3000/api/datasources" | grep -c '"name"' || echo "0")
    if [[ $grafana_datasources -lt 2 ]]; then
        validation_errors+=("Grafana has fewer than 2 datasources")
    fi
    
    # Check AlertManager
    if ! curl -s "http://localhost:9093/-/healthy" > /dev/null; then
        validation_errors+=("AlertManager is not healthy")
    fi
    
    # Check Loki
    if ! curl -s "http://localhost:3100/ready" > /dev/null; then
        validation_errors+=("Loki is not ready")
    fi
    
    if [[ ${#validation_errors[@]} -eq 0 ]]; then
        log "Monitoring validation passed"
        return 0
    else
        error "Monitoring validation failed: ${validation_errors[*]}"
        return 1
    fi
}

# Show monitoring status
show_status() {
    log "Monitoring Stack Status"
    echo "========================"
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    echo "Service URLs:"
    echo "- Prometheus: http://localhost:9090"
    echo "- Grafana: http://localhost:3000 (admin/admin)"
    echo "- AlertManager: http://localhost:9093"
    echo "- Loki: http://localhost:3100"
    echo "- Jaeger: http://localhost:16686"
    echo ""
    
    echo "Health Checks:"
    local services=("prometheus:9090" "grafana:3000" "alertmanager:9093" "loki:3100")
    
    for service in "${services[@]}"; do
        local host_port=(${service//:/ })
        local host=${host_port[0]}
        local port=${host_port[1]}
        
        if curl -s "http://localhost:$port" > /dev/null 2>&1; then
            echo "✓ $host is healthy"
        else
            echo "✗ $host is not responding"
        fi
    done
}

# Cleanup monitoring
cleanup_monitoring() {
    log "Cleaning up monitoring stack..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" down -v
    
    # Remove data volumes (optional)
    read -p "Remove monitoring data volumes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume rm monitoring_prometheus_data monitoring_grafana_data monitoring_alertmanager_data monitoring_loki_data 2>/dev/null || true
        log "Monitoring data volumes removed"
    fi
    
    log "Monitoring cleanup completed"
}

# Main setup function
setup() {
    log "Starting monitoring setup..."
    
    check_prerequisites
    create_directories
    generate_dashboards
    setup_monitoring
    configure_grafana
    setup_alerting
    generate_documentation
    validate_monitoring
    show_status
    
    log "Monitoring setup completed successfully!"
}

# Script usage
usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup complete monitoring stack"
    echo "  status    - Show monitoring stack status"
    echo "  validate  - Validate monitoring configuration"
    echo "  cleanup   - Cleanup monitoring stack"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 status"
}

# Main script logic
case "${1:-setup}" in
    setup)
        setup
        ;;
    status)
        show_status
        ;;
    validate)
        validate_monitoring
        ;;
    cleanup)
        cleanup_monitoring
        ;;
    *)
        usage
        exit 1
        ;;
esac
