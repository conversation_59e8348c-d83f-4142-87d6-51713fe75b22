#!/bin/bash
# Production Environment Setup Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="rag-legal-chatbot"
ENVIRONMENT="production"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check required commands
    local required_commands=("docker" "docker-compose" "openssl" "curl" "jq")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
    fi
    
    # Check Docker Compose version
    local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    local required_version="1.29.0"
    
    if ! printf '%s\n%s\n' "$required_version" "$compose_version" | sort -V -C; then
        warn "Docker Compose version $compose_version is older than recommended $required_version"
    fi
    
    log "Prerequisites check completed"
}

# Generate secure secrets
generate_secrets() {
    log "Generating secure secrets..."
    
    # Generate secret key (32 bytes, base64 encoded)
    SECRET_KEY=$(openssl rand -base64 32)
    
    # Generate admin API key (24 bytes, base64 encoded)
    ADMIN_API_KEY=$(openssl rand -base64 24)
    
    # Generate Redis password (16 bytes, hex)
    REDIS_PASSWORD=$(openssl rand -hex 16)
    
    # Generate MinIO credentials
    MINIO_ACCESS_KEY=$(openssl rand -base64 12 | tr -d '/' | tr '+' '-')
    MINIO_SECRET_KEY=$(openssl rand -base64 24 | tr -d '/' | tr '+' '-')
    
    log "Secrets generated successfully"
}

# Create production environment file
create_env_file() {
    log "Creating production environment file..."
    
    local env_file="$PROJECT_ROOT/.env"
    
    if [[ -f "$env_file" ]]; then
        warn "Environment file already exists. Creating backup..."
        cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Copy template and update with generated secrets
    cp "$PROJECT_ROOT/.env.production" "$env_file"
    
    # Update secrets in the file
    sed -i.bak \
        -e "s/your_super_secret_key_change_this_in_production_min_32_chars/$SECRET_KEY/g" \
        -e "s/your_admin_api_key_for_admin_endpoints/$ADMIN_API_KEY/g" \
        -e "s/REDIS_PASSWORD=/REDIS_PASSWORD=$REDIS_PASSWORD/g" \
        -e "s/MINIO_ACCESS_KEY=minioadmin/MINIO_ACCESS_KEY=$MINIO_ACCESS_KEY/g" \
        -e "s/MINIO_SECRET_KEY=minioadmin/MINIO_SECRET_KEY=$MINIO_SECRET_KEY/g" \
        "$env_file"
    
    # Remove backup file
    rm "$env_file.bak"
    
    # Set secure permissions
    chmod 600 "$env_file"
    
    log "Environment file created: $env_file"
}

# Create SSL certificates
create_ssl_certificates() {
    log "Creating SSL certificates..."
    
    local ssl_dir="$PROJECT_ROOT/chatbot-engine/docker/ssl"
    mkdir -p "$ssl_dir"
    
    # Generate self-signed certificate for development/testing
    if [[ ! -f "$ssl_dir/cert.pem" ]] || [[ ! -f "$ssl_dir/key.pem" ]]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$ssl_dir/key.pem" \
            -out "$ssl_dir/cert.pem" \
            -subj "/C=DE/ST=State/L=City/O=Organization/OU=OrgUnit/CN=rag-chatbot.local"
        
        # Set secure permissions
        chmod 600 "$ssl_dir/key.pem"
        chmod 644 "$ssl_dir/cert.pem"
        
        log "Self-signed SSL certificates created"
        warn "For production, replace with certificates from a trusted CA"
    else
        log "SSL certificates already exist"
    fi
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    local directories=(
        "$PROJECT_ROOT/chatbot-engine/logs"
        "$PROJECT_ROOT/chatbot-engine/data"
        "$PROJECT_ROOT/chatbot-engine/data/uploads"
        "$PROJECT_ROOT/chatbot-engine/backups"
        "$PROJECT_ROOT/monitoring/prometheus/data"
        "$PROJECT_ROOT/monitoring/grafana/data"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        # Set appropriate permissions
        chmod 755 "$dir"
    done
    
    log "Directories created successfully"
}

# Setup Docker networks
setup_docker_networks() {
    log "Setting up Docker networks..."
    
    # Create custom network for better isolation
    if ! docker network ls | grep -q "${PROJECT_NAME}-network"; then
        docker network create "${PROJECT_NAME}-network" \
            --driver bridge \
            --subnet=**********/16 \
            --ip-range=************/20
        log "Docker network created: ${PROJECT_NAME}-network"
    else
        log "Docker network already exists: ${PROJECT_NAME}-network"
    fi
}

# Pull Docker images
pull_docker_images() {
    log "Pulling Docker images..."
    
    # Pull base images
    docker pull python:3.11-slim
    docker pull redis:7-alpine
    docker pull nginx:alpine
    docker pull milvusdb/milvus:v2.3.0
    docker pull quay.io/coreos/etcd:v3.5.5
    docker pull minio/minio:RELEASE.2023-03-20T20-16-18Z
    
    log "Docker images pulled successfully"
}

# Build application images
build_application_images() {
    log "Building application images..."
    
    cd "$PROJECT_ROOT"
    
    # Build API image
    docker build -f chatbot-engine/docker/Dockerfile.api -t rag-chatbot-api:latest .
    
    # Build Worker image
    docker build -f chatbot-engine/docker/Dockerfile.worker -t rag-chatbot-worker:latest .
    
    log "Application images built successfully"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    local monitoring_dir="$PROJECT_ROOT/monitoring"
    mkdir -p "$monitoring_dir"
    
    # Create Prometheus configuration
    cat > "$monitoring_dir/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert.rules.yml"

scrape_configs:
  - job_name: 'rag-chatbot-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'milvus'
    static_configs:
      - targets: ['milvus:9091']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
EOF

    # Create alert rules
    cat > "$monitoring_dir/alert.rules.yml" << EOF
groups:
  - name: rag-chatbot-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High error rate detected

      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High memory usage detected

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Service is down
EOF

    log "Monitoring configuration created"
}

# Setup log rotation
setup_log_rotation() {
    log "Setting up log rotation..."
    
    local logrotate_config="/etc/logrotate.d/rag-chatbot"
    
    # Create logrotate configuration (requires sudo)
    if command -v sudo &> /dev/null; then
        sudo tee "$logrotate_config" > /dev/null << EOF
$PROJECT_ROOT/chatbot-engine/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        docker-compose -f $PROJECT_ROOT/docker-compose.prod.yml restart api worker
    endscript
}
EOF
        log "Log rotation configured"
    else
        warn "sudo not available. Log rotation not configured."
    fi
}

# Setup backup script
setup_backup() {
    log "Setting up backup script..."
    
    local backup_script="$PROJECT_ROOT/scripts/backup.sh"
    
    cat > "$backup_script" << 'EOF'
#!/bin/bash
# Backup script for RAG Legal Chatbot

BACKUP_DIR="/app/chatbot-engine/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="rag-chatbot-backup-$DATE"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup Redis data
docker exec rag-redis-prod redis-cli BGSAVE
docker cp rag-redis-prod:/data/dump.rdb "$BACKUP_DIR/$BACKUP_NAME/"

# Backup Milvus data
docker exec rag-milvus-standalone-prod tar czf /tmp/milvus-backup.tar.gz /var/lib/milvus
docker cp rag-milvus-standalone-prod:/tmp/milvus-backup.tar.gz "$BACKUP_DIR/$BACKUP_NAME/"

# Backup application data
tar czf "$BACKUP_DIR/$BACKUP_NAME/app-data.tar.gz" -C /app/chatbot-engine data

# Backup configuration
tar czf "$BACKUP_DIR/$BACKUP_NAME/config.tar.gz" -C /app/chatbot-engine config

# Create backup manifest
cat > "$BACKUP_DIR/$BACKUP_NAME/manifest.txt" << EOL
Backup created: $(date)
Redis data: dump.rdb
Milvus data: milvus-backup.tar.gz
Application data: app-data.tar.gz
Configuration: config.tar.gz
EOL

# Cleanup old backups (keep last 30 days)
find "$BACKUP_DIR" -type d -name "rag-chatbot-backup-*" -mtime +30 -exec rm -rf {} \;

echo "Backup completed: $BACKUP_NAME"
EOF

    chmod +x "$backup_script"
    log "Backup script created: $backup_script"
}

# Validate configuration
validate_configuration() {
    log "Validating configuration..."
    
    local env_file="$PROJECT_ROOT/.env"
    
    # Check if required environment variables are set
    local required_vars=(
        "GOOGLE_API_KEY"
        "COHERE_API_KEY"
        "SECRET_KEY"
        "ADMIN_API_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file" || grep -q "^$var=your_" "$env_file"; then
            error "Required environment variable $var is not properly set in $env_file"
        fi
    done
    
    log "Configuration validation completed"
}

# Main setup function
setup_production() {
    log "Starting production environment setup for $PROJECT_NAME..."
    
    check_root
    check_prerequisites
    generate_secrets
    create_env_file
    create_ssl_certificates
    create_directories
    setup_docker_networks
    pull_docker_images
    build_application_images
    setup_monitoring
    setup_log_rotation
    setup_backup
    validate_configuration
    
    log "Production environment setup completed successfully!"
    
    info "Next steps:"
    echo "1. Review and update .env file with your actual API keys"
    echo "2. Replace SSL certificates with production certificates"
    echo "3. Update domain names in configuration files"
    echo "4. Run: docker-compose -f docker-compose.prod.yml up -d"
    echo "5. Verify deployment: curl http://localhost/health"
}

# Cleanup function
cleanup_production() {
    log "Cleaning up production environment..."
    
    # Stop and remove containers
    cd "$PROJECT_ROOT"
    docker-compose -f docker-compose.prod.yml down -v
    
    # Remove Docker network
    docker network rm "${PROJECT_NAME}-network" 2>/dev/null || true
    
    # Remove generated files (keep backups)
    rm -f .env
    rm -rf chatbot-engine/docker/ssl
    
    log "Cleanup completed"
}

# Script usage
usage() {
    echo "Usage: $0 [setup|cleanup|validate]"
    echo ""
    echo "Commands:"
    echo "  setup    - Setup production environment"
    echo "  cleanup  - Cleanup production environment"
    echo "  validate - Validate current configuration"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 cleanup"
}

# Main script logic
case "${1:-setup}" in
    setup)
        setup_production
        ;;
    cleanup)
        cleanup_production
        ;;
    validate)
        validate_configuration
        ;;
    *)
        usage
        exit 1
        ;;
esac
