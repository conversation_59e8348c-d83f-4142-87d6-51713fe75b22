#!/bin/bash
# Backup and Restore Management Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_ROOT/backup"
NAMESPACE="rag-chatbot"
S3_BUCKET="rag-chatbot-backups"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking backup prerequisites..."
    
    local required_commands=("kubectl" "aws" "velero" "jq")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS credentials not configured"
    fi
    
    log "Prerequisites check completed"
}

# Setup backup infrastructure
setup_backup_infrastructure() {
    log "Setting up backup infrastructure..."
    
    # Install Velero if not present
    if ! kubectl get namespace velero &> /dev/null; then
        log "Installing Velero..."
        
        # Create Velero namespace
        kubectl create namespace velero
        
        # Install Velero with AWS plugin
        velero install \
            --provider aws \
            --plugins velero/velero-plugin-for-aws:v1.8.0 \
            --bucket "$S3_BUCKET" \
            --backup-location-config region=us-west-2 \
            --snapshot-location-config region=us-west-2 \
            --secret-file ~/.aws/credentials
    fi
    
    # Apply backup configurations
    kubectl apply -f "$BACKUP_DIR/backup-strategy.yaml"
    kubectl apply -f "$BACKUP_DIR/disaster-recovery.yaml"
    
    log "Backup infrastructure setup completed"
}

# Create manual backup
create_backup() {
    local backup_name="${1:-manual-backup-$(date +%Y%m%d_%H%M%S)}"
    
    log "Creating backup: $backup_name"
    
    # Create Velero backup
    velero backup create "$backup_name" \
        --include-namespaces "$NAMESPACE" \
        --wait
    
    # Trigger database backups
    kubectl create job --from=cronjob/milvus-backup "milvus-backup-$(date +%s)" -n "$NAMESPACE"
    kubectl create job --from=cronjob/redis-backup "redis-backup-$(date +%s)" -n "$NAMESPACE"
    
    # Wait for database backup jobs to complete
    log "Waiting for database backups to complete..."
    kubectl wait --for=condition=complete job -l job-name --timeout=600s -n "$NAMESPACE"
    
    log "Backup created successfully: $backup_name"
}

# List available backups
list_backups() {
    log "Available backups:"
    
    echo ""
    echo "Velero Backups:"
    velero backup get
    
    echo ""
    echo "Milvus Backups:"
    aws s3 ls "s3://$S3_BUCKET/milvus-backups/" | tail -10
    
    echo ""
    echo "Redis Backups:"
    aws s3 ls "s3://$S3_BUCKET/redis-backups/" | tail -10
}

# Restore from backup
restore_backup() {
    local backup_name="$1"
    local target_namespace="${2:-$NAMESPACE-restore}"
    
    if [[ -z "$backup_name" ]]; then
        error "Backup name is required"
    fi
    
    log "Restoring from backup: $backup_name to namespace: $target_namespace"
    
    # Create target namespace
    kubectl create namespace "$target_namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # Restore Velero backup
    velero restore create "restore-$(date +%s)" \
        --from-backup "$backup_name" \
        --namespace-mappings "$NAMESPACE:$target_namespace" \
        --wait
    
    # Restore databases
    restore_databases "$target_namespace"
    
    log "Restore completed successfully"
}

# Restore databases
restore_databases() {
    local target_namespace="$1"
    
    log "Restoring databases to namespace: $target_namespace"
    
    # Get latest database backups
    local latest_milvus=$(aws s3 ls "s3://$S3_BUCKET/milvus-backups/" | sort | tail -n 1 | awk '{print $4}')
    local latest_redis=$(aws s3 ls "s3://$S3_BUCKET/redis-backups/" | sort | tail -n 1 | awk '{print $4}')
    
    # Restore Milvus
    if [[ -n "$latest_milvus" ]]; then
        log "Restoring Milvus from: $latest_milvus"
        
        # Download and extract backup
        aws s3 cp "s3://$S3_BUCKET/milvus-backups/$latest_milvus" /tmp/
        tar -xzf "/tmp/$latest_milvus" -C /tmp/
        
        # Create restore job
        cat << EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: milvus-restore-$(date +%s)
  namespace: $target_namespace
spec:
  template:
    spec:
      containers:
      - name: milvus-restore
        image: milvusdb/milvus:v2.3.0
        command:
        - /bin/sh
        - -c
        - |
          # Restore Milvus data
          echo "Restoring Milvus data..."
          # Implementation depends on backup format
          echo "Milvus restore completed"
        volumeMounts:
        - name: backup-data
          mountPath: /backup
      volumes:
      - name: backup-data
        emptyDir: {}
      restartPolicy: Never
EOF
    fi
    
    # Restore Redis
    if [[ -n "$latest_redis" ]]; then
        log "Restoring Redis from: $latest_redis"
        
        # Download and extract backup
        aws s3 cp "s3://$S3_BUCKET/redis-backups/$latest_redis" /tmp/
        tar -xzf "/tmp/$latest_redis" -C /tmp/
        
        # Copy RDB file to Redis pod
        local redis_pod=$(kubectl get pods -n "$target_namespace" -l component=redis -o jsonpath='{.items[0].metadata.name}')
        if [[ -n "$redis_pod" ]]; then
            kubectl cp "/tmp/$(basename $latest_redis .tar.gz)/dump.rdb" "$target_namespace/$redis_pod:/data/dump.rdb"
            kubectl exec -n "$target_namespace" "$redis_pod" -- redis-cli DEBUG RESTART
        fi
    fi
    
    log "Database restore completed"
}

# Verify backup integrity
verify_backup() {
    local backup_name="$1"
    
    if [[ -z "$backup_name" ]]; then
        error "Backup name is required"
    fi
    
    log "Verifying backup: $backup_name"
    
    # Check Velero backup status
    local backup_status=$(velero backup describe "$backup_name" --details | grep "Phase:" | awk '{print $2}')
    
    if [[ "$backup_status" != "Completed" ]]; then
        error "Backup $backup_name is not in Completed state: $backup_status"
    fi
    
    # Verify backup files exist
    local backup_date=$(echo "$backup_name" | grep -o '[0-9]\{8\}')
    if [[ -n "$backup_date" ]]; then
        local milvus_backup_count=$(aws s3 ls "s3://$S3_BUCKET/milvus-backups/" | grep "$backup_date" | wc -l)
        local redis_backup_count=$(aws s3 ls "s3://$S3_BUCKET/redis-backups/" | grep "$backup_date" | wc -l)
        
        if [[ "$milvus_backup_count" -eq 0 ]]; then
            warn "No Milvus backup found for date: $backup_date"
        fi
        
        if [[ "$redis_backup_count" -eq 0 ]]; then
            warn "No Redis backup found for date: $backup_date"
        fi
    fi
    
    log "Backup verification completed"
}

# Test disaster recovery
test_disaster_recovery() {
    log "Starting disaster recovery test..."
    
    local test_namespace="dr-test-$(date +%s)"
    
    # Get latest backup
    local latest_backup=$(velero backup get --output json | jq -r '.items[0].metadata.name')
    
    if [[ -z "$latest_backup" || "$latest_backup" == "null" ]]; then
        error "No backups found for testing"
    fi
    
    log "Testing with backup: $latest_backup"
    
    # Perform test restore
    restore_backup "$latest_backup" "$test_namespace"
    
    # Verify restored application
    log "Verifying restored application..."
    
    # Wait for pods to be ready
    kubectl wait --for=condition=ready pod -l app=rag-legal-chatbot -n "$test_namespace" --timeout=300s
    
    # Test API endpoint
    local api_pod=$(kubectl get pods -n "$test_namespace" -l component=api -o jsonpath='{.items[0].metadata.name}')
    if [[ -n "$api_pod" ]]; then
        kubectl exec -n "$test_namespace" "$api_pod" -- curl -f http://localhost:8000/health
    fi
    
    # Cleanup test namespace
    log "Cleaning up test environment..."
    kubectl delete namespace "$test_namespace" --wait=false
    
    log "Disaster recovery test completed successfully"
}

# Cleanup old backups
cleanup_backups() {
    local retention_days="${1:-30}"
    
    log "Cleaning up backups older than $retention_days days..."
    
    # Cleanup Velero backups
    local old_backups=$(velero backup get --output json | jq -r ".items[] | select(.metadata.creationTimestamp < \"$(date -d "$retention_days days ago" -Iseconds)\") | .metadata.name")
    
    for backup in $old_backups; do
        log "Deleting old backup: $backup"
        velero backup delete "$backup" --confirm
    done
    
    # Cleanup S3 backups
    aws s3 ls "s3://$S3_BUCKET/milvus-backups/" | while read -r line; do
        local backup_date=$(echo "$line" | awk '{print $1}')
        if [[ $(date -d "$backup_date" +%s) -lt $(date -d "$retention_days days ago" +%s) ]]; then
            local backup_file=$(echo "$line" | awk '{print $4}')
            log "Deleting old Milvus backup: $backup_file"
            aws s3 rm "s3://$S3_BUCKET/milvus-backups/$backup_file"
        fi
    done
    
    aws s3 ls "s3://$S3_BUCKET/redis-backups/" | while read -r line; do
        local backup_date=$(echo "$line" | awk '{print $1}')
        if [[ $(date -d "$backup_date" +%s) -lt $(date -d "$retention_days days ago" +%s) ]]; then
            local backup_file=$(echo "$line" | awk '{print $4}')
            log "Deleting old Redis backup: $backup_file"
            aws s3 rm "s3://$S3_BUCKET/redis-backups/$backup_file"
        fi
    done
    
    log "Backup cleanup completed"
}

# Generate backup report
generate_report() {
    log "Generating backup report..."
    
    local report_file="$BACKUP_DIR/backup-report-$(date +%Y%m%d_%H%M%S).md"
    
    {
        echo "# Backup and Recovery Report"
        echo "Generated: $(date)"
        echo ""
        
        echo "## Backup Status"
        echo "### Velero Backups"
        velero backup get
        echo ""
        
        echo "### Database Backups"
        echo "#### Milvus Backups (Last 10)"
        aws s3 ls "s3://$S3_BUCKET/milvus-backups/" | tail -10
        echo ""
        
        echo "#### Redis Backups (Last 10)"
        aws s3 ls "s3://$S3_BUCKET/redis-backups/" | tail -10
        echo ""
        
        echo "## Storage Usage"
        echo "### S3 Bucket Usage"
        aws s3 ls "s3://$S3_BUCKET/" --recursive --human-readable --summarize
        echo ""
        
        echo "## Backup Jobs Status"
        kubectl get cronjobs -n "$NAMESPACE" | grep backup
        echo ""
        
        echo "## Recent Backup Job Runs"
        kubectl get jobs -n "$NAMESPACE" | grep backup | head -10
        
    } > "$report_file"
    
    log "Backup report generated: $report_file"
}

# Script usage
usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  setup                    - Setup backup infrastructure"
    echo "  backup [name]           - Create manual backup"
    echo "  list                    - List available backups"
    echo "  restore <backup> [ns]   - Restore from backup"
    echo "  verify <backup>         - Verify backup integrity"
    echo "  test-dr                 - Test disaster recovery"
    echo "  cleanup [days]          - Cleanup old backups (default: 30 days)"
    echo "  report                  - Generate backup report"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 backup emergency-backup"
    echo "  $0 restore daily-backup-20241201"
    echo "  $0 cleanup 60"
}

# Main script logic
check_prerequisites

case "${1:-list}" in
    setup)
        setup_backup_infrastructure
        ;;
    backup)
        create_backup "$2"
        ;;
    list)
        list_backups
        ;;
    restore)
        restore_backup "$2" "$3"
        ;;
    verify)
        verify_backup "$2"
        ;;
    test-dr)
        test_disaster_recovery
        ;;
    cleanup)
        cleanup_backups "$2"
        ;;
    report)
        generate_report
        ;;
    *)
        usage
        exit 1
        ;;
esac
