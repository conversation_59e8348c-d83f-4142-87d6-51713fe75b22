#!/bin/bash
# Security Hardening Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SECURITY_DIR="$PROJECT_ROOT/security"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking security hardening prerequisites..."
    
    local required_commands=("kubectl" "openssl" "curl" "jq")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    log "Prerequisites check completed"
}

# Generate strong passwords and keys
generate_security_credentials() {
    log "Generating security credentials..."
    
    mkdir -p "$SECURITY_DIR/generated"
    
    # Generate strong passwords
    REDIS_PASSWORD=$(openssl rand -base64 32)
    MILVUS_PASSWORD=$(openssl rand -base64 32)
    ADMIN_PASSWORD=$(openssl rand -base64 24)
    SECRET_KEY=$(openssl rand -base64 32)
    JWT_SECRET=$(openssl rand -base64 32)
    
    # Generate API keys
    API_KEY=$(openssl rand -hex 32)
    WEBHOOK_SECRET=$(openssl rand -hex 24)
    
    # Save to secure file
    cat > "$SECURITY_DIR/generated/credentials.env" << EOF
# Generated Security Credentials - $(date)
# KEEP THIS FILE SECURE AND DO NOT COMMIT TO VERSION CONTROL

REDIS_PASSWORD=$REDIS_PASSWORD
MILVUS_PASSWORD=$MILVUS_PASSWORD
ADMIN_PASSWORD=$ADMIN_PASSWORD
SECRET_KEY=$SECRET_KEY
JWT_SECRET=$JWT_SECRET
API_KEY=$API_KEY
WEBHOOK_SECRET=$WEBHOOK_SECRET
EOF

    chmod 600 "$SECURITY_DIR/generated/credentials.env"
    
    log "Security credentials generated and saved to $SECURITY_DIR/generated/credentials.env"
}

# Generate TLS certificates
generate_tls_certificates() {
    log "Generating TLS certificates..."
    
    local cert_dir="$SECURITY_DIR/generated/certs"
    mkdir -p "$cert_dir"
    
    # Generate CA private key
    openssl genrsa -out "$cert_dir/ca-key.pem" 4096
    
    # Generate CA certificate
    openssl req -new -x509 -days 365 -key "$cert_dir/ca-key.pem" -sha256 -out "$cert_dir/ca.pem" -subj "/C=DE/ST=State/L=City/O=RAG-Chatbot/CN=RAG-Chatbot-CA"
    
    # Generate server private key
    openssl genrsa -out "$cert_dir/server-key.pem" 4096
    
    # Generate server certificate signing request
    openssl req -subj "/C=DE/ST=State/L=City/O=RAG-Chatbot/CN=rag-chatbot.local" -sha256 -new -key "$cert_dir/server-key.pem" -out "$cert_dir/server.csr"
    
    # Create extensions file
    cat > "$cert_dir/server-extfile.cnf" << EOF
subjectAltName = DNS:rag-chatbot.local,DNS:api.rag-chatbot.local,DNS:admin.rag-chatbot.local,IP:127.0.0.1,IP:localhost
extendedKeyUsage = serverAuth
EOF
    
    # Generate server certificate
    openssl x509 -req -days 365 -sha256 -in "$cert_dir/server.csr" -CA "$cert_dir/ca.pem" -CAkey "$cert_dir/ca-key.pem" -out "$cert_dir/server-cert.pem" -extfile "$cert_dir/server-extfile.cnf" -CAcreateserial
    
    # Generate client private key
    openssl genrsa -out "$cert_dir/client-key.pem" 4096
    
    # Generate client certificate signing request
    openssl req -subj "/C=DE/ST=State/L=City/O=RAG-Chatbot/CN=client" -new -key "$cert_dir/client-key.pem" -out "$cert_dir/client.csr"
    
    # Create client extensions file
    echo "extendedKeyUsage = clientAuth" > "$cert_dir/client-extfile.cnf"
    
    # Generate client certificate
    openssl x509 -req -days 365 -sha256 -in "$cert_dir/client.csr" -CA "$cert_dir/ca.pem" -CAkey "$cert_dir/ca-key.pem" -out "$cert_dir/client-cert.pem" -extfile "$cert_dir/client-extfile.cnf" -CAcreateserial
    
    # Generate DH parameters for perfect forward secrecy
    openssl dhparam -out "$cert_dir/dhparam.pem" 2048
    
    # Set proper permissions
    chmod 400 "$cert_dir"/*-key.pem
    chmod 444 "$cert_dir"/*.pem
    
    # Clean up
    rm "$cert_dir"/*.csr "$cert_dir"/*.cnf
    
    log "TLS certificates generated in $cert_dir"
}

# Apply security policies
apply_security_policies() {
    log "Applying Kubernetes security policies..."
    
    # Apply security policies
    kubectl apply -f "$SECURITY_DIR/security-policies.yaml"
    
    # Apply TLS certificates configuration
    kubectl apply -f "$SECURITY_DIR/tls-certificates.yaml"
    
    # Apply secrets management
    kubectl apply -f "$SECURITY_DIR/secrets-management.yaml"
    
    log "Security policies applied successfully"
}

# Configure network security
configure_network_security() {
    log "Configuring network security..."
    
    # Create network policies
    cat > "$SECURITY_DIR/generated/network-policies.yaml" << EOF
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all-ingress
  namespace: rag-chatbot
spec:
  podSelector: {}
  policyTypes:
  - Ingress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all-egress
  namespace: rag-chatbot
spec:
  podSelector: {}
  policyTypes:
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns
  namespace: rag-chatbot
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
EOF

    kubectl apply -f "$SECURITY_DIR/generated/network-policies.yaml"
    
    log "Network security policies configured"
}

# Setup security monitoring
setup_security_monitoring() {
    log "Setting up security monitoring..."
    
    # Create security monitoring configuration
    cat > "$SECURITY_DIR/generated/security-monitoring.yaml" << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-monitoring-config
  namespace: rag-chatbot
data:
  falco.yaml: |
    rules_file:
      - /etc/falco/falco_rules.yaml
      - /etc/falco/falco_rules.local.yaml
      - /etc/falco/k8s_audit_rules.yaml
      - /etc/falco/rules.d
    
    time_format_iso_8601: true
    json_output: true
    json_include_output_property: true
    
    log_stderr: true
    log_syslog: true
    log_level: info
    
    priority: debug
    
    buffered_outputs: false
    
    outputs:
      rate: 1
      max_burst: 1000
    
    syslog_output:
      enabled: true
    
    file_output:
      enabled: true
      keep_alive: false
      filename: /var/log/falco.log
    
    stdout_output:
      enabled: true
    
    webserver:
      enabled: true
      listen_port: 8765
      k8s_healthz_endpoint: /healthz
      ssl_enabled: false
      ssl_certificate: /etc/ssl/falco/falco.pem
    
    grpc:
      enabled: false
      bind_address: "0.0.0.0:5060"
      threadiness: 8
    
    grpc_output:
      enabled: false
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: rag-chatbot
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccount: falco
      hostNetwork: true
      hostPID: true
      containers:
      - name: falco
        image: falcosecurity/falco:latest
        securityContext:
          privileged: true
        volumeMounts:
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
        - mountPath: /host/dev
          name: dev-fs
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /etc/falco
          name: falco-config
      volumes:
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: falco-config
        configMap:
          name: security-monitoring-config
EOF

    kubectl apply -f "$SECURITY_DIR/generated/security-monitoring.yaml"
    
    log "Security monitoring configured"
}

# Harden container images
harden_container_images() {
    log "Hardening container images..."
    
    # Create security context for containers
    cat > "$SECURITY_DIR/generated/security-contexts.yaml" << EOF
apiVersion: v1
kind: SecurityContext
metadata:
  name: rag-chatbot-security-context
spec:
  runAsNonRoot: true
  runAsUser: 1001
  runAsGroup: 1001
  fsGroup: 1001
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
    - ALL
  seccompProfile:
    type: RuntimeDefault
  seLinuxOptions:
    level: "s0:c123,c456"
EOF

    # Create Pod Security Standards
    kubectl label namespace rag-chatbot pod-security.kubernetes.io/enforce=restricted
    kubectl label namespace rag-chatbot pod-security.kubernetes.io/audit=restricted
    kubectl label namespace rag-chatbot pod-security.kubernetes.io/warn=restricted
    
    log "Container security hardening completed"
}

# Setup vulnerability scanning
setup_vulnerability_scanning() {
    log "Setting up vulnerability scanning..."
    
    # Create vulnerability scanning job
    cat > "$SECURITY_DIR/generated/vulnerability-scan.yaml" << EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: vulnerability-scan
  namespace: rag-chatbot
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trivy-scanner
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              # Scan running containers
              trivy image --format json --output /reports/api-scan.json rag-chatbot-api:latest
              trivy image --format json --output /reports/worker-scan.json rag-chatbot-worker:latest
              
              # Scan for misconfigurations
              trivy config --format json --output /reports/config-scan.json /k8s
              
              # Check for critical vulnerabilities
              CRITICAL_COUNT=\$(cat /reports/*-scan.json | jq '[.Results[]?.Vulnerabilities[]? | select(.Severity == "CRITICAL")] | length')
              
              if [ "\$CRITICAL_COUNT" -gt 0 ]; then
                echo "ALERT: Found \$CRITICAL_COUNT critical vulnerabilities"
                # Send alert (webhook, email, etc.)
              fi
            volumeMounts:
            - name: scan-reports
              mountPath: /reports
            - name: k8s-configs
              mountPath: /k8s
          volumes:
          - name: scan-reports
            persistentVolumeClaim:
              claimName: scan-reports-pvc
          - name: k8s-configs
            configMap:
              name: k8s-configs
          restartPolicy: OnFailure
EOF

    kubectl apply -f "$SECURITY_DIR/generated/vulnerability-scan.yaml"
    
    log "Vulnerability scanning configured"
}

# Configure audit logging
configure_audit_logging() {
    log "Configuring audit logging..."
    
    # Create audit policy
    cat > "$SECURITY_DIR/generated/audit-policy.yaml" << EOF
apiVersion: audit.k8s.io/v1
kind: Policy
rules:
# Log all requests at RequestResponse level for secrets
- level: RequestResponse
  resources:
  - group: ""
    resources: ["secrets"]
  namespaces: ["rag-chatbot"]

# Log all requests at Metadata level for other resources
- level: Metadata
  resources:
  - group: ""
    resources: ["pods", "services", "configmaps"]
  - group: "apps"
    resources: ["deployments", "replicasets"]
  namespaces: ["rag-chatbot"]

# Log authentication and authorization
- level: Request
  users: ["system:anonymous"]
  
- level: RequestResponse
  resources:
  - group: "rbac.authorization.k8s.io"
    resources: ["roles", "rolebindings", "clusterroles", "clusterrolebindings"]
EOF

    log "Audit logging policy created"
}

# Validate security configuration
validate_security() {
    log "Validating security configuration..."
    
    local validation_errors=()
    
    # Check if namespace has security labels
    if ! kubectl get namespace rag-chatbot -o jsonpath='{.metadata.labels.pod-security\.kubernetes\.io/enforce}' | grep -q "restricted"; then
        validation_errors+=("Namespace does not have restricted pod security policy")
    fi
    
    # Check if network policies are applied
    local network_policies=$(kubectl get networkpolicy -n rag-chatbot --no-headers | wc -l)
    if [[ $network_policies -lt 3 ]]; then
        validation_errors+=("Insufficient network policies applied")
    fi
    
    # Check if TLS certificates exist
    if ! kubectl get secret rag-chatbot-tls -n rag-chatbot &> /dev/null; then
        validation_errors+=("TLS certificates not found")
    fi
    
    # Check if security monitoring is running
    if ! kubectl get daemonset falco -n rag-chatbot &> /dev/null; then
        validation_errors+=("Security monitoring (Falco) not running")
    fi
    
    if [[ ${#validation_errors[@]} -eq 0 ]]; then
        log "Security validation passed"
        return 0
    else
        error "Security validation failed: ${validation_errors[*]}"
        return 1
    fi
}

# Generate security report
generate_security_report() {
    log "Generating security report..."
    
    local report_file="$SECURITY_DIR/generated/security-report-$(date +%Y%m%d_%H%M%S).md"
    
    {
        echo "# Security Hardening Report"
        echo "Generated: $(date)"
        echo ""
        
        echo "## Security Policies Applied"
        kubectl get networkpolicy -n rag-chatbot
        echo ""
        
        echo "## TLS Certificates"
        kubectl get certificates -n rag-chatbot
        echo ""
        
        echo "## Security Monitoring"
        kubectl get daemonset falco -n rag-chatbot
        echo ""
        
        echo "## Pod Security Standards"
        kubectl get namespace rag-chatbot -o yaml | grep -A 5 "pod-security"
        echo ""
        
        echo "## RBAC Configuration"
        kubectl get rolebinding -n rag-chatbot
        echo ""
        
    } > "$report_file"
    
    log "Security report generated: $report_file"
}

# Main hardening function
harden_security() {
    log "Starting security hardening for RAG Legal Chatbot..."
    
    check_prerequisites
    generate_security_credentials
    generate_tls_certificates
    apply_security_policies
    configure_network_security
    setup_security_monitoring
    harden_container_images
    setup_vulnerability_scanning
    configure_audit_logging
    validate_security
    generate_security_report
    
    log "Security hardening completed successfully!"
    
    info "Next steps:"
    echo "1. Review generated credentials in $SECURITY_DIR/generated/"
    echo "2. Update application configurations with new credentials"
    echo "3. Configure external secrets management (Vault, AWS Secrets Manager)"
    echo "4. Set up certificate management with cert-manager"
    echo "5. Configure monitoring alerts for security events"
    echo "6. Review and customize security policies as needed"
}

# Script usage
usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  harden     - Apply all security hardening measures"
    echo "  validate   - Validate current security configuration"
    echo "  report     - Generate security report"
    echo "  creds      - Generate new security credentials"
    echo "  certs      - Generate TLS certificates"
    echo ""
    echo "Examples:"
    echo "  $0 harden"
    echo "  $0 validate"
}

# Main script logic
case "${1:-harden}" in
    harden)
        harden_security
        ;;
    validate)
        validate_security
        ;;
    report)
        generate_security_report
        ;;
    creds)
        generate_security_credentials
        ;;
    certs)
        generate_tls_certificates
        ;;
    *)
        usage
        exit 1
        ;;
esac
