#!/bin/bash
# Secrets Management Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SECRETS_DIR="$PROJECT_ROOT/.secrets"
VAULT_FILE="$SECRETS_DIR/vault.enc"
KEY_FILE="$SECRETS_DIR/vault.key"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    local required_commands=("openssl" "base64" "jq")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
}

# Initialize secrets vault
init_vault() {
    log "Initializing secrets vault..."
    
    mkdir -p "$SECRETS_DIR"
    chmod 700 "$SECRETS_DIR"
    
    # Generate encryption key
    if [[ ! -f "$KEY_FILE" ]]; then
        openssl rand -base64 32 > "$KEY_FILE"
        chmod 600 "$KEY_FILE"
        log "Encryption key generated: $KEY_FILE"
    else
        log "Encryption key already exists: $KEY_FILE"
    fi
    
    # Create empty vault if it doesn't exist
    if [[ ! -f "$VAULT_FILE" ]]; then
        echo '{}' | encrypt_data > "$VAULT_FILE"
        log "Secrets vault initialized: $VAULT_FILE"
    else
        log "Secrets vault already exists: $VAULT_FILE"
    fi
}

# Encrypt data
encrypt_data() {
    local key=$(cat "$KEY_FILE")
    openssl enc -aes-256-cbc -salt -pbkdf2 -k "$key"
}

# Decrypt data
decrypt_data() {
    local key=$(cat "$KEY_FILE")
    openssl enc -d -aes-256-cbc -salt -pbkdf2 -k "$key"
}

# Get secret from vault
get_secret() {
    local secret_name="$1"
    
    if [[ -z "$secret_name" ]]; then
        error "Secret name is required"
    fi
    
    if [[ ! -f "$VAULT_FILE" ]]; then
        error "Secrets vault not found. Run 'init' first."
    fi
    
    local decrypted_data=$(cat "$VAULT_FILE" | decrypt_data)
    local secret_value=$(echo "$decrypted_data" | jq -r ".$secret_name // empty")
    
    if [[ -z "$secret_value" ]]; then
        error "Secret '$secret_name' not found in vault"
    fi
    
    echo "$secret_value"
}

# Set secret in vault
set_secret() {
    local secret_name="$1"
    local secret_value="$2"
    
    if [[ -z "$secret_name" ]] || [[ -z "$secret_value" ]]; then
        error "Secret name and value are required"
    fi
    
    if [[ ! -f "$VAULT_FILE" ]]; then
        init_vault
    fi
    
    # Decrypt current vault
    local current_data=$(cat "$VAULT_FILE" | decrypt_data)
    
    # Update secret
    local updated_data=$(echo "$current_data" | jq --arg name "$secret_name" --arg value "$secret_value" '. + {($name): $value}')
    
    # Encrypt and save
    echo "$updated_data" | encrypt_data > "$VAULT_FILE"
    
    log "Secret '$secret_name' stored in vault"
}

# Delete secret from vault
delete_secret() {
    local secret_name="$1"
    
    if [[ -z "$secret_name" ]]; then
        error "Secret name is required"
    fi
    
    if [[ ! -f "$VAULT_FILE" ]]; then
        error "Secrets vault not found"
    fi
    
    # Decrypt current vault
    local current_data=$(cat "$VAULT_FILE" | decrypt_data)
    
    # Remove secret
    local updated_data=$(echo "$current_data" | jq --arg name "$secret_name" 'del(.[$name])')
    
    # Encrypt and save
    echo "$updated_data" | encrypt_data > "$VAULT_FILE"
    
    log "Secret '$secret_name' deleted from vault"
}

# List all secrets (names only)
list_secrets() {
    if [[ ! -f "$VAULT_FILE" ]]; then
        error "Secrets vault not found"
    fi
    
    local decrypted_data=$(cat "$VAULT_FILE" | decrypt_data)
    echo "$decrypted_data" | jq -r 'keys[]'
}

# Generate secure random secret
generate_secret() {
    local length="${1:-32}"
    local type="${2:-base64}"
    
    case "$type" in
        "base64")
            openssl rand -base64 "$length"
            ;;
        "hex")
            openssl rand -hex "$length"
            ;;
        "alphanumeric")
            openssl rand -base64 "$length" | tr -d "=+/" | cut -c1-"$length"
            ;;
        *)
            error "Unknown secret type: $type. Use: base64, hex, alphanumeric"
            ;;
    esac
}

# Export secrets to environment file
export_to_env() {
    local env_file="${1:-$PROJECT_ROOT/.env}"
    
    if [[ ! -f "$VAULT_FILE" ]]; then
        error "Secrets vault not found"
    fi
    
    log "Exporting secrets to environment file: $env_file"
    
    # Backup existing env file
    if [[ -f "$env_file" ]]; then
        cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Decrypt vault
    local decrypted_data=$(cat "$VAULT_FILE" | decrypt_data)
    
    # Create/update env file
    {
        echo "# Environment file generated from secrets vault"
        echo "# Generated on: $(date)"
        echo ""
        
        # Export each secret
        echo "$decrypted_data" | jq -r 'to_entries[] | "\(.key)=\(.value)"'
    } > "$env_file"
    
    chmod 600 "$env_file"
    log "Secrets exported to: $env_file"
}

# Import secrets from environment file
import_from_env() {
    local env_file="${1:-$PROJECT_ROOT/.env}"
    
    if [[ ! -f "$env_file" ]]; then
        error "Environment file not found: $env_file"
    fi
    
    log "Importing secrets from environment file: $env_file"
    
    # Initialize vault if needed
    if [[ ! -f "$VAULT_FILE" ]]; then
        init_vault
    fi
    
    # Read env file and extract secrets
    while IFS='=' read -r key value; do
        # Skip comments and empty lines
        if [[ "$key" =~ ^[[:space:]]*# ]] || [[ -z "$key" ]]; then
            continue
        fi
        
        # Remove quotes from value
        value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
        
        # Store in vault
        set_secret "$key" "$value"
    done < "$env_file"
    
    log "Secrets imported from: $env_file"
}

# Rotate secrets
rotate_secrets() {
    log "Rotating secrets..."
    
    # List of secrets to rotate
    local secrets_to_rotate=(
        "SECRET_KEY"
        "ADMIN_API_KEY"
        "REDIS_PASSWORD"
        "MINIO_ACCESS_KEY"
        "MINIO_SECRET_KEY"
    )
    
    for secret in "${secrets_to_rotate[@]}"; do
        local new_value
        case "$secret" in
            "SECRET_KEY")
                new_value=$(generate_secret 32 base64)
                ;;
            "ADMIN_API_KEY")
                new_value=$(generate_secret 24 base64)
                ;;
            "REDIS_PASSWORD")
                new_value=$(generate_secret 16 hex)
                ;;
            "MINIO_ACCESS_KEY")
                new_value=$(generate_secret 12 alphanumeric)
                ;;
            "MINIO_SECRET_KEY")
                new_value=$(generate_secret 24 alphanumeric)
                ;;
        esac
        
        set_secret "$secret" "$new_value"
        log "Rotated secret: $secret"
    done
    
    log "Secret rotation completed"
}

# Backup vault
backup_vault() {
    local backup_file="${1:-$SECRETS_DIR/vault.backup.$(date +%Y%m%d_%H%M%S)}"
    
    if [[ ! -f "$VAULT_FILE" ]]; then
        error "Secrets vault not found"
    fi
    
    cp "$VAULT_FILE" "$backup_file"
    cp "$KEY_FILE" "$backup_file.key"
    
    log "Vault backed up to: $backup_file"
}

# Restore vault from backup
restore_vault() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]] || [[ ! -f "$backup_file" ]]; then
        error "Backup file not found: $backup_file"
    fi
    
    if [[ ! -f "$backup_file.key" ]]; then
        error "Backup key file not found: $backup_file.key"
    fi
    
    # Backup current vault
    if [[ -f "$VAULT_FILE" ]]; then
        backup_vault
    fi
    
    # Restore from backup
    cp "$backup_file" "$VAULT_FILE"
    cp "$backup_file.key" "$KEY_FILE"
    
    log "Vault restored from: $backup_file"
}

# Validate vault integrity
validate_vault() {
    if [[ ! -f "$VAULT_FILE" ]] || [[ ! -f "$KEY_FILE" ]]; then
        error "Vault or key file not found"
    fi
    
    # Try to decrypt vault
    if cat "$VAULT_FILE" | decrypt_data | jq . > /dev/null 2>&1; then
        log "Vault integrity check passed"
        return 0
    else
        error "Vault integrity check failed"
    fi
}

# Interactive secret management
interactive_mode() {
    log "Entering interactive secrets management mode"
    
    while true; do
        echo ""
        echo "Secrets Management Menu:"
        echo "1. List secrets"
        echo "2. Get secret"
        echo "3. Set secret"
        echo "4. Delete secret"
        echo "5. Generate secret"
        echo "6. Export to .env"
        echo "7. Rotate secrets"
        echo "8. Backup vault"
        echo "9. Validate vault"
        echo "0. Exit"
        echo ""
        read -p "Choose an option: " choice
        
        case "$choice" in
            1)
                echo "Available secrets:"
                list_secrets
                ;;
            2)
                read -p "Secret name: " name
                get_secret "$name"
                ;;
            3)
                read -p "Secret name: " name
                read -s -p "Secret value: " value
                echo ""
                set_secret "$name" "$value"
                ;;
            4)
                read -p "Secret name: " name
                delete_secret "$name"
                ;;
            5)
                read -p "Length (default 32): " length
                read -p "Type (base64/hex/alphanumeric): " type
                generate_secret "${length:-32}" "${type:-base64}"
                ;;
            6)
                export_to_env
                ;;
            7)
                rotate_secrets
                ;;
            8)
                backup_vault
                ;;
            9)
                validate_vault
                ;;
            0)
                log "Exiting interactive mode"
                break
                ;;
            *)
                warn "Invalid option"
                ;;
        esac
    done
}

# Script usage
usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  init                     - Initialize secrets vault"
    echo "  get <name>              - Get secret value"
    echo "  set <name> <value>      - Set secret value"
    echo "  delete <name>           - Delete secret"
    echo "  list                    - List secret names"
    echo "  generate [length] [type] - Generate random secret"
    echo "  export [file]           - Export secrets to env file"
    echo "  import [file]           - Import secrets from env file"
    echo "  rotate                  - Rotate all secrets"
    echo "  backup [file]           - Backup vault"
    echo "  restore <file>          - Restore vault from backup"
    echo "  validate                - Validate vault integrity"
    echo "  interactive             - Interactive mode"
    echo ""
    echo "Examples:"
    echo "  $0 init"
    echo "  $0 set GOOGLE_API_KEY 'your-api-key'"
    echo "  $0 get GOOGLE_API_KEY"
    echo "  $0 generate 32 base64"
    echo "  $0 export .env"
}

# Main script logic
check_prerequisites

case "${1:-interactive}" in
    init)
        init_vault
        ;;
    get)
        get_secret "$2"
        ;;
    set)
        set_secret "$2" "$3"
        ;;
    delete)
        delete_secret "$2"
        ;;
    list)
        list_secrets
        ;;
    generate)
        generate_secret "$2" "$3"
        ;;
    export)
        export_to_env "$2"
        ;;
    import)
        import_from_env "$2"
        ;;
    rotate)
        rotate_secrets
        ;;
    backup)
        backup_vault "$2"
        ;;
    restore)
        restore_vault "$2"
        ;;
    validate)
        validate_vault
        ;;
    interactive)
        interactive_mode
        ;;
    *)
        usage
        exit 1
        ;;
esac
