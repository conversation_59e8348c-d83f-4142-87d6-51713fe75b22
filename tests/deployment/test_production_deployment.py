#!/usr/bin/env python3
"""
Production Deployment Validation Tests
Comprehensive test suite for validating production deployment
"""

import asyncio
import json
import time
import requests
import pytest
from typing import Dict, List, Any
import subprocess
import yaml
from pathlib import Path

class ProductionDeploymentValidator:
    """Validates production deployment readiness"""
    
    def __init__(self, base_url: str = "http://localhost:8000", admin_token: str = None):
        self.base_url = base_url.rstrip('/')
        self.admin_token = admin_token
        self.session = requests.Session()
        if admin_token:
            self.session.headers.update({"Authorization": f"Bearer {admin_token}"})
    
    def test_basic_connectivity(self) -> Dict[str, Any]:
        """Test basic API connectivity"""
        results = {}
        
        try:
            # Test health endpoint
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            results["health_check"] = {
                "status": "pass" if response.status_code == 200 else "fail",
                "response_time": response.elapsed.total_seconds(),
                "status_code": response.status_code
            }
        except Exception as e:
            results["health_check"] = {"status": "fail", "error": str(e)}
        
        try:
            # Test API documentation
            response = self.session.get(f"{self.base_url}/docs", timeout=10)
            results["api_docs"] = {
                "status": "pass" if response.status_code == 200 else "fail",
                "status_code": response.status_code
            }
        except Exception as e:
            results["api_docs"] = {"status": "fail", "error": str(e)}
        
        try:
            # Test metrics endpoint
            response = self.session.get(f"{self.base_url}/metrics", timeout=10)
            results["metrics"] = {
                "status": "pass" if response.status_code == 200 else "fail",
                "status_code": response.status_code
            }
        except Exception as e:
            results["metrics"] = {"status": "fail", "error": str(e)}
        
        return results
    
    def test_api_functionality(self) -> Dict[str, Any]:
        """Test core API functionality"""
        results = {}
        
        # Test chat endpoint
        try:
            chat_payload = {
                "query": "What is a trademark?",
                "session_id": "test_session_validation",
                "max_results": 3
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=chat_payload,
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                results["chat_endpoint"] = {
                    "status": "pass",
                    "response_time": response_time,
                    "has_answer": "answer" in data,
                    "has_sources": "sources" in data,
                    "sources_count": len(data.get("sources", []))
                }
            else:
                results["chat_endpoint"] = {
                    "status": "fail",
                    "status_code": response.status_code,
                    "response": response.text[:200]
                }
        except Exception as e:
            results["chat_endpoint"] = {"status": "fail", "error": str(e)}
        
        # Test search endpoint
        try:
            search_payload = {
                "query": "intellectual property",
                "limit": 5
            }
            
            response = self.session.post(
                f"{self.base_url}/api/search",
                json=search_payload,
                timeout=20
            )
            
            if response.status_code == 200:
                data = response.json()
                results["search_endpoint"] = {
                    "status": "pass",
                    "results_count": len(data.get("results", [])),
                    "has_metadata": "search_metadata" in data
                }
            else:
                results["search_endpoint"] = {
                    "status": "fail",
                    "status_code": response.status_code
                }
        except Exception as e:
            results["search_endpoint"] = {"status": "fail", "error": str(e)}
        
        return results
    
    def test_performance_requirements(self) -> Dict[str, Any]:
        """Test performance requirements"""
        results = {}
        
        # Response time test
        response_times = []
        for i in range(10):
            try:
                start_time = time.time()
                response = self.session.get(f"{self.base_url}/health", timeout=10)
                response_time = time.time() - start_time
                response_times.append(response_time)
            except Exception:
                response_times.append(10.0)  # Timeout value
        
        avg_response_time = sum(response_times) / len(response_times)
        p95_response_time = sorted(response_times)[int(0.95 * len(response_times))]
        
        results["response_time"] = {
            "average": avg_response_time,
            "p95": p95_response_time,
            "meets_requirement": p95_response_time < 2.0,  # 2 second requirement
            "all_times": response_times
        }
        
        # Concurrent request test
        async def make_request():
            try:
                response = requests.get(f"{self.base_url}/health", timeout=10)
                return response.status_code == 200
            except Exception:
                return False
        
        async def concurrent_test():
            tasks = [make_request() for _ in range(20)]
            results = await asyncio.gather(*tasks)
            return sum(results), len(results)
        
        try:
            successful, total = asyncio.run(concurrent_test())
            results["concurrent_requests"] = {
                "successful": successful,
                "total": total,
                "success_rate": successful / total,
                "meets_requirement": (successful / total) > 0.95
            }
        except Exception as e:
            results["concurrent_requests"] = {"status": "fail", "error": str(e)}
        
        return results
    
    def test_security_configuration(self) -> Dict[str, Any]:
        """Test security configuration"""
        results = {}
        
        # Test HTTPS redirect (if applicable)
        try:
            response = requests.get(
                self.base_url.replace("https://", "http://"),
                allow_redirects=False,
                timeout=10
            )
            results["https_redirect"] = {
                "status": "pass" if response.status_code in [301, 302] else "fail",
                "status_code": response.status_code
            }
        except Exception as e:
            results["https_redirect"] = {"status": "fail", "error": str(e)}
        
        # Test security headers
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            headers = response.headers
            
            security_headers = {
                "X-Frame-Options": headers.get("X-Frame-Options"),
                "X-Content-Type-Options": headers.get("X-Content-Type-Options"),
                "X-XSS-Protection": headers.get("X-XSS-Protection"),
                "Strict-Transport-Security": headers.get("Strict-Transport-Security")
            }
            
            results["security_headers"] = {
                "headers": security_headers,
                "has_frame_options": "X-Frame-Options" in headers,
                "has_content_type_options": "X-Content-Type-Options" in headers,
                "has_xss_protection": "X-XSS-Protection" in headers
            }
        except Exception as e:
            results["security_headers"] = {"status": "fail", "error": str(e)}
        
        # Test rate limiting
        try:
            responses = []
            for i in range(100):  # Try to exceed rate limit
                response = self.session.get(f"{self.base_url}/health", timeout=1)
                responses.append(response.status_code)
                if response.status_code == 429:
                    break
            
            results["rate_limiting"] = {
                "triggered": 429 in responses,
                "requests_before_limit": len([r for r in responses if r != 429])
            }
        except Exception as e:
            results["rate_limiting"] = {"status": "fail", "error": str(e)}
        
        return results
    
    def test_database_connectivity(self) -> Dict[str, Any]:
        """Test database connectivity and health"""
        results = {}
        
        if self.admin_token:
            try:
                response = self.session.get(f"{self.base_url}/api/admin/health", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    components = data.get("components", {})
                    
                    results["database_health"] = {
                        "redis": components.get("database", {}).get("redis"),
                        "milvus": components.get("database", {}).get("milvus"),
                        "overall_status": data.get("status")
                    }
                else:
                    results["database_health"] = {
                        "status": "fail",
                        "status_code": response.status_code
                    }
            except Exception as e:
                results["database_health"] = {"status": "fail", "error": str(e)}
        else:
            results["database_health"] = {"status": "skipped", "reason": "No admin token"}
        
        return results
    
    def test_monitoring_integration(self) -> Dict[str, Any]:
        """Test monitoring and metrics integration"""
        results = {}
        
        # Test Prometheus metrics format
        try:
            response = self.session.get(f"{self.base_url}/metrics", timeout=10)
            if response.status_code == 200:
                metrics_text = response.text
                
                # Check for key metrics
                has_http_requests = "http_requests_total" in metrics_text
                has_response_time = "http_request_duration" in metrics_text
                has_help_text = "# HELP" in metrics_text
                
                results["prometheus_metrics"] = {
                    "status": "pass" if has_help_text else "fail",
                    "has_http_requests": has_http_requests,
                    "has_response_time": has_response_time,
                    "metrics_count": len([line for line in metrics_text.split('\n') if line.startswith('# HELP')])
                }
            else:
                results["prometheus_metrics"] = {
                    "status": "fail",
                    "status_code": response.status_code
                }
        except Exception as e:
            results["prometheus_metrics"] = {"status": "fail", "error": str(e)}
        
        return results
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run all validation tests"""
        print("Starting comprehensive deployment validation...")
        
        validation_results = {
            "timestamp": time.time(),
            "base_url": self.base_url,
            "tests": {}
        }
        
        test_suites = [
            ("connectivity", self.test_basic_connectivity),
            ("api_functionality", self.test_api_functionality),
            ("performance", self.test_performance_requirements),
            ("security", self.test_security_configuration),
            ("database", self.test_database_connectivity),
            ("monitoring", self.test_monitoring_integration)
        ]
        
        for suite_name, test_function in test_suites:
            print(f"Running {suite_name} tests...")
            try:
                validation_results["tests"][suite_name] = test_function()
                print(f"✓ {suite_name} tests completed")
            except Exception as e:
                validation_results["tests"][suite_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"✗ {suite_name} tests failed: {e}")
        
        # Calculate overall status
        all_tests = []
        for suite_results in validation_results["tests"].values():
            if isinstance(suite_results, dict):
                for test_result in suite_results.values():
                    if isinstance(test_result, dict) and "status" in test_result:
                        all_tests.append(test_result["status"])
        
        passed_tests = sum(1 for status in all_tests if status == "pass")
        total_tests = len(all_tests)
        
        validation_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "overall_status": "pass" if passed_tests / total_tests >= 0.8 else "fail"
        }
        
        return validation_results

class KubernetesDeploymentValidator:
    """Validates Kubernetes deployment configuration"""
    
    def __init__(self, namespace: str = "rag-chatbot"):
        self.namespace = namespace
    
    def run_kubectl_command(self, command: List[str]) -> Dict[str, Any]:
        """Run kubectl command and return result"""
        try:
            result = subprocess.run(
                ["kubectl"] + command,
                capture_output=True,
                text=True,
                timeout=30
            )
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Command timeout"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_pod_health(self) -> Dict[str, Any]:
        """Test pod health and readiness"""
        results = {}
        
        # Get pod status
        cmd_result = self.run_kubectl_command([
            "get", "pods", "-n", self.namespace, "-o", "json"
        ])
        
        if cmd_result["success"]:
            try:
                pods_data = json.loads(cmd_result["stdout"])
                pods = pods_data.get("items", [])
                
                pod_statuses = {}
                for pod in pods:
                    name = pod["metadata"]["name"]
                    status = pod["status"]
                    
                    pod_statuses[name] = {
                        "phase": status.get("phase"),
                        "ready": all(
                            condition.get("status") == "True"
                            for condition in status.get("conditions", [])
                            if condition.get("type") == "Ready"
                        ),
                        "restart_count": sum(
                            container.get("restartCount", 0)
                            for container in status.get("containerStatuses", [])
                        )
                    }
                
                results["pod_health"] = {
                    "total_pods": len(pods),
                    "running_pods": sum(1 for p in pod_statuses.values() if p["phase"] == "Running"),
                    "ready_pods": sum(1 for p in pod_statuses.values() if p["ready"]),
                    "pods": pod_statuses
                }
            except Exception as e:
                results["pod_health"] = {"status": "fail", "error": str(e)}
        else:
            results["pod_health"] = {"status": "fail", "error": cmd_result.get("stderr")}
        
        return results
    
    def test_service_configuration(self) -> Dict[str, Any]:
        """Test service configuration and endpoints"""
        results = {}
        
        # Get services
        cmd_result = self.run_kubectl_command([
            "get", "services", "-n", self.namespace, "-o", "json"
        ])
        
        if cmd_result["success"]:
            try:
                services_data = json.loads(cmd_result["stdout"])
                services = services_data.get("items", [])
                
                service_info = {}
                for service in services:
                    name = service["metadata"]["name"]
                    spec = service["spec"]
                    
                    service_info[name] = {
                        "type": spec.get("type"),
                        "ports": spec.get("ports", []),
                        "selector": spec.get("selector", {})
                    }
                
                results["services"] = {
                    "total_services": len(services),
                    "services": service_info
                }
            except Exception as e:
                results["services"] = {"status": "fail", "error": str(e)}
        else:
            results["services"] = {"status": "fail", "error": cmd_result.get("stderr")}
        
        # Get endpoints
        cmd_result = self.run_kubectl_command([
            "get", "endpoints", "-n", self.namespace, "-o", "json"
        ])
        
        if cmd_result["success"]:
            try:
                endpoints_data = json.loads(cmd_result["stdout"])
                endpoints = endpoints_data.get("items", [])
                
                endpoint_info = {}
                for endpoint in endpoints:
                    name = endpoint["metadata"]["name"]
                    subsets = endpoint.get("subsets", [])
                    
                    endpoint_info[name] = {
                        "ready_addresses": sum(len(subset.get("addresses", [])) for subset in subsets),
                        "not_ready_addresses": sum(len(subset.get("notReadyAddresses", [])) for subset in subsets)
                    }
                
                results["endpoints"] = {
                    "total_endpoints": len(endpoints),
                    "endpoints": endpoint_info
                }
            except Exception as e:
                results["endpoints"] = {"status": "fail", "error": str(e)}
        
        return results
    
    def test_resource_usage(self) -> Dict[str, Any]:
        """Test resource usage and limits"""
        results = {}
        
        # Get resource usage
        cmd_result = self.run_kubectl_command([
            "top", "pods", "-n", self.namespace, "--no-headers"
        ])
        
        if cmd_result["success"]:
            try:
                lines = cmd_result["stdout"].strip().split('\n')
                resource_usage = {}
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            pod_name = parts[0]
                            cpu = parts[1]
                            memory = parts[2]
                            
                            resource_usage[pod_name] = {
                                "cpu": cpu,
                                "memory": memory
                            }
                
                results["resource_usage"] = {
                    "pods": resource_usage,
                    "total_pods_monitored": len(resource_usage)
                }
            except Exception as e:
                results["resource_usage"] = {"status": "fail", "error": str(e)}
        else:
            results["resource_usage"] = {"status": "fail", "error": "Metrics server not available"}
        
        return results
    
    def run_kubernetes_validation(self) -> Dict[str, Any]:
        """Run all Kubernetes validation tests"""
        print("Starting Kubernetes deployment validation...")
        
        validation_results = {
            "timestamp": time.time(),
            "namespace": self.namespace,
            "tests": {}
        }
        
        test_functions = [
            ("pod_health", self.test_pod_health),
            ("service_configuration", self.test_service_configuration),
            ("resource_usage", self.test_resource_usage)
        ]
        
        for test_name, test_function in test_functions:
            print(f"Running {test_name} validation...")
            try:
                validation_results["tests"][test_name] = test_function()
                print(f"✓ {test_name} validation completed")
            except Exception as e:
                validation_results["tests"][test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"✗ {test_name} validation failed: {e}")
        
        return validation_results

def main():
    """Main validation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Production Deployment Validator")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL for API tests")
    parser.add_argument("--admin-token", help="Admin token for authenticated tests")
    parser.add_argument("--namespace", default="rag-chatbot", help="Kubernetes namespace")
    parser.add_argument("--output", help="Output file for results")
    
    args = parser.parse_args()
    
    # Run API validation
    api_validator = ProductionDeploymentValidator(args.base_url, args.admin_token)
    api_results = api_validator.run_comprehensive_validation()
    
    # Run Kubernetes validation
    k8s_validator = KubernetesDeploymentValidator(args.namespace)
    k8s_results = k8s_validator.run_kubernetes_validation()
    
    # Combine results
    final_results = {
        "validation_timestamp": time.time(),
        "api_validation": api_results,
        "kubernetes_validation": k8s_results
    }
    
    # Calculate overall status
    api_success = api_results.get("summary", {}).get("overall_status") == "pass"
    k8s_success = len(k8s_results.get("tests", {})) > 0  # Basic check
    
    final_results["overall_status"] = "pass" if api_success and k8s_success else "fail"
    
    # Output results
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(final_results, f, indent=2)
        print(f"Results saved to {args.output}")
    else:
        print(json.dumps(final_results, indent=2))
    
    # Exit with appropriate code
    exit(0 if final_results["overall_status"] == "pass" else 1)

if __name__ == "__main__":
    main()
