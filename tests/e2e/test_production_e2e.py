#!/usr/bin/env python3
"""
End-to-End Production Tests
Comprehensive E2E tests for production deployment validation
"""

import asyncio
import json
import time
import pytest
import requests
from typing import Dict, List, Any, Optional
import uuid
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TestConfig:
    """Test configuration"""
    base_url: str = "http://localhost:8000"
    admin_token: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    concurrent_users: int = 10

class ProductionE2ETests:
    """End-to-end tests for production deployment"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = config.timeout
        
        if config.admin_token:
            self.session.headers.update({
                "Authorization": f"Bearer {config.admin_token}"
            })
    
    def setup_method(self):
        """Setup for each test method"""
        self.test_session_id = f"e2e_test_{uuid.uuid4().hex[:8]}"
        self.start_time = time.time()
    
    def teardown_method(self):
        """Cleanup after each test method"""
        # Clean up test session
        try:
            self.session.delete(
                f"{self.config.base_url}/api/conversations/{self.test_session_id}"
            )
        except Exception:
            pass  # Ignore cleanup errors
    
    def test_system_health(self):
        """Test basic system health"""
        response = self.session.get(f"{self.config.base_url}/health")
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data["status"] == "healthy"
        assert "timestamp" in health_data
        assert "version" in health_data
    
    def test_api_documentation_accessible(self):
        """Test API documentation is accessible"""
        response = self.session.get(f"{self.config.base_url}/docs")
        assert response.status_code == 200
        assert "swagger" in response.text.lower() or "openapi" in response.text.lower()
    
    def test_metrics_endpoint(self):
        """Test Prometheus metrics endpoint"""
        response = self.session.get(f"{self.config.base_url}/metrics")
        assert response.status_code == 200
        
        metrics_text = response.text
        assert "# HELP" in metrics_text
        assert "# TYPE" in metrics_text
        assert "http_requests_total" in metrics_text
    
    def test_basic_chat_functionality(self):
        """Test basic chat functionality"""
        chat_payload = {
            "query": "What is intellectual property law?",
            "session_id": self.test_session_id,
            "max_results": 5
        }
        
        response = self.session.post(
            f"{self.config.base_url}/api/chat",
            json=chat_payload
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "answer" in data
        assert "sources" in data
        assert "session_id" in data
        assert "query_id" in data
        assert data["session_id"] == self.test_session_id
        assert len(data["answer"]) > 0
        assert isinstance(data["sources"], list)
    
    def test_chat_with_filters(self):
        """Test chat with document filters"""
        chat_payload = {
            "query": "What are the requirements for trademark registration?",
            "session_id": self.test_session_id,
            "max_results": 3,
            "filters": {
                "document_type": "statute",
                "jurisdiction": "germany"
            }
        }
        
        response = self.session.post(
            f"{self.config.base_url}/api/chat",
            json=chat_payload
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "answer" in data
        assert "sources" in data
        assert len(data["sources"]) <= 3
    
    def test_streaming_chat(self):
        """Test streaming chat functionality"""
        chat_payload = {
            "query": "Explain copyright law basics",
            "session_id": self.test_session_id,
            "stream": True
        }
        
        response = self.session.post(
            f"{self.config.base_url}/api/chat/stream",
            json=chat_payload,
            stream=True
        )
        
        assert response.status_code == 200
        
        chunks_received = 0
        for line in response.iter_lines():
            if line:
                chunks_received += 1
                # Basic validation of SSE format
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    try:
                        json.loads(line_str[6:])  # Parse JSON after 'data: '
                    except json.JSONDecodeError:
                        pass  # Some chunks might not be JSON
                
                if chunks_received > 50:  # Prevent infinite loops
                    break
        
        assert chunks_received > 0
    
    def test_document_search(self):
        """Test document search functionality"""
        search_payload = {
            "query": "data protection regulations",
            "limit": 10,
            "filters": {
                "document_type": "regulation"
            }
        }
        
        response = self.session.post(
            f"{self.config.base_url}/api/search",
            json=search_payload
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "results" in data
        assert "total_results" in data
        assert "search_metadata" in data
        assert isinstance(data["results"], list)
        assert len(data["results"]) <= 10
    
    def test_conversation_history(self):
        """Test conversation history functionality"""
        # First, create some conversation history
        queries = [
            "What is a patent?",
            "How long does a patent last?",
            "What are the requirements for patent application?"
        ]
        
        for query in queries:
            chat_payload = {
                "query": query,
                "session_id": self.test_session_id,
                "max_results": 2
            }
            
            response = self.session.post(
                f"{self.config.base_url}/api/chat",
                json=chat_payload
            )
            assert response.status_code == 200
        
        # Now retrieve conversation history
        response = self.session.get(
            f"{self.config.base_url}/api/conversations/{self.test_session_id}"
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "conversations" in data
        assert "session_id" in data
        assert data["session_id"] == self.test_session_id
        assert len(data["conversations"]) == len(queries)
    
    def test_conversation_cleanup(self):
        """Test conversation cleanup functionality"""
        # Create some conversation history
        chat_payload = {
            "query": "Test query for cleanup",
            "session_id": self.test_session_id,
            "max_results": 1
        }
        
        response = self.session.post(
            f"{self.config.base_url}/api/chat",
            json=chat_payload
        )
        assert response.status_code == 200
        
        # Clear conversation history
        response = self.session.delete(
            f"{self.config.base_url}/api/conversations/{self.test_session_id}"
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "queries_deleted" in data
    
    def test_error_handling(self):
        """Test API error handling"""
        # Test invalid query
        invalid_payload = {
            "query": "",  # Empty query should fail
            "session_id": self.test_session_id
        }
        
        response = self.session.post(
            f"{self.config.base_url}/api/chat",
            json=invalid_payload
        )
        
        assert response.status_code == 400
        
        error_data = response.json()
        assert "error" in error_data
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Make rapid requests to trigger rate limiting
        responses = []
        
        for i in range(100):
            try:
                response = self.session.get(
                    f"{self.config.base_url}/health",
                    timeout=1
                )
                responses.append(response.status_code)
                
                if response.status_code == 429:
                    break
            except requests.exceptions.Timeout:
                break
        
        # Should eventually hit rate limit
        assert 429 in responses or len(responses) < 100
    
    def test_concurrent_requests(self):
        """Test system under concurrent load"""
        def make_request():
            try:
                chat_payload = {
                    "query": f"Test concurrent query {uuid.uuid4().hex[:8]}",
                    "session_id": f"concurrent_{uuid.uuid4().hex[:8]}",
                    "max_results": 2
                }
                
                response = self.session.post(
                    f"{self.config.base_url}/api/chat",
                    json=chat_payload,
                    timeout=30
                )
                
                return {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_time": response.elapsed.total_seconds()
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "success": False,
                    "error": str(e)
                }
        
        # Run concurrent requests
        with ThreadPoolExecutor(max_workers=self.config.concurrent_users) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = [future.result() for future in futures]
        
        # Analyze results
        successful_requests = sum(1 for r in results if r["success"])
        total_requests = len(results)
        success_rate = successful_requests / total_requests
        
        assert success_rate >= 0.8, f"Success rate too low: {success_rate}"
        
        # Check response times
        response_times = [r.get("response_time", 30) for r in results if r["success"]]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            assert avg_response_time < 10, f"Average response time too high: {avg_response_time}"
    
    def test_admin_endpoints(self):
        """Test admin endpoints (if admin token provided)"""
        if not self.config.admin_token:
            pytest.skip("Admin token not provided")
        
        # Test admin health endpoint
        response = self.session.get(f"{self.config.base_url}/api/admin/health")
        
        if response.status_code == 200:
            data = response.json()
            assert "status" in data
            assert "components" in data
        elif response.status_code == 401:
            pytest.skip("Admin authentication failed")
        else:
            pytest.fail(f"Unexpected status code: {response.status_code}")
    
    def test_data_persistence(self):
        """Test data persistence across requests"""
        # Create conversation
        chat_payload = {
            "query": "What is a copyright?",
            "session_id": self.test_session_id,
            "max_results": 3
        }
        
        response1 = self.session.post(
            f"{self.config.base_url}/api/chat",
            json=chat_payload
        )
        assert response1.status_code == 200
        
        # Wait a bit
        time.sleep(2)
        
        # Retrieve conversation history
        response2 = self.session.get(
            f"{self.config.base_url}/api/conversations/{self.test_session_id}"
        )
        assert response2.status_code == 200
        
        data = response2.json()
        assert len(data["conversations"]) >= 1
        assert data["conversations"][0]["query"] == chat_payload["query"]
    
    def test_response_format_consistency(self):
        """Test response format consistency"""
        queries = [
            "What is trademark law?",
            "Explain patent protection",
            "Define copyright infringement"
        ]
        
        for query in queries:
            chat_payload = {
                "query": query,
                "session_id": self.test_session_id,
                "max_results": 3
            }
            
            response = self.session.post(
                f"{self.config.base_url}/api/chat",
                json=chat_payload
            )
            
            assert response.status_code == 200
            
            data = response.json()
            
            # Check required fields
            required_fields = ["answer", "sources", "session_id", "query_id"]
            for field in required_fields:
                assert field in data, f"Missing field: {field}"
            
            # Check data types
            assert isinstance(data["answer"], str)
            assert isinstance(data["sources"], list)
            assert isinstance(data["session_id"], str)
            assert isinstance(data["query_id"], str)
            
            # Check source format
            for source in data["sources"]:
                assert "document_id" in source
                assert "title" in source
                assert "score" in source
                assert isinstance(source["score"], (int, float))

class PerformanceTests:
    """Performance-specific tests"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.session = requests.Session()
    
    def test_response_time_requirements(self):
        """Test response time requirements"""
        response_times = []
        
        for i in range(10):
            start_time = time.time()
            
            response = self.session.get(f"{self.config.base_url}/health")
            
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
            
            assert response.status_code == 200
        
        # Calculate statistics
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        p95_response_time = sorted(response_times)[int(0.95 * len(response_times))]
        
        # Assert requirements
        assert avg_response_time < 1.0, f"Average response time too high: {avg_response_time}"
        assert max_response_time < 5.0, f"Max response time too high: {max_response_time}"
        assert p95_response_time < 2.0, f"95th percentile response time too high: {p95_response_time}"
    
    def test_throughput_requirements(self):
        """Test throughput requirements"""
        def make_health_request():
            try:
                response = self.session.get(f"{self.config.base_url}/health", timeout=10)
                return response.status_code == 200
            except Exception:
                return False
        
        # Measure throughput over 30 seconds
        start_time = time.time()
        end_time = start_time + 30
        successful_requests = 0
        total_requests = 0
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            while time.time() < end_time:
                futures = [executor.submit(make_health_request) for _ in range(10)]
                results = [future.result() for future in futures]
                
                successful_requests += sum(results)
                total_requests += len(results)
                
                time.sleep(0.1)  # Small delay between batches
        
        actual_duration = time.time() - start_time
        throughput = successful_requests / actual_duration
        
        # Assert minimum throughput (adjust based on requirements)
        assert throughput >= 50, f"Throughput too low: {throughput} requests/second"

def run_e2e_tests():
    """Run all E2E tests"""
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description="Production E2E Tests")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL")
    parser.add_argument("--admin-token", help="Admin token")
    parser.add_argument("--timeout", type=int, default=30, help="Request timeout")
    parser.add_argument("--concurrent-users", type=int, default=10, help="Concurrent users")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    config = TestConfig(
        base_url=args.base_url,
        admin_token=args.admin_token,
        timeout=args.timeout,
        concurrent_users=args.concurrent_users
    )
    
    # Run tests
    test_suite = ProductionE2ETests(config)
    performance_suite = PerformanceTests(config)
    
    test_methods = [
        method for method in dir(test_suite)
        if method.startswith('test_') and callable(getattr(test_suite, method))
    ]
    
    performance_methods = [
        method for method in dir(performance_suite)
        if method.startswith('test_') and callable(getattr(performance_suite, method))
    ]
    
    passed = 0
    failed = 0
    
    print(f"Running {len(test_methods)} functional tests...")
    
    for method_name in test_methods:
        try:
            test_suite.setup_method()
            method = getattr(test_suite, method_name)
            method()
            test_suite.teardown_method()
            print(f"✓ {method_name}")
            passed += 1
        except Exception as e:
            print(f"✗ {method_name}: {e}")
            failed += 1
    
    print(f"\nRunning {len(performance_methods)} performance tests...")
    
    for method_name in performance_methods:
        try:
            method = getattr(performance_suite, method_name)
            method()
            print(f"✓ {method_name}")
            passed += 1
        except Exception as e:
            print(f"✗ {method_name}: {e}")
            failed += 1
    
    print(f"\nResults: {passed} passed, {failed} failed")
    
    if failed > 0:
        sys.exit(1)
    else:
        print("All tests passed!")
        sys.exit(0)

if __name__ == "__main__":
    run_e2e_tests()
