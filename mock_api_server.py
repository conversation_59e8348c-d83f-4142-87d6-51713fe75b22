#!/usr/bin/env python3
"""
Mock API Server for Admin Dashboard Testing
This creates a simple FastAPI server that mimics the expected endpoints
to demonstrate the network connectivity fix.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uvicorn
import random

app = FastAPI(title="Mock RAG Chatbot API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock data
MOCK_SOURCES = [
    {"id": "1", "name": "Legal Documents", "type": "pdf", "status": "active", "last_updated": "2024-01-15T10:30:00Z"},
    {"id": "2", "name": "Case Studies", "type": "web", "status": "active", "last_updated": "2024-01-14T15:45:00Z"},
    {"id": "3", "name": "Regulations", "type": "database", "status": "processing", "last_updated": "2024-01-13T09:20:00Z"},
]

@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/admin/health")
async def admin_health():
    """Admin health check with system status"""
    return {
        "status": "healthy",
        "services": {
            "database": "healthy",
            "vector_store": "healthy",
            "cache": "healthy"
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/system/status")
async def system_status():
    """System status endpoint"""
    return {
        "status": "healthy",
        "uptime": "2 days, 14 hours",
        "memory_usage": 65.2,
        "cpu_usage": 23.8,
        "disk_usage": 45.1,
        "active_connections": 12,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/system/metrics")
async def system_metrics():
    """System metrics endpoint"""
    return {
        "cpu_usage": random.uniform(20, 80),
        "memory_usage": random.uniform(50, 90),
        "disk_usage": random.uniform(30, 70),
        "network_io": {
            "bytes_sent": random.randint(1000000, 10000000),
            "bytes_received": random.randint(500000, 5000000)
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/sources")
async def list_sources():
    """List all data sources"""
    return MOCK_SOURCES

@app.post("/admin/sources")
async def create_source(source_data: dict):
    """Create a new data source"""
    # Generate a new ID
    new_id = str(len(MOCK_SOURCES) + 1)

    # Create new source with mock data
    new_source = {
        "id": new_id,
        "name": source_data.get("name", "New Source"),
        "source_type": source_data.get("source_type", "website"),
        "url": source_data.get("url"),
        "path": source_data.get("path"),
        "crawl_depth": source_data.get("crawl_depth", 2),
        "enabled": source_data.get("enabled", True),
        "status": "pending",
        "last_processed": None,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "metadata": source_data.get("metadata", {})
    }

    # Add to mock sources
    MOCK_SOURCES.append(new_source)

    return new_source

@app.get("/admin/sources/summary")
async def sources_summary():
    """Sources summary for dashboard"""
    return {
        "total_sources": len(MOCK_SOURCES),
        "active_sources": len([s for s in MOCK_SOURCES if s["status"] == "active"]),
        "processing_sources": len([s for s in MOCK_SOURCES if s["status"] == "processing"]),
        "last_updated": datetime.now().isoformat()
    }

@app.get("/admin/analytics/summary")
async def usage_summary():
    """Usage analytics summary"""
    return {
        "total_queries_today": random.randint(150, 500),
        "total_sessions_today": random.randint(25, 75),
        "avg_response_time": random.uniform(800, 1500),
        "success_rate": random.uniform(95, 99.5),
        "total_documents": random.randint(1000, 5000),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/analytics/queries")
async def query_analytics(days: int = 7):
    """Query analytics for specified days"""
    return {
        "total_queries": random.randint(1000, 5000),
        "avg_queries_per_day": random.randint(100, 800),
        "peak_hour": "14:00",
        "most_common_topics": ["contracts", "regulations", "compliance"],
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/analytics/sessions")
async def session_analytics(days: int = 7):
    """Session analytics for specified days"""
    return {
        "total_sessions": random.randint(200, 1000),
        "avg_session_duration": random.uniform(300, 900),
        "unique_users": random.randint(50, 200),
        "bounce_rate": random.uniform(10, 30),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/analytics/performance")
async def performance_analytics(days: int = 7):
    """Performance analytics for specified days"""
    return {
        "avg_response_time": random.uniform(800, 1500),
        "p95_response_time": random.uniform(1500, 3000),
        "error_rate": random.uniform(0.5, 2.0),
        "cache_hit_rate": random.uniform(80, 95),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/admin/pipeline/tasks/active")
async def active_tasks():
    """Get active pipeline tasks"""
    return {
        "active_tasks": [
            {
                "id": "task_123",
                "type": "indexing",
                "status": "running",
                "progress": random.randint(10, 90),
                "started_at": datetime.now().isoformat()
            }
        ]
    }

@app.get("/admin/pipeline/tasks/history")
async def task_history(limit: int = 100):
    """Get pipeline task history"""
    return {
        "task_history": [
            {
                "id": "task_122",
                "type": "indexing",
                "status": "completed",
                "progress": 100,
                "started_at": "2024-01-15T10:00:00Z",
                "completed_at": "2024-01-15T10:30:00Z"
            },
            {
                "id": "task_121",
                "type": "crawling",
                "status": "completed",
                "progress": 100,
                "started_at": "2024-01-15T09:00:00Z",
                "completed_at": "2024-01-15T09:45:00Z"
            }
        ]
    }

@app.get("/admin/metrics")
async def legacy_metrics():
    """Legacy metrics endpoint"""
    return {
        "system_health": "healthy",
        "total_queries": random.randint(1000, 5000),
        "active_sessions": random.randint(10, 50),
        "response_time": random.uniform(800, 1500),
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("🚀 Starting Mock API Server for Admin Dashboard Testing")
    print("📊 This server provides mock data to demonstrate network connectivity fixes")
    print("🌐 Server will be available at: http://localhost:8000")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("✅ All admin dashboard endpoints are mocked with realistic data")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000, 
        log_level="info",
        reload=False
    )
