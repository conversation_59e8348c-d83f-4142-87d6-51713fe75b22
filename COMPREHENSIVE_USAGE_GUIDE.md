# 🚀 RAG Chatbot - Comprehensive Usage Guide

## Overview

This guide provides step-by-step instructions for using your RAG chatbot application effectively, covering data ingestion, API usage, testing, and monitoring.

## 📋 Prerequisites

Ensure your Docker environment is running:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

Verify services are healthy:
```bash
python test_infrastructure.py
```

## 🔄 1. Data Ingestion Process

### 1.1 Understanding the Data Flow

The RAG system follows this workflow:
1. **Source Configuration** → URLs/PDFs defined in `sources.yaml`
2. **Web Crawling** → Crawl4AI processes websites
3. **Document Processing** → Text extraction and chunking
4. **Embedding Generation** → Gemini creates vector representations
5. **Storage** → Vectors stored in Milvus, metadata in SQLite

### 1.2 Configure Data Sources

Edit the configuration file:
```bash
nano chatbot-engine/config/sources.yaml
```

Add your URLs:
```yaml
websites:
  - url: "https://example-legal-site.com"
    name: "Example Legal Site"
    crawl_depth: 2
    enabled: true
    
  - url: "https://another-legal-resource.com"
    name: "Legal Resource Database"
    crawl_depth: 3
    enabled: true

local_pdfs:
  - path: "./chatbot-engine/data/sources/legal_document.pdf"
    name: "Legal Document Collection"
    enabled: true
```

### 1.3 Trigger Data Ingestion

**Option A: Using Admin API (Recommended)**
```bash
# Start the API server first
cd chatbot-engine
uvicorn src.online_pipeline.api.main:app --reload --host 0.0.0.0 --port 8000

# In another terminal, trigger reindexing
curl -X POST "http://localhost:8000/admin/reindex" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{"source_ids": null, "force": false}'
```

**Option B: Using Admin API to Add Individual Sources**
```bash
curl -X POST "http://localhost:8000/admin/sources" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "name": "New Legal Website",
    "source_type": "website",
    "url": "https://new-legal-site.com",
    "crawl_depth": 2,
    "enabled": true,
    "metadata": {"category": "legislation"}
  }'
```

### 1.4 Monitor Ingestion Progress

Check processing status:
```bash
# View logs
docker-compose -f docker-compose.dev.yml logs -f api

# Check admin metrics
curl -X GET "http://localhost:8000/admin/metrics" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 🗣️ 2. Chat API Usage

### 2.1 Available Endpoints

| Endpoint | Purpose | Method |
|----------|---------|---------|
| `/chat/query` | Complete response | POST |
| `/chat/stream` | Streaming response | POST |
| `/chat/sessions` | Create session | POST |
| `/chat/regenerate` | Regenerate response | POST |

### 2.2 Basic Chat Query

```bash
curl -X POST "http://localhost:8000/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are the requirements for forming a company in Germany?",
    "session_id": "user_session_123",
    "max_results": 5,
    "include_sources": true
  }'
```

**Response:**
```json
{
  "answer": "To form a company in Germany, you need to meet several requirements...",
  "sources": [
    {
      "document_id": "doc_123",
      "title": "German Company Law",
      "excerpt": "Relevant text excerpt...",
      "score": 0.95,
      "metadata": {
        "source_url": "https://example.com/company-law",
        "document_type": "legislation"
      }
    }
  ],
  "session_id": "user_session_123",
  "processing_time": 1.25,
  "metadata": {
    "total_chunks_searched": 150,
    "embedding_time": 0.1,
    "retrieval_time": 0.3,
    "generation_time": 0.85
  }
}
```

### 2.3 Streaming Chat

```bash
curl -X POST "http://localhost:8000/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Explain trademark registration process",
    "session_id": "user_session_123"
  }'
```

**Response (Server-Sent Events):**
```
data: {"type": "start", "session_id": "user_session_123"}

data: {"type": "chunk", "content": "Trademark registration"}

data: {"type": "chunk", "content": " involves several steps..."}

data: {"type": "sources", "sources": [...]}

data: {"type": "end"}

data: [DONE]
```

### 2.4 Session Management

Create a new session:
```bash
curl -X POST "http://localhost:8000/chat/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "metadata": {
      "user_type": "legal_professional",
      "jurisdiction": "germany"
    }
  }'
```

## 🧪 3. Testing and Validation

### 3.1 Test Infrastructure

```bash
# Test all services
python test_infrastructure.py

# Test API endpoints
python test_api.py
```

### 3.2 Validate Knowledge Graph

Check if data is properly indexed:
```bash
# Check collection stats
curl -X GET "http://localhost:8000/admin/metrics" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Test search functionality
curl -X POST "http://localhost:8000/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "test query about legal concepts",
    "max_results": 3
  }'
```

### 3.3 Good Test URLs for Validation

Use these reliable legal resources for testing:
```yaml
# Add to sources.yaml for testing
websites:
  - url: "https://www.gesetze-im-internet.de/gg/"
    name: "German Basic Law (Test)"
    crawl_depth: 1
    enabled: true
    
  - url: "https://eur-lex.europa.eu/homepage.html"
    name: "EU Law Database (Test)"
    crawl_depth: 1
    enabled: true
```

## 📊 4. Monitoring and Maintenance

### 4.1 Health Checks

```bash
# Basic health check
curl http://localhost:8000/health

# Detailed health check (admin)
curl -X GET "http://localhost:8000/admin/health" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 4.2 View System Metrics

```bash
# Get system statistics
curl -X GET "http://localhost:8000/admin/stats" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Check data sources status
curl -X GET "http://localhost:8000/admin/sources" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 4.3 Monitor Crawling Progress

```bash
# View real-time logs
docker-compose -f docker-compose.dev.yml logs -f worker

# Check processing operations
curl -X GET "http://localhost:8000/admin/operations" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 🔧 5. Configuration and Customization

### 5.1 Adjust Processing Settings

Edit `chatbot-engine/config/sources.yaml`:
```yaml
processing_settings:
  chunk_size: 1000          # Adjust for your content
  chunk_overlap: 200        # Overlap between chunks
  min_chunk_size: 100       # Minimum chunk size
  max_chunk_size: 2000      # Maximum chunk size
```

### 5.2 Crawling Configuration

```yaml
crawl_settings:
  delay_between_requests: 1.0    # Be respectful to servers
  max_concurrent_requests: 5     # Parallel requests
  timeout: 30                    # Request timeout
  respect_robots_txt: true       # Follow robots.txt
```

## 🚨 6. Troubleshooting

### 6.1 Common Issues

**No responses from chat:**
```bash
# Check if data is indexed
curl -X GET "http://localhost:8000/admin/metrics"

# Verify vector store has data
docker-compose logs milvus-standalone
```

**Crawling failures:**
```bash
# Check crawler logs
docker-compose logs worker

# Test individual URL
curl -I https://your-target-url.com
```

**API errors:**
```bash
# Check API logs
docker-compose logs api

# Verify environment variables
docker-compose exec api env | grep -E "(GOOGLE|COHERE)"
```

### 6.2 Reset and Restart

```bash
# Reset all data
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d

# Trigger fresh indexing
curl -X POST "http://localhost:8000/admin/reindex" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{"force": true}'
```

## 🔗 7. API Documentation

- **Interactive Docs:** http://localhost:8000/docs
- **ReDoc:** http://localhost:8000/redoc
- **OpenAPI JSON:** http://localhost:8000/openapi.json

## 📞 Next Steps

1. **Add your legal data sources** to `sources.yaml`
2. **Trigger initial indexing** using the admin API
3. **Test chat functionality** with legal queries
4. **Monitor system performance** using health endpoints
5. **Customize settings** based on your requirements

## 🛠️ 8. Quick Start Scripts

I've created several helper scripts to make using your RAG chatbot easier:

### 8.1 Complete Setup and Test
```bash
# Run complete setup and validation
python setup_and_test.py
```

This script will:
- Set up environment variables
- Start Docker services
- Test infrastructure
- Add sample data sources
- Trigger indexing
- Test chat functionality

### 8.2 API Usage Examples
```bash
# Run comprehensive API examples
python examples/api_usage_examples.py
```

This demonstrates:
- Basic chat queries
- Data source management
- Streaming responses
- System monitoring
- Test queries

### 8.3 Knowledge Graph Validation
```bash
# Test knowledge graph and data ingestion
python test_knowledge_graph.py

# Add a test source and validate
python test_knowledge_graph.py --add-test-source
```

### 8.4 Infrastructure Testing
```bash
# Test all infrastructure components
python test_infrastructure.py
```

## 🔑 9. Authentication Setup

### 9.1 Admin Token Configuration

Edit your `.env` file:
```bash
# Set your admin token
ADMIN_API_KEY=your_secure_admin_token_here

# Set other required keys
GOOGLE_API_KEY=your_google_ai_api_key
COHERE_API_KEY=your_cohere_api_key
SECRET_KEY=your_secret_key
```

### 9.2 Using Admin Token in Requests
```bash
# Example admin request
curl -X GET "http://localhost:8000/admin/sources" \
  -H "Authorization: Bearer your_secure_admin_token_here"
```

## 📈 10. Performance Optimization

### 10.1 Adjust Chunk Settings
```yaml
# In config/sources.yaml
processing_settings:
  chunk_size: 1500          # Larger chunks for better context
  chunk_overlap: 300        # More overlap for continuity
  min_chunk_size: 200       # Minimum meaningful size
  max_chunk_size: 3000      # Maximum for performance
```

### 10.2 Crawling Optimization
```yaml
crawl_settings:
  delay_between_requests: 0.5    # Faster crawling (be respectful)
  max_concurrent_requests: 10    # More parallel requests
  timeout: 45                    # Longer timeout for slow sites
```

## 🚨 11. Common Issues and Solutions

### 11.1 "No sources found" in chat responses
**Solution:**
```bash
# Check if sources are added
curl -X GET "http://localhost:8000/admin/sources" \
  -H "Authorization: Bearer your_admin_token"

# Trigger reindexing
curl -X POST "http://localhost:8000/admin/reindex" \
  -H "Authorization: Bearer your_admin_token" \
  -d '{"force": true}'
```

### 11.2 "Empty knowledge graph" error
**Solution:**
```bash
# Check system metrics
curl -X GET "http://localhost:8000/admin/metrics" \
  -H "Authorization: Bearer your_admin_token"

# Add test source and reindex
python test_knowledge_graph.py --add-test-source
```

### 11.3 API server not responding
**Solution:**
```bash
# Check Docker services
docker-compose -f docker-compose.dev.yml ps

# Restart services
docker-compose -f docker-compose.dev.yml restart

# Check logs
docker-compose -f docker-compose.dev.yml logs -f api
```

## 📊 12. Monitoring Dashboard

### 12.1 Key Metrics to Monitor
```bash
# System health
curl http://localhost:8000/admin/health

# Document statistics
curl http://localhost:8000/admin/metrics

# Processing operations
curl http://localhost:8000/admin/operations
```

### 12.2 Log Monitoring
```bash
# API logs
docker-compose -f docker-compose.dev.yml logs -f api

# Worker logs (for indexing)
docker-compose -f docker-compose.dev.yml logs -f worker

# All services
docker-compose -f docker-compose.dev.yml logs -f
```

Your RAG chatbot is now ready for production use! 🎉

## 📞 Support and Resources

- **Setup Script:** `python setup_and_test.py`
- **API Examples:** `python examples/api_usage_examples.py`
- **Knowledge Test:** `python test_knowledge_graph.py`
- **Infrastructure Test:** `python test_infrastructure.py`
- **Documentation:** http://localhost:8000/docs
