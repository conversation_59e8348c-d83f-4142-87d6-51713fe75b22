apiVersion: apps/v1
kind: Deployment
metadata:
  name: etcd
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: etcd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: etcd
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: etcd
    spec:
      containers:
      - name: etcd
        image: quay.io/coreos/etcd:v3.5.5
        ports:
        - containerPort: 2379
        - containerPort: 2380
        env:
        - name: ETCD_AUTO_COMPACTION_MODE
          value: "revision"
        - name: ETCD_AUTO_COMPACTION_RETENTION
          value: "1000"
        - name: ETCD_QUOTA_BACKEND_BYTES
          value: "4294967296"
        - name: ETCD_SNAPSHOT_COUNT
          value: "50000"
        command:
        - etcd
        - -advertise-client-urls=http://127.0.0.1:2379
        - -listen-client-urls=http://0.0.0.0:2379
        - --data-dir=/etcd
        volumeMounts:
        - name: etcd-data
          mountPath: /etcd
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - etcdctl
            - endpoint
            - health
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - etcdctl
            - endpoint
            - health
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: etcd-data
        persistentVolumeClaim:
          claimName: etcd-data-pvc
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: etcd-service
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: etcd
spec:
  selector:
    app: rag-legal-chatbot
    component: etcd
  ports:
  - name: client
    port: 2379
    targetPort: 2379
  - name: peer
    port: 2380
    targetPort: 2380
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: minio
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: minio
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: minio
    spec:
      containers:
      - name: minio
        image: minio/minio:RELEASE.2023-03-20T20-16-18Z
        ports:
        - containerPort: 9000
        - containerPort: 9001
        env:
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: MINIO_SECRET_KEY
        command:
        - minio
        - server
        - /data
        - --console-address
        - ":9001"
        volumeMounts:
        - name: minio-data
          mountPath: /data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: minio-data
        persistentVolumeClaim:
          claimName: minio-data-pvc
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: minio-service
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: minio
spec:
  selector:
    app: rag-legal-chatbot
    component: minio
  ports:
  - name: api
    port: 9000
    targetPort: 9000
  - name: console
    port: 9001
    targetPort: 9001
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: milvus
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: milvus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: milvus
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: milvus
    spec:
      containers:
      - name: milvus
        image: milvusdb/milvus:v2.3.0
        ports:
        - containerPort: 19530
        - containerPort: 9091
        env:
        - name: ETCD_ENDPOINTS
          value: "etcd-service:2379"
        - name: MINIO_ADDRESS
          value: "minio-service:9000"
        command:
        - milvus
        - run
        - standalone
        volumeMounts:
        - name: milvus-data
          mountPath: /var/lib/milvus
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 9091
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /healthz
            port: 9091
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: milvus-data
        persistentVolumeClaim:
          claimName: milvus-data-pvc
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: milvus-service
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: milvus
spec:
  selector:
    app: rag-legal-chatbot
    component: milvus
  ports:
  - name: grpc
    port: 19530
    targetPort: 19530
  - name: http
    port: 9091
    targetPort: 9091
  type: ClusterIP
