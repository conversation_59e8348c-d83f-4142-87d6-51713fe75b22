apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-chatbot-worker
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: worker
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: worker
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: worker
        version: v1
      annotations:
        prometheus.io/scrape: "false"
    spec:
      imagePullSecrets:
      - name: registry-secret
      containers:
      - name: worker
        image: rag-chatbot-worker:latest
        imagePullPolicy: Always
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: ENVIRONMENT
        - name: PYTHONPATH
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: PYTHONPATH
        - name: MILVUS_HOST
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: MILVUS_HOST
        - name: MILVUS_PORT
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: MILVUS_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: REDIS_PORT
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: CELERY_RESULT_BACKEND
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: GOOGLE_API_KEY
        - name: COHERE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: COHERE_API_KEY
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: SECRET_KEY
        - name: C_FORCE_ROOT
          value: "1"
        volumeMounts:
        - name: app-logs
          mountPath: /app/chatbot-engine/logs
        - name: app-data
          mountPath: /app/chatbot-engine/data
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          exec:
            command:
            - celery
            - -A
            - chatbot-engine.src.shared.celery_app
            - inspect
            - ping
            - -d
            - celery@$(hostname)
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 30
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - celery
            - -A
            - chatbot-engine.src.shared.celery_app
            - inspect
            - ping
            - -d
            - celery@$(hostname)
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 20
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1002
          runAsGroup: 1002
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: app-logs
        persistentVolumeClaim:
          claimName: app-logs-pvc
      - name: app-data
        persistentVolumeClaim:
          claimName: app-data-pvc
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      restartPolicy: Always
      securityContext:
        fsGroup: 1002
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: component
                  operator: In
                  values:
                  - worker
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data-pvc
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: data
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rag-chatbot-worker-hpa
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rag-chatbot-worker
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 20
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Max
