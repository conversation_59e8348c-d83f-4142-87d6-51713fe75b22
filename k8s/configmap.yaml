apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-chatbot-config
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: config
data:
  # Application configuration
  ENVIRONMENT: "production"
  PYTHONPATH: "/app"
  PYTHONDONTWRITEBYTECODE: "1"
  PYTHONUNBUFFERED: "1"
  
  # Database configuration
  MILVUS_HOST: "milvus-service"
  MILVUS_PORT: "19530"
  MILVUS_COLLECTION_NAME: "legal_docs_v1"
  
  # Redis configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # API configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_WORKERS: "1"
  
  # Celery configuration
  CELERY_BROKER_URL: "redis://redis-service:6379/0"
  CELERY_RESULT_BACKEND: "redis://redis-service:6379/0"
  CELERY_WORKER_CONCURRENCY: "2"
  
  # Monitoring
  ENABLE_METRICS: "true"
  METRICS_PORT: "9090"
  
  # Logging
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"
  
  # Security
  CORS_ORIGINS: "https://yourdomain.com"
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_REQUESTS_PER_MINUTE: "30"
  
  # Performance
  MAX_CONCURRENT_REQUESTS: "100"
  REQUEST_TIMEOUT: "60"
  
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: nginx
data:
  nginx.conf: |
    worker_processes auto;
    worker_rlimit_nofile 65535;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 4096;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        upstream api_backend {
            least_conn;
            server rag-chatbot-api-service:8000 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api_limit:10m rate=20r/s;
        limit_req_zone $binary_remote_addr zone=admin_limit:10m rate=5r/s;
        limit_req_zone $binary_remote_addr zone=chat_limit:10m rate=10r/s;
        
        # Connection limiting
        limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

        # Performance settings
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        server_tokens off;
        client_max_body_size 10M;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';

        access_log /var/log/nginx/access.log main;

        server {
            listen 80;
            server_name _;

            # Health check
            location /health {
                limit_req zone=api_limit burst=10 nodelay;
                proxy_pass http://api_backend/health;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # API endpoints
            location /api/ {
                limit_req zone=api_limit burst=30 nodelay;
                limit_conn conn_limit_per_ip 10;
                
                proxy_pass http://api_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 10s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }

            # Chat streaming
            location /chat/stream {
                limit_req zone=chat_limit burst=15 nodelay;
                limit_conn conn_limit_per_ip 5;
                
                proxy_pass http://api_backend/chat/stream;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_buffering off;
                proxy_cache off;
                proxy_read_timeout 600s;
                proxy_send_timeout 600s;
                proxy_set_header Connection '';
                proxy_http_version 1.1;
            }

            # Admin endpoints
            location /admin/ {
                limit_req zone=admin_limit burst=5 nodelay;
                limit_conn conn_limit_per_ip 3;
                
                proxy_pass http://api_backend/admin/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Metrics (internal only)
            location /metrics {
                allow 10.0.0.0/8;
                allow **********/12;
                allow ***********/16;
                deny all;
                
                proxy_pass http://api_backend/metrics;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location / {
                return 404;
            }
        }
    }
