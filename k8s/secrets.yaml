apiVersion: v1
kind: Secret
metadata:
  name: rag-chatbot-secrets
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: secrets
type: Opaque
data:
  # Base64 encoded secrets - replace with actual values
  # To encode: echo -n "your-secret" | base64
  GOOGLE_API_KEY: "dGVzdF9nb29nbGVfa2V5"  # test_google_key
  COHERE_API_KEY: "dGVzdF9jb2hlcmVfa2V5"  # test_cohere_key
  SECRET_KEY: "dGVzdF9zZWNyZXRfa2V5X2NoYW5nZV9pbl9wcm9kdWN0aW9u"  # test_secret_key_change_in_production
  ADMIN_API_KEY: "dGVzdF9hZG1pbl9hcGlfa2V5"  # test_admin_api_key
  REDIS_PASSWORD: ""  # Empty for development, set in production
  MINIO_ACCESS_KEY: "bWluaW9hZG1pbg=="  # minioadmin
  MINIO_SECRET_KEY: "bWluaW9hZG1pbg=="  # minioadmin
---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: tls
type: kubernetes.io/tls
data:
  # Replace with actual TLS certificate and key
  # To generate self-signed for testing:
  # openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout tls.key -out tls.crt -subj "/CN=rag-chatbot.local"
  # kubectl create secret tls tls-secret --cert=tls.crt --key=tls.key -n rag-chatbot --dry-run=client -o yaml
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...  # Base64 encoded certificate
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...  # Base64 encoded private key
---
apiVersion: v1
kind: Secret
metadata:
  name: registry-secret
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: registry
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry credentials for private images
  # To create: kubectl create secret docker-registry registry-secret --docker-server=your-registry --docker-username=user --docker-password=pass --docker-email=email -n rag-chatbot --dry-run=client -o yaml
  .dockerconfigjson: eyJhdXRocyI6e319  # Empty auth for public images
