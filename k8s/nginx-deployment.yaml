apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-proxy
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: nginx
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: nginx
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: nginx
        version: v1
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
          name: http
        - containerPort: 443
          name: https
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: nginx-logs
          mountPath: /var/log/nginx
        - name: tls-certs
          mountPath: /etc/nginx/ssl
          readOnly: true
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 101
          runAsGroup: 101
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
      - name: nginx-logs
        emptyDir: {}
      - name: tls-certs
        secret:
          secretName: tls-secret
      restartPolicy: Always
      securityContext:
        fsGroup: 101
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: component
                  operator: In
                  values:
                  - nginx
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: nginx
spec:
  selector:
    app: rag-legal-chatbot
    component: nginx
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  type: LoadBalancer
  sessionAffinity: None
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rag-chatbot-ingress
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/rate-limit: "20"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://yourdomain.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - rag-chatbot.yourdomain.com
    secretName: rag-chatbot-tls
  rules:
  - host: rag-chatbot.yourdomain.com
    http:
      paths:
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: rag-chatbot-api-service
            port:
              number: 8000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: rag-chatbot-api-service
            port:
              number: 8000
      - path: /chat
        pathType: Prefix
        backend:
          service:
            name: rag-chatbot-api-service
            port:
              number: 8000
      - path: /admin
        pathType: Prefix
        backend:
          service:
            name: rag-chatbot-api-service
            port:
              number: 8000
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: rag-chatbot-api-service
            port:
              number: 8000
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: rag-chatbot-network-policy
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
spec:
  podSelector:
    matchLabels:
      app: rag-legal-chatbot
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 19530
    - protocol: TCP
      port: 9091
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  - to:
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
    ports:
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 19530
    - protocol: TCP
      port: 9091
    - protocol: TCP
      port: 2379
    - protocol: TCP
      port: 9000
