# Kubernetes Deployment Guide for RAG Legal Chatbot

This directory contains comprehensive Kubernetes manifests for deploying the RAG Legal Chatbot system in production environments.

## 📋 Overview

The deployment includes:
- **API Service**: FastAPI application with auto-scaling
- **Worker Service**: Celery workers for background processing
- **Milvus**: Vector database with etcd and MinIO
- **Redis**: Caching and message broker
- **Nginx**: Load balancer and reverse proxy
- **Monitoring**: Prometheus metrics and health checks
- **Security**: Network policies, RBAC, and TLS

## 🏗️ Architecture

```
Internet → Ingress → Nginx → API Service → Milvus/Redis
                           → Worker Service → Milvus/Redis
```

## 📁 File Structure

```
k8s/
├── namespace.yaml              # Namespace and resource quotas
├── secrets.yaml               # Secrets for API keys and TLS
├── configmap.yaml             # Configuration and nginx config
├── persistent-volumes.yaml    # Storage for data persistence
├── redis-deployment.yaml      # Redis cache and message broker
├── milvus-deployment.yaml     # Milvus vector database stack
├── api-deployment.yaml        # FastAPI application
├── worker-deployment.yaml     # Celery workers
├── nginx-deployment.yaml      # Load balancer and ingress
├── deploy.sh                  # Automated deployment script
└── README.md                  # This documentation
```

## 🚀 Quick Start

### Prerequisites

1. **Kubernetes Cluster**: v1.20+ with at least 8GB RAM and 4 CPU cores
2. **kubectl**: Configured to access your cluster
3. **Storage Class**: `fast-ssd` and `standard` storage classes available
4. **Ingress Controller**: Nginx ingress controller (optional)
5. **Cert Manager**: For automatic TLS certificates (optional)

### 1. Prepare Secrets

Edit `secrets.yaml` and replace the base64 encoded values with your actual secrets:

```bash
# Encode your secrets
echo -n "your-google-api-key" | base64
echo -n "your-cohere-api-key" | base64
echo -n "your-secret-key" | base64
```

### 2. Deploy the Application

```bash
# Make the deployment script executable
chmod +x deploy.sh

# Deploy everything
./deploy.sh deploy

# Or deploy manually step by step
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f configmap.yaml
kubectl apply -f persistent-volumes.yaml
kubectl apply -f redis-deployment.yaml
kubectl apply -f milvus-deployment.yaml
kubectl apply -f api-deployment.yaml
kubectl apply -f worker-deployment.yaml
kubectl apply -f nginx-deployment.yaml
```

### 3. Verify Deployment

```bash
# Check deployment status
./deploy.sh verify

# Or manually check
kubectl get pods -n rag-chatbot
kubectl get services -n rag-chatbot
kubectl get ingress -n rag-chatbot
```

### 4. Access the Application

```bash
# Get external IP
kubectl get service nginx-service -n rag-chatbot

# Port forward for local access
kubectl port-forward service/nginx-service 8080:80 -n rag-chatbot

# Access the application
curl http://localhost:8080/health
```

## 🔧 Configuration

### Environment Variables

Key configuration is managed through ConfigMaps and Secrets:

- **ConfigMap**: Non-sensitive configuration
- **Secrets**: API keys, passwords, and certificates

### Resource Limits

Default resource allocations:

| Component | CPU Request | Memory Request | CPU Limit | Memory Limit |
|-----------|-------------|----------------|-----------|--------------|
| API       | 500m        | 1Gi            | 1         | 2Gi          |
| Worker    | 500m        | 1Gi            | 1         | 2Gi          |
| Milvus    | 1           | 2Gi            | 2         | 4Gi          |
| Redis     | 250m        | 512Mi          | 500m      | 1Gi          |
| Nginx     | 100m        | 128Mi          | 200m      | 256Mi        |

### Auto-scaling

Horizontal Pod Autoscaler (HPA) is configured for:

- **API**: 2-10 replicas based on CPU (70%) and memory (80%)
- **Worker**: 1-5 replicas based on CPU (80%) and memory (85%)

## 🔒 Security

### Network Policies

- Ingress: Only from ingress controller and monitoring
- Egress: DNS, HTTPS, and internal service communication
- Pod-to-pod: Only within the same namespace

### Security Context

- Non-root containers
- Read-only root filesystem
- Dropped capabilities
- Security profiles (seccomp)

### TLS/SSL

- TLS termination at ingress
- Automatic certificate management with cert-manager
- Secure headers and HSTS

## 📊 Monitoring

### Health Checks

- **Liveness Probes**: Detect and restart unhealthy containers
- **Readiness Probes**: Control traffic routing to healthy pods
- **Startup Probes**: Handle slow-starting containers

### Metrics

- Prometheus metrics exposed on `/metrics`
- Application-specific metrics
- Infrastructure metrics

## 🗄️ Storage

### Persistent Volumes

- **Milvus Data**: 50Gi for vector storage
- **Redis Data**: 10Gi for cache persistence
- **Application Logs**: 5Gi for log storage
- **Application Data**: 10Gi for shared data

### Backup Strategy

- Regular snapshots of persistent volumes
- Database backups to external storage
- Configuration backup

## 🔄 Operations

### Scaling

```bash
# Scale API horizontally
kubectl scale deployment/rag-chatbot-api --replicas=5 -n rag-chatbot

# Scale workers
kubectl scale deployment/rag-chatbot-worker --replicas=3 -n rag-chatbot
```

### Updates

```bash
# Rolling update
kubectl set image deployment/rag-chatbot-api api=rag-chatbot-api:v2.0 -n rag-chatbot

# Check rollout status
kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot

# Rollback if needed
kubectl rollout undo deployment/rag-chatbot-api -n rag-chatbot
```

### Logs

```bash
# View API logs
kubectl logs -f deployment/rag-chatbot-api -n rag-chatbot

# View worker logs
kubectl logs -f deployment/rag-chatbot-worker -n rag-chatbot

# View all logs
kubectl logs -f -l app=rag-legal-chatbot -n rag-chatbot
```

### Debugging

```bash
# Get pod details
kubectl describe pod <pod-name> -n rag-chatbot

# Execute commands in pod
kubectl exec -it <pod-name> -n rag-chatbot -- /bin/bash

# Check events
kubectl get events -n rag-chatbot --sort-by='.lastTimestamp'
```

## 🧹 Cleanup

```bash
# Remove entire deployment
./deploy.sh cleanup

# Or manually
kubectl delete namespace rag-chatbot
```

## 🔧 Troubleshooting

### Common Issues

1. **Pods Stuck in Pending**
   - Check resource quotas and limits
   - Verify storage class availability
   - Check node capacity

2. **ImagePullBackOff**
   - Verify image names and tags
   - Check registry credentials
   - Ensure network connectivity

3. **CrashLoopBackOff**
   - Check application logs
   - Verify environment variables
   - Check health check endpoints

4. **Service Unavailable**
   - Verify service selectors
   - Check pod labels
   - Ensure pods are ready

### Performance Tuning

1. **Resource Optimization**
   - Monitor actual resource usage
   - Adjust requests and limits
   - Optimize JVM/Python settings

2. **Network Optimization**
   - Use service mesh for advanced routing
   - Implement connection pooling
   - Optimize DNS resolution

3. **Storage Optimization**
   - Use appropriate storage classes
   - Implement data lifecycle policies
   - Monitor I/O performance

## 📚 Additional Resources

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Nginx Ingress Controller](https://kubernetes.github.io/ingress-nginx/)
- [Cert Manager](https://cert-manager.io/)
- [Prometheus Operator](https://prometheus-operator.dev/)

## 🆘 Support

For deployment issues:
1. Check the troubleshooting section
2. Review application logs
3. Verify configuration
4. Check cluster resources
