apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-chatbot-api
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rag-legal-chatbot
      component: api
  template:
    metadata:
      labels:
        app: rag-legal-chatbot
        component: api
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      imagePullSecrets:
      - name: registry-secret
      containers:
      - name: api
        image: rag-chatbot-api:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: ENVIRONMENT
        - name: PYTHONPATH
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: PYTHONPATH
        - name: MILVUS_HOST
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: MILVUS_HOST
        - name: MILVUS_PORT
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: MILVUS_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: rag-chatbot-config
              key: REDIS_PORT
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: GOOGLE_API_KEY
        - name: COHERE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: COHERE_API_KEY
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: SECRET_KEY
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-chatbot-secrets
              key: ADMIN_API_KEY
        volumeMounts:
        - name: app-logs
          mountPath: /app/chatbot-engine/logs
        - name: app-data
          mountPath: /app/chatbot-engine/data
          readOnly: true
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 12
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: app-logs
        persistentVolumeClaim:
          claimName: app-logs-pvc
      - name: app-data
        configMap:
          name: rag-chatbot-config
      - name: tmp
        emptyDir:
          sizeLimit: 100Mi
      restartPolicy: Always
      securityContext:
        fsGroup: 1001
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: component
                  operator: In
                  values:
                  - api
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: rag-chatbot-api-service
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: rag-legal-chatbot
    component: api
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
  sessionAffinity: None
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rag-chatbot-api-hpa
  namespace: rag-chatbot
  labels:
    app: rag-legal-chatbot
    component: api
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rag-chatbot-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
