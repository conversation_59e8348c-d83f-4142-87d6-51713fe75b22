#!/bin/bash
# Kubernetes deployment script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="rag-chatbot"
KUBECTL_TIMEOUT="300s"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed or not in PATH"
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    # Check if namespace exists
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        warn "Namespace $NAMESPACE already exists"
    fi
    
    log "Prerequisites check completed"
}

# Create namespace and basic resources
create_namespace() {
    log "Creating namespace and basic resources..."
    kubectl apply -f namespace.yaml
    log "Namespace created successfully"
}

# Deploy secrets
deploy_secrets() {
    log "Deploying secrets..."
    
    # Check if secrets file exists
    if [ ! -f "secrets.yaml" ]; then
        error "secrets.yaml not found. Please create it with your actual secrets."
    fi
    
    kubectl apply -f secrets.yaml
    log "Secrets deployed successfully"
}

# Deploy ConfigMaps
deploy_configmaps() {
    log "Deploying ConfigMaps..."
    kubectl apply -f configmap.yaml
    log "ConfigMaps deployed successfully"
}

# Deploy persistent volumes
deploy_storage() {
    log "Deploying persistent volumes..."
    kubectl apply -f persistent-volumes.yaml
    
    # Wait for PVCs to be bound
    log "Waiting for PVCs to be bound..."
    kubectl wait --for=condition=Bound pvc --all -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    log "Storage deployed successfully"
}

# Deploy infrastructure services (Redis, Milvus)
deploy_infrastructure() {
    log "Deploying infrastructure services..."
    
    # Deploy Redis
    kubectl apply -f redis-deployment.yaml
    log "Redis deployment created"
    
    # Deploy Milvus components
    kubectl apply -f milvus-deployment.yaml
    log "Milvus deployment created"
    
    # Wait for infrastructure to be ready
    log "Waiting for infrastructure services to be ready..."
    kubectl wait --for=condition=available deployment/redis -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    kubectl wait --for=condition=available deployment/etcd -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    kubectl wait --for=condition=available deployment/minio -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    kubectl wait --for=condition=available deployment/milvus -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    
    log "Infrastructure services deployed successfully"
}

# Deploy application services
deploy_application() {
    log "Deploying application services..."
    
    # Deploy API
    kubectl apply -f api-deployment.yaml
    log "API deployment created"
    
    # Deploy Worker
    kubectl apply -f worker-deployment.yaml
    log "Worker deployment created"
    
    # Wait for application services to be ready
    log "Waiting for application services to be ready..."
    kubectl wait --for=condition=available deployment/rag-chatbot-api -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    kubectl wait --for=condition=available deployment/rag-chatbot-worker -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    
    log "Application services deployed successfully"
}

# Deploy ingress and load balancer
deploy_ingress() {
    log "Deploying ingress and load balancer..."
    kubectl apply -f nginx-deployment.yaml
    
    # Wait for nginx to be ready
    kubectl wait --for=condition=available deployment/nginx-proxy -n "$NAMESPACE" --timeout="$KUBECTL_TIMEOUT"
    
    log "Ingress deployed successfully"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check all pods are running
    info "Checking pod status..."
    kubectl get pods -n "$NAMESPACE"
    
    # Check services
    info "Checking services..."
    kubectl get services -n "$NAMESPACE"
    
    # Check ingress
    info "Checking ingress..."
    kubectl get ingress -n "$NAMESPACE"
    
    # Check HPA
    info "Checking HPA..."
    kubectl get hpa -n "$NAMESPACE"
    
    # Health check
    info "Performing health check..."
    if kubectl get pods -n "$NAMESPACE" | grep -q "Running"; then
        log "Deployment verification completed successfully"
    else
        warn "Some pods may not be running. Please check the status."
    fi
}

# Get deployment info
get_deployment_info() {
    log "Getting deployment information..."
    
    # Get external IP
    EXTERNAL_IP=$(kubectl get service nginx-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "Pending")
    
    info "Deployment Information:"
    echo "=========================="
    echo "Namespace: $NAMESPACE"
    echo "External IP: $EXTERNAL_IP"
    echo "Health Check: http://$EXTERNAL_IP/health"
    echo "API Endpoint: http://$EXTERNAL_IP/api"
    echo "Admin Panel: http://$EXTERNAL_IP/admin"
    echo ""
    echo "To access the application:"
    echo "kubectl port-forward service/nginx-service 8080:80 -n $NAMESPACE"
    echo "Then visit: http://localhost:8080"
    echo ""
    echo "To view logs:"
    echo "kubectl logs -f deployment/rag-chatbot-api -n $NAMESPACE"
    echo "kubectl logs -f deployment/rag-chatbot-worker -n $NAMESPACE"
    echo ""
    echo "To scale the application:"
    echo "kubectl scale deployment/rag-chatbot-api --replicas=5 -n $NAMESPACE"
    echo "=========================="
}

# Cleanup function
cleanup() {
    log "Cleaning up deployment..."
    kubectl delete namespace "$NAMESPACE" --ignore-not-found=true
    log "Cleanup completed"
}

# Main deployment function
deploy() {
    log "Starting RAG Legal Chatbot deployment..."
    
    check_prerequisites
    create_namespace
    deploy_secrets
    deploy_configmaps
    deploy_storage
    deploy_infrastructure
    deploy_application
    deploy_ingress
    verify_deployment
    get_deployment_info
    
    log "RAG Legal Chatbot deployment completed successfully!"
}

# Script usage
usage() {
    echo "Usage: $0 [deploy|cleanup|verify|info]"
    echo ""
    echo "Commands:"
    echo "  deploy   - Deploy the entire application"
    echo "  cleanup  - Remove the entire deployment"
    echo "  verify   - Verify the current deployment"
    echo "  info     - Get deployment information"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 cleanup"
    echo "  $0 verify"
}

# Main script logic
case "${1:-deploy}" in
    deploy)
        deploy
        ;;
    cleanup)
        cleanup
        ;;
    verify)
        verify_deployment
        ;;
    info)
        get_deployment_info
        ;;
    *)
        usage
        exit 1
        ;;
esac
