# RAG Legal Chatbot Engine

An Advanced Legal Chatbot Engine with Retrieval-Augmented Generation (RAG) capabilities specifically designed for the German legal domain.

## Overview

This project implements a sophisticated chatbot system that provides accurate, context-aware, and citable answers to legal questions by combining structured knowledge graph principles with advanced search and ranking techniques.

### Key Features

- **Hybrid Architecture**: Combines offline data ingestion with real-time query processing
- **Multi-Modal Document Support**: Processes HTML content from legal websites and PDF documents
- **Advanced Retrieval**: Hybrid search combining vector similarity and keyword matching
- **Intelligent Reranking**: Uses Cohere Rerank for optimal document relevance
- **Citation Support**: Provides proper source citations for all responses
- **Streaming Responses**: Real-time response generation with FastAPI streaming
- **Administrative Interface**: CRUD operations for data source management

## Architecture

The system consists of two main pipelines:

1. **Offline Pipeline (Data Ingestion)**: Background processing for data collection, processing, and storage
2. **Online Pipeline (Real-Time Query)**: Live API for handling user queries and generating responses

## Technology Stack

- **Framework**: LangChain with LangChain Expression Language (LCEL)
- **Vector Database**: Milvus for scalable vector storage
- **LLM**: Gemini 1.5 Flash (fine-tuned for legal domain)
- **Embedding Model**: Gemini text-embedding-004
- **Web Crawler**: Crawl4AI with JavaScript rendering support
- **Reranker**: Cohere Rerank for relevance optimization
- **API Framework**: FastAPI with streaming capabilities

## Project Structure

```
chatbot-engine/
├── src/
│   ├── offline_pipeline/     # Data ingestion and processing
│   ├── online_pipeline/      # Real-time query processing
│   ├── admin_interface/      # Administrative endpoints
│   └── shared/              # Shared utilities and models
├── config/                  # Configuration files
├── data/                    # Data storage and processing
├── tests/                   # Test suites
├── scripts/                 # Utility scripts
├── docker/                  # Docker configurations
└── documents/              # Project documentation
```

## Quick Start

1. **Environment Setup**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configuration**
   - Copy `.env.example` to `.env`
   - Add your API keys for Google AI and Cohere
   - Configure Milvus connection settings

3. **Start Services**
   ```bash
   docker-compose up -d  # Start Milvus and Redis
   ```

4. **Run the Application**
   ```bash
   uvicorn src.online_pipeline.api.main:app --reload
   ```

## Development Phases

- [x] **Phase 1**: Project Setup & Infrastructure
- [ ] **Phase 2**: Offline Pipeline Implementation
- [ ] **Phase 3**: Online Pipeline Implementation
- [ ] **Phase 4**: Admin Interface & Management
- [ ] **Phase 5**: Testing & Quality Assurance
- [ ] **Phase 6**: Deployment & Production Setup

## Documentation

Detailed documentation is available in the `documents/` directory:

- [Development Plan](chatbot-engine/documents/Development_Plan.md)
- [Technical Requirements](chatbot-engine/documents/Technical_Requirements.md)
- [Architecture Overview](chatbot-engine/documents/Architecture_Overview.md)
- [Developer Documentation](chatbot-engine/documents/Developer_Documentation.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
