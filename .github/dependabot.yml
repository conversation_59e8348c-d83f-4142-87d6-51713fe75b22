version: 2
updates:
  # Python dependencies
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "backend-team"
    assignees:
      - "security-team"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "fastapi"
        update-types: ["version-update:semver-major"]
      - dependency-name: "langchain*"
        update-types: ["version-update:semver-major"]

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/chatbot-engine/docker"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "devops-team"
    commit-message:
      prefix: "docker"
    labels:
      - "dependencies"
      - "docker"

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "devops-team"
    commit-message:
      prefix: "ci"
    labels:
      - "dependencies"
      - "github-actions"
