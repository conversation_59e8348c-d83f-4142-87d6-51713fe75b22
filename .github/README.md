# CI/CD Pipeline Documentation

This directory contains the complete CI/CD pipeline configuration for the RAG Legal Chatbot project.

## 📋 Overview

Our CI/CD pipeline provides:
- **Continuous Integration**: Automated testing, code quality checks, and security scanning
- **Continuous Deployment**: Automated deployment to staging and production environments
- **Security**: Comprehensive security scanning and vulnerability management
- **Quality Gates**: Automated quality checks before deployment
- **Release Management**: Automated release creation and artifact management

## 🔄 Workflows

### 1. Continuous Integration (`ci.yml`)

**Triggers**: Push to main/develop, Pull Requests

**Jobs**:
- **Code Quality**: Black formatting, Flake8 linting, Pylint, MyPy type checking
- **Security Scanning**: Bandit security scan, Safety dependency check
- **Testing**: Unit tests, integration tests across Python 3.9-3.11
- **Docker Build**: Multi-stage builds with security scanning
- **E2E Tests**: End-to-end testing in containerized environment
- **Performance Tests**: Load testing with Locust
- **Quality Gate**: Aggregated pass/fail decision

**Artifacts**:
- Test coverage reports
- Security scan results
- Performance test results
- Docker images (for testing)

### 2. Continuous Deployment (`cd.yml`)

**Triggers**: Push to main, Git tags, Manual dispatch

**Jobs**:
- **Build & Push**: Multi-platform Docker images to GitHub Container Registry
- **Security Scan**: Container vulnerability scanning with Trivy
- **Deploy Staging**: Automated deployment to staging environment
- **Deploy Production**: Deployment to production (tags only)
- **Post-deployment**: Health monitoring and validation

**Environments**:
- **Staging**: Automatic deployment from main branch
- **Production**: Manual approval required, tag-based deployment

### 3. Security Scanning (`security.yml`)

**Triggers**: Daily schedule, Push to main, Pull Requests

**Jobs**:
- **CodeQL**: Static analysis for security vulnerabilities
- **Dependency Scan**: Safety, Bandit, Semgrep scanning
- **Container Scan**: Trivy and Grype vulnerability scanning
- **Infrastructure Scan**: Checkov and Terrascan for IaC security
- **Secrets Scan**: TruffleHog and GitLeaks for exposed secrets
- **License Scan**: License compliance checking

### 4. Release Management (`release.yml`)

**Triggers**: Git tags, Manual dispatch

**Jobs**:
- **Create Release**: Automated GitHub release creation
- **Build Artifacts**: Source and deployment packages
- **Update Documentation**: Version updates in documentation
- **Notification**: Stakeholder notifications

## 🔧 Configuration

### Environment Variables

Set these secrets in GitHub repository settings:

```bash
# API Keys (for testing)
GOOGLE_API_KEY_TEST=your_test_google_api_key
COHERE_API_KEY_TEST=your_test_cohere_api_key

# Kubernetes Configuration
KUBE_CONFIG_STAGING=base64_encoded_kubeconfig_for_staging
KUBE_CONFIG_PRODUCTION=base64_encoded_kubeconfig_for_production

# Container Registry
GITHUB_TOKEN=automatically_provided_by_github

# Notifications (optional)
SLACK_WEBHOOK_URL=your_slack_webhook_url
TEAMS_WEBHOOK_URL=your_teams_webhook_url
```

### Branch Protection Rules

Configure these branch protection rules for `main`:

- [x] Require a pull request before merging
- [x] Require approvals (minimum 2)
- [x] Dismiss stale PR approvals when new commits are pushed
- [x] Require review from code owners
- [x] Require status checks to pass before merging
  - [x] Code Quality & Security
  - [x] Tests (3.9, 3.10, 3.11)
  - [x] Docker Build & Scan
- [x] Require branches to be up to date before merging
- [x] Require conversation resolution before merging
- [x] Restrict pushes that create files larger than 100MB

### Required Status Checks

- `code-quality`
- `test (3.9)`
- `test (3.10)`
- `test (3.11)`
- `docker-build`
- `quality-gate`

## 🚀 Deployment Process

### Staging Deployment

1. **Automatic**: Every push to `main` branch
2. **Process**:
   - Build and push Docker images
   - Security scanning
   - Deploy to staging Kubernetes cluster
   - Run smoke tests
   - Update deployment status

### Production Deployment

1. **Trigger**: Create a git tag (e.g., `v1.0.0`)
2. **Process**:
   - All staging checks must pass
   - Manual approval required
   - Pre-deployment backup
   - Rolling deployment to production
   - Post-deployment verification
   - 10-minute monitoring period
   - Automatic rollback on failure

### Manual Deployment

Use workflow dispatch for emergency deployments:

```bash
# Via GitHub UI
Actions → Continuous Deployment → Run workflow

# Via GitHub CLI
gh workflow run cd.yml -f environment=production -f force_deploy=true
```

## 🔍 Quality Gates

### Code Quality Requirements

- **Black**: Code formatting compliance
- **Flake8**: Basic linting (E9, F63, F7, F82 errors)
- **Pylint**: Advanced linting (minimum score: 8.0/10)
- **MyPy**: Type checking compliance

### Test Requirements

- **Unit Tests**: Minimum 80% code coverage
- **Integration Tests**: All critical paths covered
- **E2E Tests**: Core user journeys validated
- **Performance Tests**: Response time < 2s (95th percentile)

### Security Requirements

- **No Critical Vulnerabilities**: In dependencies or containers
- **Security Scan**: All scans must pass
- **Secrets**: No exposed secrets in code
- **License Compliance**: No GPL/AGPL licenses

## 📊 Monitoring & Alerts

### Build Notifications

- **Success**: Slack notification to #deployments
- **Failure**: Slack notification to #alerts with details
- **Security Issues**: Immediate notification to security team

### Deployment Monitoring

- **Health Checks**: Automated endpoint monitoring
- **Performance**: Response time and error rate tracking
- **Rollback**: Automatic rollback on health check failures

## 🛠️ Troubleshooting

### Common Issues

1. **Test Failures**
   ```bash
   # Run tests locally
   pytest tests/ -v --cov=chatbot-engine/src
   ```

2. **Docker Build Failures**
   ```bash
   # Test Docker build locally
   docker build -f chatbot-engine/docker/Dockerfile.api .
   ```

3. **Security Scan Failures**
   ```bash
   # Run security scans locally
   bandit -r chatbot-engine/src/
   safety check
   ```

4. **Deployment Failures**
   ```bash
   # Check Kubernetes status
   kubectl get pods -n rag-chatbot
   kubectl logs -f deployment/rag-chatbot-api -n rag-chatbot
   ```

### Debug Mode

Enable debug mode by setting `ACTIONS_STEP_DEBUG=true` in repository secrets.

### Manual Intervention

For emergency fixes:

1. **Skip CI**: Add `[skip ci]` to commit message
2. **Force Deploy**: Use `force_deploy=true` in workflow dispatch
3. **Emergency Rollback**: Use previous deployment backup

## 📚 Best Practices

### Commit Messages

Follow conventional commits:
```
feat: add new chat endpoint
fix: resolve memory leak in worker
docs: update API documentation
ci: improve test coverage reporting
```

### Pull Requests

- Use the provided PR template
- Ensure all checks pass before requesting review
- Include tests for new features
- Update documentation as needed

### Releases

- Use semantic versioning (v1.2.3)
- Include comprehensive changelog
- Test in staging before production release
- Coordinate with stakeholders for major releases

## 🔗 Related Documentation

- [Deployment Guide](../k8s/README.md)
- [Monitoring Setup](../monitoring/README.md)
- [Security Guidelines](../docs/SECURITY.md)
- [Contributing Guide](../CONTRIBUTING.md)
