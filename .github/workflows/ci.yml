name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Python dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install black flake8 pylint mypy bandit safety

      - name: Code formatting check (Black)
        run: black --check --diff .

      - name: <PERSON><PERSON> (Flake8)
        run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics

      - name: Advanced linting (Pylint)
        run: pylint chatbot-engine/src/ --fail-under=8.0

      - name: Type checking (MyPy)
        run: mypy chatbot-engine/src/ --ignore-missing-imports

      - name: Security scan (Bandit)
        run: bandit -r chatbot-engine/src/ -f json -o bandit-report.json

      - name: Dependency vulnerability scan (Safety)
        run: safety check --json --output safety-report.json

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            bandit-report.json
            safety-report.json

  # Unit and Integration Tests
  test:
    name: Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache Python dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('**/requirements*.txt') }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio pytest-mock

      - name: Set up test environment
        run: |
          cp .env.example .env
          echo "REDIS_HOST=localhost" >> .env
          echo "REDIS_PORT=6379" >> .env
          echo "GOOGLE_API_KEY=test_key" >> .env
          echo "COHERE_API_KEY=test_key" >> .env

      - name: Run unit tests
        run: |
          pytest tests/unit/ -v --cov=chatbot-engine/src --cov-report=xml --cov-report=html

      - name: Run integration tests
        run: |
          pytest tests/integration/ -v --cov=chatbot-engine/src --cov-append --cov-report=xml

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

      - name: Upload test artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.python-version }}
          path: |
            htmlcov/
            coverage.xml
            pytest-report.xml

  # Docker Build and Security Scan
  docker-build:
    name: Docker Build & Scan
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build API image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./chatbot-engine/docker/Dockerfile.api
          target: production
          tags: rag-chatbot-api:${{ github.sha }}
          load: true
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build Worker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./chatbot-engine/docker/Dockerfile.worker
          target: production
          tags: rag-chatbot-worker:${{ github.sha }}
          load: true
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Run Trivy vulnerability scanner (API)
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rag-chatbot-api:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-api-results.sarif'

      - name: Run Trivy vulnerability scanner (Worker)
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rag-chatbot-worker:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-worker-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-api-results.sarif'

      - name: Test container startup
        run: |
          # Test API container
          docker run -d --name test-api \
            -e GOOGLE_API_KEY=test \
            -e COHERE_API_KEY=test \
            -e REDIS_HOST=localhost \
            rag-chatbot-api:${{ github.sha }}
          
          # Wait for startup
          sleep 30
          
          # Check if container is running
          if ! docker ps | grep test-api; then
            echo "API container failed to start"
            docker logs test-api
            exit 1
          fi
          
          docker stop test-api
          docker rm test-api

  # End-to-End Tests
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Compose
        run: |
          sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
          sudo chmod +x /usr/local/bin/docker-compose

      - name: Start test environment
        run: |
          cp .env.example .env
          echo "GOOGLE_API_KEY=${{ secrets.GOOGLE_API_KEY_TEST }}" >> .env
          echo "COHERE_API_KEY=${{ secrets.COHERE_API_KEY_TEST }}" >> .env
          docker-compose -f docker-compose.test.yml up -d

      - name: Wait for services
        run: |
          timeout 300 bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'

      - name: Run E2E tests
        run: |
          pytest tests/e2e/ -v --tb=short

      - name: Collect logs
        if: always()
        run: |
          mkdir -p logs
          docker-compose -f docker-compose.test.yml logs > logs/docker-compose.log

      - name: Upload E2E artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-artifacts
          path: |
            logs/
            screenshots/

      - name: Cleanup test environment
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml down -v

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install performance testing tools
        run: |
          pip install locust pytest-benchmark

      - name: Start test environment
        run: |
          cp .env.example .env
          echo "GOOGLE_API_KEY=${{ secrets.GOOGLE_API_KEY_TEST }}" >> .env
          echo "COHERE_API_KEY=${{ secrets.COHERE_API_KEY_TEST }}" >> .env
          docker-compose -f docker-compose.test.yml up -d

      - name: Wait for services
        run: |
          timeout 300 bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'

      - name: Run performance tests
        run: |
          python tests/performance/test_load_testing.py --headless --users 10 --spawn-rate 2 --run-time 60s

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-results
          path: |
            performance-report.html
            performance-stats.json

      - name: Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml down -v

  # Quality Gate
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [code-quality, test, docker-build]
    if: always()
    
    steps:
      - name: Check job results
        run: |
          echo "Code Quality: ${{ needs.code-quality.result }}"
          echo "Tests: ${{ needs.test.result }}"
          echo "Docker Build: ${{ needs.docker-build.result }}"
          
          if [[ "${{ needs.code-quality.result }}" != "success" ]]; then
            echo "❌ Code quality checks failed"
            exit 1
          fi
          
          if [[ "${{ needs.test.result }}" != "success" ]]; then
            echo "❌ Tests failed"
            exit 1
          fi
          
          if [[ "${{ needs.docker-build.result }}" != "success" ]]; then
            echo "❌ Docker build failed"
            exit 1
          fi
          
          echo "✅ All quality gates passed"

      - name: Post status to PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const { owner, repo, number } = context.issue;
            await github.rest.issues.createComment({
              owner,
              repo,
              issue_number: number,
              body: '✅ All CI checks passed! Ready for review.'
            });

  # Notification
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [quality-gate, e2e-tests, performance-tests]
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
      - name: Notify on success
        if: needs.quality-gate.result == 'success'
        run: |
          echo "🎉 CI pipeline completed successfully!"
          # Add Slack/Teams notification here

      - name: Notify on failure
        if: needs.quality-gate.result == 'failure'
        run: |
          echo "❌ CI pipeline failed!"
          # Add Slack/Teams notification here
