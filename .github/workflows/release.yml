name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string
      prerelease:
        description: 'Mark as pre-release'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  packages: write

jobs:
  # Create GitHub Release
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      release-id: ${{ steps.create-release.outputs.id }}
      upload-url: ${{ steps.create-release.outputs.upload_url }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate changelog
        id: changelog
        run: |
          # Get the latest tag
          LATEST_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          if [ -z "$LATEST_TAG" ]; then
            echo "No previous tag found, generating changelog from first commit"
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" --reverse)
          else
            echo "Generating changelog since $LATEST_TAG"
            CHANGELOG=$(git log ${LATEST_TAG}..HEAD --pretty=format:"- %s (%h)" --reverse)
          fi
          
          # Save changelog to file
          echo "$CHANGELOG" > CHANGELOG.md
          
          # Set output for GitHub Actions
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create-release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name || github.event.inputs.version }}
          release_name: Release ${{ github.ref_name || github.event.inputs.version }}
          body: |
            ## What's Changed
            
            ${{ steps.changelog.outputs.changelog }}
            
            ## Docker Images
            
            - API: `ghcr.io/${{ github.repository }}-api:${{ github.ref_name || github.event.inputs.version }}`
            - Worker: `ghcr.io/${{ github.repository }}-worker:${{ github.ref_name || github.event.inputs.version }}`
            
            ## Installation
            
            ```bash
            # Download and extract
            curl -L https://github.com/${{ github.repository }}/archive/${{ github.ref_name || github.event.inputs.version }}.tar.gz | tar xz
            
            # Or clone specific tag
            git clone --branch ${{ github.ref_name || github.event.inputs.version }} https://github.com/${{ github.repository }}.git
            ```
            
            ## Deployment
            
            See [deployment documentation](./k8s/README.md) for Kubernetes deployment instructions.
            
            ## Full Changelog
            
            **Full Changelog**: https://github.com/${{ github.repository }}/compare/${{ steps.changelog.outputs.previous-tag }}...${{ github.ref_name || github.event.inputs.version }}
          draft: false
          prerelease: ${{ github.event.inputs.prerelease || false }}

  # Build and attach release artifacts
  build-artifacts:
    name: Build Release Artifacts
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Create source distribution
        run: |
          # Create source archive
          git archive --format=tar.gz --prefix=rag-chatbot-${{ github.ref_name }}/ HEAD > rag-chatbot-${{ github.ref_name }}-source.tar.gz
          
          # Create deployment package
          mkdir -p deployment-package
          cp -r k8s/ deployment-package/
          cp -r monitoring/ deployment-package/
          cp -r scripts/ deployment-package/
          cp docker-compose.prod.yml deployment-package/
          cp .env.production deployment-package/.env.example
          cp README.md deployment-package/
          
          tar -czf rag-chatbot-${{ github.ref_name }}-deployment.tar.gz -C deployment-package .

      - name: Generate checksums
        run: |
          sha256sum rag-chatbot-${{ github.ref_name }}-source.tar.gz > checksums.txt
          sha256sum rag-chatbot-${{ github.ref_name }}-deployment.tar.gz >> checksums.txt

      - name: Upload source archive
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload-url }}
          asset_path: ./rag-chatbot-${{ github.ref_name }}-source.tar.gz
          asset_name: rag-chatbot-${{ github.ref_name }}-source.tar.gz
          asset_content_type: application/gzip

      - name: Upload deployment package
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload-url }}
          asset_path: ./rag-chatbot-${{ github.ref_name }}-deployment.tar.gz
          asset_name: rag-chatbot-${{ github.ref_name }}-deployment.tar.gz
          asset_content_type: application/gzip

      - name: Upload checksums
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload-url }}
          asset_path: ./checksums.txt
          asset_name: checksums.txt
          asset_content_type: text/plain

  # Update documentation
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Update version in documentation
        run: |
          # Update version in README
          sed -i "s/version: .*/version: ${{ github.ref_name }}/g" README.md
          
          # Update version in Kubernetes manifests
          find k8s/ -name "*.yaml" -exec sed -i "s/rag-chatbot-api:latest/rag-chatbot-api:${{ github.ref_name }}/g" {} \;
          find k8s/ -name "*.yaml" -exec sed -i "s/rag-chatbot-worker:latest/rag-chatbot-worker:${{ github.ref_name }}/g" {} \;

      - name: Commit version updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          git commit -m "Update version to ${{ github.ref_name }}" || exit 0
          git push

  # Notify stakeholders
  notify:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [create-release, build-artifacts, update-docs]
    if: always()
    
    steps:
      - name: Notify success
        if: needs.create-release.result == 'success'
        run: |
          echo "🎉 Release ${{ github.ref_name }} created successfully!"
          echo "Release URL: https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}"
          # Add Slack/Teams/Email notification here

      - name: Notify failure
        if: needs.create-release.result == 'failure'
        run: |
          echo "❌ Release creation failed!"
          # Add Slack/Teams/Email notification here
