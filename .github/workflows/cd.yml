name: Continuous Deployment

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: 'Force deployment (skip some checks)'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Build and Push Docker Images
  build-and-push:
    name: Build & Push Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push API image
        id: build-api
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./chatbot-engine/docker/Dockerfile.api
          target: production
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Build and push Worker image
        id: build-worker
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./chatbot-engine/docker/Dockerfile.worker
          target: production
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-worker:${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:${{ steps.meta.outputs.tags }}
          format: spdx-json
          output-file: sbom-api.spdx.json

      - name: Upload SBOM
        uses: actions/upload-artifact@v3
        with:
          name: sbom
          path: sbom-api.spdx.json

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-and-push
    permissions:
      security-events: write
    
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:${{ needs.build-and-push.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Check for critical vulnerabilities
        run: |
          CRITICAL_COUNT=$(cat trivy-results.sarif | jq '.runs[0].results | map(select(.level == "error")) | length')
          if [ "$CRITICAL_COUNT" -gt 0 ] && [ "${{ github.event.inputs.force_deploy }}" != "true" ]; then
            echo "❌ Found $CRITICAL_COUNT critical vulnerabilities. Deployment blocked."
            exit 1
          fi
          echo "✅ Security scan passed or force deployment enabled."

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan]
    environment: staging
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl for staging
        run: |
          echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Deploy to staging
        run: |
          export KUBECONFIG=kubeconfig
          
          # Update image tags in Kubernetes manifests
          sed -i "s|rag-chatbot-api:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:${{ needs.build-and-push.outputs.image-tag }}|g" k8s/api-deployment.yaml
          sed -i "s|rag-chatbot-worker:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-worker:${{ needs.build-and-push.outputs.image-tag }}|g" k8s/worker-deployment.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f k8s/namespace.yaml
          kubectl apply -f k8s/configmap.yaml
          kubectl apply -f k8s/secrets.yaml
          kubectl apply -f k8s/persistent-volumes.yaml
          kubectl apply -f k8s/redis-deployment.yaml
          kubectl apply -f k8s/milvus-deployment.yaml
          kubectl apply -f k8s/api-deployment.yaml
          kubectl apply -f k8s/worker-deployment.yaml
          kubectl apply -f k8s/nginx-deployment.yaml
          
          # Wait for rollout
          kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot --timeout=600s
          kubectl rollout status deployment/rag-chatbot-worker -n rag-chatbot --timeout=600s

      - name: Run smoke tests
        run: |
          export KUBECONFIG=kubeconfig
          
          # Get service endpoint
          STAGING_URL=$(kubectl get service nginx-service -n rag-chatbot -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Wait for service to be ready
          timeout 300 bash -c "until curl -f http://$STAGING_URL/health; do sleep 10; done"
          
          # Run basic smoke tests
          curl -f http://$STAGING_URL/health
          curl -f http://$STAGING_URL/api/health

      - name: Update deployment status
        if: always()
        run: |
          if [ $? -eq 0 ]; then
            echo "✅ Staging deployment successful"
          else
            echo "❌ Staging deployment failed"
            exit 1
          fi

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan, deploy-staging]
    environment: production
    if: startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl for production
        run: |
          echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Pre-deployment backup
        run: |
          export KUBECONFIG=kubeconfig
          
          # Create backup of current deployment
          kubectl get deployment rag-chatbot-api -n rag-chatbot -o yaml > api-backup.yaml
          kubectl get deployment rag-chatbot-worker -n rag-chatbot -o yaml > worker-backup.yaml

      - name: Deploy to production
        run: |
          export KUBECONFIG=kubeconfig
          
          # Update image tags
          sed -i "s|rag-chatbot-api:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:${{ needs.build-and-push.outputs.image-tag }}|g" k8s/api-deployment.yaml
          sed -i "s|rag-chatbot-worker:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-worker:${{ needs.build-and-push.outputs.image-tag }}|g" k8s/worker-deployment.yaml
          
          # Rolling deployment
          kubectl apply -f k8s/api-deployment.yaml
          kubectl apply -f k8s/worker-deployment.yaml
          
          # Wait for rollout with longer timeout for production
          kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot --timeout=900s
          kubectl rollout status deployment/rag-chatbot-worker -n rag-chatbot --timeout=900s

      - name: Post-deployment verification
        run: |
          export KUBECONFIG=kubeconfig
          
          # Get production endpoint
          PROD_URL=$(kubectl get service nginx-service -n rag-chatbot -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Comprehensive health checks
          timeout 600 bash -c "until curl -f http://$PROD_URL/health; do sleep 15; done"
          
          # Verify all endpoints
          curl -f http://$PROD_URL/health
          curl -f http://$PROD_URL/api/health
          curl -f http://$PROD_URL/metrics
          
          # Check pod status
          kubectl get pods -n rag-chatbot
          
          # Verify no pods are in error state
          ERROR_PODS=$(kubectl get pods -n rag-chatbot --field-selector=status.phase!=Running --no-headers | wc -l)
          if [ "$ERROR_PODS" -gt 0 ]; then
            echo "❌ Found $ERROR_PODS pods not in Running state"
            kubectl get pods -n rag-chatbot
            exit 1
          fi

      - name: Rollback on failure
        if: failure()
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "🔄 Rolling back production deployment..."
          kubectl apply -f api-backup.yaml
          kubectl apply -f worker-backup.yaml
          kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot --timeout=600s
          kubectl rollout status deployment/rag-chatbot-worker -n rag-chatbot --timeout=600s

      - name: Update deployment status
        if: always()
        run: |
          if [ $? -eq 0 ]; then
            echo "✅ Production deployment successful"
          else
            echo "❌ Production deployment failed"
            exit 1
          fi

  # Post-deployment monitoring
  post-deployment:
    name: Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: success() && (startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production')
    
    steps:
      - name: Monitor deployment
        run: |
          echo "🔍 Monitoring production deployment for 10 minutes..."
          
          # Monitor for 10 minutes
          for i in {1..20}; do
            echo "Check $i/20..."
            
            # Check health endpoint
            if ! curl -f http://$PROD_URL/health; then
              echo "❌ Health check failed at minute $((i/2))"
              exit 1
            fi
            
            sleep 30
          done
          
          echo "✅ Post-deployment monitoring completed successfully"

      - name: Update GitHub deployment status
        uses: actions/github-script@v6
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: context.payload.deployment.id,
              state: 'success',
              description: 'Deployment completed successfully',
              environment_url: 'https://your-production-domain.com'
            });

  # Notification
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production, post-deployment]
    if: always()
    
    steps:
      - name: Notify success
        if: needs.deploy-production.result == 'success'
        run: |
          echo "🎉 Production deployment completed successfully!"
          # Add Slack/Teams/Email notification here

      - name: Notify failure
        if: needs.deploy-production.result == 'failure'
        run: |
          echo "❌ Production deployment failed!"
          # Add Slack/Teams/Email notification here
