---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## Bug Description

A clear and concise description of what the bug is.

## Steps to Reproduce

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior

A clear and concise description of what you expected to happen.

## Actual Behavior

A clear and concise description of what actually happened.

## Screenshots

If applicable, add screenshots to help explain your problem.

## Environment

- **OS**: [e.g. Ubuntu 20.04, macOS 12.0, Windows 11]
- **Python Version**: [e.g. 3.11.0]
- **Docker Version**: [e.g. 20.10.17]
- **Browser**: [e.g. Chrome 108, Firefox 107] (if applicable)
- **Deployment**: [e.g. Docker Compose, Kubernetes, Local]

## Configuration

- **Environment**: [e.g. development, staging, production]
- **API Keys**: [Redacted - just mention which services]
- **Custom Configuration**: [Any non-default settings]

## Logs

```
Paste relevant log output here
```

## Error Messages

```
Paste any error messages here
```

## Additional Context

Add any other context about the problem here.

## Possible Solution

If you have ideas on how to fix this, please describe them here.

## Impact

- [ ] Blocks development
- [ ] Blocks testing
- [ ] Blocks deployment
- [ ] Affects user experience
- [ ] Security concern
- [ ] Performance issue

## Workaround

If you found a temporary workaround, please describe it here.
