# RAG Chatbot Admin Dashboard

A comprehensive administrative dashboard for managing the RAG (Retrieval-Augmented Generation) Legal Chatbot system. Built with Next.js, TypeScript, and shadcn/ui components.

## Features

### 🏠 Dashboard Overview
- Real-time system metrics and health monitoring
- Query statistics and performance indicators
- Quick action buttons for common tasks
- Recent activity feed

### 📁 Data Source Management
- CRUD operations for data sources (Website, PDF, Text files)
- Bulk operations (enable/disable/delete multiple sources)
- Source status monitoring and validation
- Processing status tracking

### 🗄️ Database Management
- Vector database operations and monitoring
- Document chunk browsing and management
- Vector search testing interface
- Storage analytics and performance metrics

### ⚙️ Pipeline Management
- Data processing pipeline control
- Task monitoring and cancellation
- Worker status and resource utilization
- Pipeline history and statistics

### 📊 System Monitoring
- Real-time system health checks
- Performance metrics and charts
- Service status monitoring
- Resource usage tracking

### 💬 Chat Interface
- Interactive chat testing interface
- Response time monitoring
- Source citation display
- Chat session management

### 🔧 Configuration Management
- API settings configuration
- Model parameter tuning
- Search algorithm settings
- Configuration backup and restore

### 📈 Analytics Dashboard
- Query analytics and trends
- Session and user analytics
- Performance analytics
- Usage pattern analysis

## Technology Stack

- **Framework:** Next.js 14 with App Router
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **UI Components:** shadcn/ui
- **State Management:** Zustand
- **Charts:** Recharts
- **Forms:** React Hook Form with Zod validation
- **HTTP Client:** Axios
- **Notifications:** Sonner

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Access to the RAG Chatbot API

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open [http://localhost:3001](http://localhost:3001) in your browser.

### Authentication

The dashboard uses API key authentication. For development:
- Username: `admin`
- API Key: Any non-empty value

For production, configure proper API key validation with the RAG Chatbot API.

## API Integration

The dashboard integrates with the RAG Chatbot API running on `http://localhost:8000` by default. Make sure the RAG Chatbot API server is running before using the dashboard.

### Available Features

- **Data Source Management:** Add, edit, delete, and monitor data sources
- **Pipeline Control:** Start, stop, and monitor data processing pipelines
- **System Monitoring:** Real-time health checks and performance metrics
- **Chat Testing:** Interactive interface to test the RAG system
- **Analytics:** Usage statistics and performance analytics
- **Configuration:** Manage system settings and parameters

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Dashboard pages
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── forms/            # Form components
│   └── layout/           # Layout components
├── lib/                  # Utilities and configurations
│   ├── api/              # API client and services
│   ├── stores/           # Zustand stores
│   ├── types/            # TypeScript types
│   └── utils/            # Utility functions
└── hooks/                # Custom React hooks
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Features

- **Responsive Design:** Mobile-first approach with collapsible sidebar
- **Real-time Updates:** Auto-refresh for active tasks and live metrics
- **Error Handling:** Comprehensive error boundaries and user feedback
- **Form Validation:** Zod schema validation with real-time feedback
- **Data Visualization:** Interactive charts and performance dashboards

## Deployment

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm start
```

The dashboard will be available at [http://localhost:3001](http://localhost:3001).

## License

This project is part of the RAG Chatbot system.
