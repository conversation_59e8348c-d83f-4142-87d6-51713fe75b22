'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { DataSource, SourceType, AdminSourceRequest } from '@/lib/types';
import { sourceService } from '@/lib/api/services';
import { toast } from 'sonner';

const sourceSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200, 'Name too long'),
  source_type: z.nativeEnum(SourceType),
  url: z.string().url('Invalid URL').optional().or(z.literal('')),
  path: z.string().optional(),
  crawl_depth: z.number().min(1).max(10).optional(),
  enabled: z.boolean(),
  metadata: z.record(z.any()).optional(),
});

type SourceFormData = z.infer<typeof sourceSchema>;

interface SourceFormProps {
  source?: DataSource;
  onSuccess: (source: DataSource) => void;
  onCancel: () => void;
}

export function SourceForm({ source, onSuccess, onCancel }: SourceFormProps) {
  const isEditing = !!source;

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<SourceFormData>({
    resolver: zodResolver(sourceSchema),
    defaultValues: {
      name: source?.name || '',
      source_type: source?.source_type || SourceType.WEBSITE,
      url: source?.url || '',
      path: source?.path || '',
      crawl_depth: source?.crawl_depth || 2,
      enabled: source?.enabled ?? true,
      metadata: source?.metadata || {},
    },
  });

  const sourceType = watch('source_type');

  const onSubmit = async (data: SourceFormData) => {
    try {
      const requestData: AdminSourceRequest = {
        name: data.name,
        source_type: data.source_type,
        url: data.url || undefined,
        path: data.path || undefined,
        crawl_depth: data.crawl_depth,
        enabled: data.enabled,
        metadata: data.metadata || {},
      };

      let result: DataSource;
      if (isEditing && source?.id) {
        result = await sourceService.updateSource(source.id, requestData);
        toast.success('Source updated successfully');
      } else {
        result = await sourceService.createSource(requestData);
        toast.success('Source created successfully');
      }

      onSuccess(result);
    } catch (error: any) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} source: ${error.message}`);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Configure the basic settings for your data source
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              placeholder="Enter source name"
              {...register('name')}
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="source_type">Source Type *</Label>
            <Select
              value={sourceType}
              onValueChange={(value) => setValue('source_type', value as SourceType)}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select source type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={SourceType.WEBSITE}>Website</SelectItem>
                <SelectItem value={SourceType.PDF}>PDF Files</SelectItem>
                <SelectItem value={SourceType.TEXT}>Text Files</SelectItem>
              </SelectContent>
            </Select>
            {errors.source_type && (
              <p className="text-sm text-red-600">{errors.source_type.message}</p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="enabled"
              checked={watch('enabled')}
              onCheckedChange={(checked) => setValue('enabled', checked)}
              disabled={isSubmitting}
            />
            <Label htmlFor="enabled">Enable this source</Label>
          </div>
        </CardContent>
      </Card>

      {/* Source Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Source Configuration</CardTitle>
          <CardDescription>
            Configure how to access and process this data source
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {sourceType === SourceType.WEBSITE && (
            <>
              <div className="space-y-2">
                <Label htmlFor="url">Website URL *</Label>
                <Input
                  id="url"
                  type="url"
                  placeholder="https://example.com"
                  {...register('url')}
                  disabled={isSubmitting}
                />
                {errors.url && (
                  <p className="text-sm text-red-600">{errors.url.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="crawl_depth">Crawl Depth</Label>
                <Input
                  id="crawl_depth"
                  type="number"
                  min="1"
                  max="10"
                  placeholder="2"
                  {...register('crawl_depth', { valueAsNumber: true })}
                  disabled={isSubmitting}
                />
                <p className="text-sm text-gray-500">
                  How many levels deep to crawl (1-10)
                </p>
                {errors.crawl_depth && (
                  <p className="text-sm text-red-600">{errors.crawl_depth.message}</p>
                )}
              </div>
            </>
          )}

          {(sourceType === SourceType.PDF || sourceType === SourceType.TEXT) && (
            <div className="space-y-2">
              <Label htmlFor="path">File Path *</Label>
              <Input
                id="path"
                placeholder="/path/to/files/"
                {...register('path')}
                disabled={isSubmitting}
              />
              <p className="text-sm text-gray-500">
                Path to the directory containing your files
              </p>
              {errors.path && (
                <p className="text-sm text-red-600">{errors.path.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Settings</CardTitle>
          <CardDescription>
            Optional metadata and advanced configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="metadata">Metadata (JSON)</Label>
            <Textarea
              id="metadata"
              placeholder='{"category": "legal", "priority": "high"}'
              defaultValue={JSON.stringify(source?.metadata || {}, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value || '{}');
                  setValue('metadata', parsed);
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              disabled={isSubmitting}
              rows={4}
            />
            <p className="text-sm text-gray-500">
              Additional metadata in JSON format (optional)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            isEditing ? 'Update Source' : 'Create Source'
          )}
        </Button>
      </div>
    </form>
  );
}
