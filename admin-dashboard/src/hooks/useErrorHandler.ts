import { useCallback } from 'react';
import { toast } from 'sonner';

interface ErrorHandlerOptions {
  showToast?: boolean;
  toastTitle?: string;
  logError?: boolean;
  onError?: (error: Error) => void;
}

interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: any;
}

export function useErrorHandler() {
  const handleError = useCallback((
    error: Error | ApiError | unknown,
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      showToast = true,
      toastTitle = 'Error',
      logError = true,
      onError,
    } = options;

    // Normalize error
    let normalizedError: ApiError;
    
    if (error instanceof Error) {
      normalizedError = error as ApiError;
    } else if (typeof error === 'string') {
      normalizedError = new Error(error) as ApiError;
    } else {
      normalizedError = new Error('An unknown error occurred') as ApiError;
    }

    // Log error if enabled
    if (logError) {
      console.error('Error handled:', normalizedError);
    }

    // Show toast notification if enabled
    if (showToast) {
      const message = getErrorMessage(normalizedError);
      toast.error(message, {
        description: toastTitle !== 'Error' ? toastTitle : undefined,
      });
    }

    // Call custom error handler if provided
    if (onError) {
      onError(normalizedError);
    }

    return normalizedError;
  }, []);

  const handleAsyncError = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    options: ErrorHandlerOptions = {}
  ): Promise<T | null> => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error, options);
      return null;
    }
  }, [handleError]);

  return {
    handleError,
    handleAsyncError,
  };
}

function getErrorMessage(error: ApiError): string {
  // Handle specific error types
  if (error.status) {
    switch (error.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Internal server error. Please try again later.';
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  // Handle network errors
  if (error.message?.includes('fetch')) {
    return 'Network error. Please check your connection and try again.';
  }

  if (error.message?.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }

  // Handle API-specific error codes
  if (error.code) {
    switch (error.code) {
      case 'VALIDATION_ERROR':
        return 'Please check your input and try again.';
      case 'RATE_LIMIT_EXCEEDED':
        return 'Too many requests. Please wait a moment and try again.';
      case 'INSUFFICIENT_PERMISSIONS':
        return 'You do not have permission to perform this action.';
      case 'RESOURCE_NOT_FOUND':
        return 'The requested resource was not found.';
      case 'SERVICE_UNAVAILABLE':
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  return error.message || 'An unexpected error occurred.';
}

// Hook for handling loading states with error handling
export function useAsyncOperation<T>() {
  const { handleAsyncError } = useErrorHandler();
  
  const execute = useCallback(async (
    operation: () => Promise<T>,
    options: ErrorHandlerOptions & {
      onSuccess?: (result: T) => void;
      onFinally?: () => void;
    } = {}
  ) => {
    const { onSuccess, onFinally, ...errorOptions } = options;
    
    try {
      const result = await handleAsyncError(operation, errorOptions);
      if (result !== null && onSuccess) {
        onSuccess(result);
      }
      return result;
    } finally {
      if (onFinally) {
        onFinally();
      }
    }
  }, [handleAsyncError]);

  return { execute };
}

// Hook for retry logic
export function useRetry() {
  const { handleError } = useErrorHandler();

  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000,
    backoff: boolean = true
  ): Promise<T | null> => {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxAttempts) {
          handleError(lastError, {
            toastTitle: `Failed after ${maxAttempts} attempts`,
          });
          break;
        }

        // Wait before retrying
        const waitTime = backoff ? delay * Math.pow(2, attempt - 1) : delay;
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    return null;
  }, [handleError]);

  return { retry };
}

// Hook for form validation errors
export function useFormErrorHandler() {
  const handleValidationErrors = useCallback((errors: Record<string, string[]>) => {
    const errorMessages = Object.entries(errors)
      .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
      .join('\n');

    toast.error('Validation failed', {
      description: errorMessages,
    });
  }, []);

  return { handleValidationErrors };
}
