import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useAuthStore } from '@/lib/stores/auth';

// Mock the auth store
jest.mock('@/lib/stores/auth');
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    pathname: '/dashboard',
  }),
  usePathname: () => '/dashboard',
}));

// Mock API services
jest.mock('@/lib/api/services', () => ({
  healthService: {
    getSystemStatus: jest.fn().mockResolvedValue({
      status: 'healthy',
      components: {},
      metrics: {},
    }),
  },
  analyticsService: {
    getUsageSummary: jest.fn().mockResolvedValue({
      total_queries_today: 100,
      total_sessions_today: 25,
      active_sources: 5,
      system_health: 'healthy',
      recent_activity: [],
    }),
  },
  sourceService: {
    getSourcesSummary: jest.fn().mockResolvedValue({
      total_sources: 5,
    }),
  },
}));

// Mock recharts
jest.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  AreaChart: ({ children }: any) => <div data-testid="area-chart">{children}</div>,
  Area: () => <div data-testid="area" />,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  Legend: () => <div data-testid="legend" />,
}));

describe('Dashboard Components', () => {
  beforeEach(() => {
    mockUseAuthStore.mockReturnValue({
      user: {
        id: '1',
        username: 'admin',
        role: 'admin',
        permissions: ['read', 'write', 'admin'],
      },
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      login: jest.fn(),
      logout: jest.fn(),
      checkAuth: jest.fn(),
      setLoading: jest.fn(),
    });
  });

  it('should render login page', async () => {
    const LoginPage = (await import('@/app/(auth)/login/page')).default;
    
    render(<LoginPage />);
    
    expect(screen.getByText('RAG Chatbot Admin')).toBeInTheDocument();
    expect(screen.getByText('Sign in to access the admin dashboard')).toBeInTheDocument();
    expect(screen.getByLabelText('Username')).toBeInTheDocument();
    expect(screen.getByLabelText('API Key')).toBeInTheDocument();
  });

  it('should render dashboard overview', async () => {
    const DashboardPage = (await import('@/app/(dashboard)/dashboard/page')).default;
    
    render(<DashboardPage />);
    
    expect(screen.getByText('Dashboard Overview')).toBeInTheDocument();
    expect(screen.getByText('Total Queries Today')).toBeInTheDocument();
    expect(screen.getByText('Active Sessions')).toBeInTheDocument();
    expect(screen.getByText('Data Sources')).toBeInTheDocument();
    expect(screen.getByText('System Health')).toBeInTheDocument();
  });

  it('should handle authentication state correctly', () => {
    const { user, isAuthenticated } = useAuthStore();
    
    expect(isAuthenticated).toBe(true);
    expect(user?.username).toBe('admin');
    expect(user?.role).toBe('admin');
  });
});

describe('Error Boundary', () => {
  it('should catch and display errors', () => {
    const ErrorBoundary = require('@/components/ErrorBoundary').default;
    
    const ThrowError = () => {
      throw new Error('Test error');
    };

    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
    expect(screen.getByText('Go Home')).toBeInTheDocument();
  });
});

describe('API Client', () => {
  it('should handle API errors gracefully', async () => {
    const { apiClient } = await import('@/lib/api/client');
    
    // Mock fetch to simulate network error
    global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
    
    try {
      await apiClient.get('/test-endpoint');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }
  });
});

describe('Utility Functions', () => {
  it('should format bytes correctly', async () => {
    const { formatBytes } = await import('@/lib/utils');
    
    expect(formatBytes(0)).toBe('0 Bytes');
    expect(formatBytes(1024)).toBe('1 KB');
    expect(formatBytes(1024 * 1024)).toBe('1 MB');
    expect(formatBytes(1024 * 1024 * 1024)).toBe('1 GB');
  });

  it('should format duration correctly', async () => {
    const { formatDuration } = await import('@/lib/utils');
    
    expect(formatDuration(500)).toBe('500ms');
    expect(formatDuration(1500)).toBe('1.5s');
    expect(formatDuration(65000)).toBe('1.1m');
    expect(formatDuration(3700000)).toBe('1.0h');
  });
});

describe('Form Validation', () => {
  it('should validate source form data', async () => {
    const { z } = await import('zod');
    
    const sourceSchema = z.object({
      name: z.string().min(1, 'Name is required'),
      source_type: z.enum(['website', 'pdf', 'text']),
      enabled: z.boolean(),
    });

    const validData = {
      name: 'Test Source',
      source_type: 'website' as const,
      enabled: true,
    };

    const invalidData = {
      name: '',
      source_type: 'invalid' as any,
      enabled: true,
    };

    expect(() => sourceSchema.parse(validData)).not.toThrow();
    expect(() => sourceSchema.parse(invalidData)).toThrow();
  });
});
