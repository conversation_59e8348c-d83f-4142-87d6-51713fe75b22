'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Activity,
  Database,
  MessageSquare,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
} from 'lucide-react';
import { healthService, analyticsService, sourceService } from '@/lib/api/services';
import { formatDate, formatDuration } from '@/lib/utils';

interface DashboardStats {
  totalQueries: number;
  activeSessions: number;
  totalSources: number;
  systemHealth: string;
  avgResponseTime: number;
  successRate: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalQueries: 0,
    activeSessions: 0,
    totalSources: 0,
    systemHealth: 'unknown',
    avgResponseTime: 0,
    successRate: 0,
  });
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Safe number formatting function
  const safeFormatNumber = (value: number | undefined | null): string => {
    if (value === undefined || value === null || isNaN(value)) {
      return '0';
    }
    return value.toLocaleString();
  };

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load data from multiple endpoints
      const [healthData, summaryData, sourcesData] = await Promise.allSettled([
        healthService.getSystemStatus(),
        analyticsService.getUsageSummary(),
        sourceService.getSourcesSummary(),
      ]);

      // Process the data
      const newStats: DashboardStats = {
        totalQueries: summaryData.status === 'fulfilled' ?
          (summaryData.value.total_queries_today || summaryData.value.queries?.today || 0) : 0,
        activeSessions: summaryData.status === 'fulfilled' ?
          (summaryData.value.total_sessions_today || summaryData.value.sessions?.active_sessions || 0) : 0,
        totalSources: sourcesData.status === 'fulfilled' ? (sourcesData.value?.total_sources || 0) : 0,
        systemHealth: healthData.status === 'fulfilled' ? (healthData.value?.status || 'unknown') : 'unknown',
        avgResponseTime: 1250, // Mock data
        successRate: 98.5, // Mock data
      };

      setStats(newStats);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthBadge = (health: string | undefined | null) => {
    if (!health || typeof health !== 'string') {
      return <Badge variant="secondary">Unknown</Badge>;
    }

    switch (health.toLowerCase()) {
      case 'healthy':
        return <Badge className="bg-green-100 text-green-800">Healthy</Badge>;
      case 'degraded':
        return <Badge className="bg-yellow-100 text-yellow-800">Degraded</Badge>;
      case 'unhealthy':
        return <Badge className="bg-red-100 text-red-800">Unhealthy</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard Overview</h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">
            Last updated: {formatDate(lastUpdated)}
          </p>
        </div>
        <Button onClick={loadDashboardData} disabled={loading} size="sm" className="sm:size-default">
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Queries Today</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{safeFormatNumber(stats.totalQueries)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline w-3 h-3 mr-1" />
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{safeFormatNumber(stats.activeSessions)}</div>
            <p className="text-xs text-muted-foreground">
              Currently active users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Sources</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{safeFormatNumber(stats.totalSources)}</div>
            <p className="text-xs text-muted-foreground">
              <CheckCircle className="inline w-3 h-3 mr-1" />
              All sources active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {getHealthBadge(stats.systemHealth)}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              All systems operational
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
            <CardDescription>Real-time system performance indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Average Response Time</span>
                <span className="text-sm text-gray-600">{formatDuration(stats.avgResponseTime)}</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Success Rate</span>
                <span className="text-sm text-gray-600">{stats.successRate}%</span>
              </div>
              <Progress value={stats.successRate} className="h-2" />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Cache Hit Rate</span>
                <span className="text-sm text-gray-600">85%</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest system events and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Pipeline completed successfully</p>
                  <p className="text-xs text-gray-600">Data source indexing finished</p>
                  <p className="text-xs text-gray-400">2 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium">New data source added</p>
                  <p className="text-xs text-gray-600">Legal documents collection</p>
                  <p className="text-xs text-gray-400">15 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium">High query volume detected</p>
                  <p className="text-xs text-gray-600">Consider scaling resources</p>
                  <p className="text-xs text-gray-400">1 hour ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-16 sm:h-20 flex flex-col">
              <Database className="w-5 h-5 sm:w-6 sm:h-6 mb-2" />
              <span className="text-sm sm:text-base">Manage Sources</span>
            </Button>
            <Button variant="outline" className="h-16 sm:h-20 flex flex-col">
              <Activity className="w-5 h-5 sm:w-6 sm:h-6 mb-2" />
              <span className="text-sm sm:text-base">View Monitoring</span>
            </Button>
            <Button variant="outline" className="h-16 sm:h-20 flex flex-col sm:col-span-2 md:col-span-1">
              <MessageSquare className="w-5 h-5 sm:w-6 sm:h-6 mb-2" />
              <span className="text-sm sm:text-base">Test Chat</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
