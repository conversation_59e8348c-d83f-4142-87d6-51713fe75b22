'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Settings,
  Save,
  RefreshCw,
  Download,
  Upload,
  History,
  CheckCircle,
  AlertTriangle,
  Loader2,
} from 'lucide-react';
import { configService } from '@/lib/api/services';
import { SystemConfig } from '@/lib/types';
import { toast } from 'sonner';

const apiSettingsSchema = z.object({
  max_query_length: z.number().min(100).max(10000),
  default_max_results: z.number().min(1).max(50),
  rate_limit_per_minute: z.number().min(1).max(1000),
});

const modelSettingsSchema = z.object({
  embedding_model: z.string().min(1),
  llm_model: z.string().min(1),
  temperature: z.number().min(0).max(2),
});

const searchSettingsSchema = z.object({
  vector_search_k: z.number().min(1).max(100),
  reranking_enabled: z.boolean(),
  hybrid_search_alpha: z.number().min(0).max(1),
});

type ApiSettings = z.infer<typeof apiSettingsSchema>;
type ModelSettings = z.infer<typeof modelSettingsSchema>;
type SearchSettings = z.infer<typeof searchSettingsSchema>;

export default function ConfigurationPage() {
  const [config, setConfig] = useState<SystemConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('api');

  const apiForm = useForm<ApiSettings>({
    resolver: zodResolver(apiSettingsSchema),
    defaultValues: {
      max_query_length: 2000,
      default_max_results: 5,
      rate_limit_per_minute: 60,
    },
  });

  const modelForm = useForm<ModelSettings>({
    resolver: zodResolver(modelSettingsSchema),
    defaultValues: {
      embedding_model: 'text-embedding-004',
      llm_model: 'gemini-2.5-flash',
      temperature: 0.7,
    },
  });

  const searchForm = useForm<SearchSettings>({
    resolver: zodResolver(searchSettingsSchema),
    defaultValues: {
      vector_search_k: 10,
      reranking_enabled: true,
      hybrid_search_alpha: 0.5,
    },
  });

  const loadConfiguration = async () => {
    setLoading(true);
    try {
      const configData = await configService.getAllConfigs();
      setConfig(configData);

      // Update form values
      if (configData.api_settings) {
        apiForm.reset(configData.api_settings);
      }
      if (configData.model_settings) {
        modelForm.reset(configData.model_settings);
      }
      if (configData.search_settings) {
        searchForm.reset(configData.search_settings);
      }

      toast.success('Configuration loaded successfully');
    } catch (error: any) {
      toast.error('Failed to load configuration: ' + error.message);
      
      // Mock data for development
      const mockConfig: SystemConfig = {
        api_settings: {
          max_query_length: 2000,
          default_max_results: 5,
          rate_limit_per_minute: 60,
        },
        model_settings: {
          embedding_model: 'text-embedding-004',
          llm_model: 'gemini-2.5-flash',
          temperature: 0.7,
        },
        search_settings: {
          vector_search_k: 10,
          reranking_enabled: true,
          hybrid_search_alpha: 0.5,
        },
      };
      setConfig(mockConfig);
      apiForm.reset(mockConfig.api_settings);
      modelForm.reset(mockConfig.model_settings);
      searchForm.reset(mockConfig.search_settings);
    } finally {
      setLoading(false);
    }
  };

  const saveConfiguration = async (configType: string, data: any) => {
    setSaving(true);
    try {
      await configService.updateConfig(configType, data, true);
      toast.success(`${configType} settings saved successfully`);
      loadConfiguration(); // Reload to get updated config
    } catch (error: any) {
      toast.error(`Failed to save ${configType} settings: ` + error.message);
    } finally {
      setSaving(false);
    }
  };

  const handleBackupConfig = async () => {
    try {
      await configService.backupConfig('all');
      toast.success('Configuration backup created successfully');
    } catch (error: any) {
      toast.error('Failed to create backup: ' + error.message);
    }
  };

  const handleExportConfig = () => {
    if (!config) return;
    
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rag-config-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success('Configuration exported successfully');
  };

  useEffect(() => {
    loadConfiguration();
  }, []);

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Configuration Management</h1>
          <p className="text-gray-600 mt-1">
            Manage system settings, API configurations, and model parameters
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadConfiguration} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleExportConfig} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline">
                <History className="w-4 h-4 mr-2" />
                Backup
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Create Configuration Backup</AlertDialogTitle>
                <AlertDialogDescription>
                  This will create a backup of all current configuration settings. 
                  You can restore from this backup later if needed.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleBackupConfig}>
                  Create Backup
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api">API Settings</TabsTrigger>
          <TabsTrigger value="model">Model Settings</TabsTrigger>
          <TabsTrigger value="search">Search Settings</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        {/* API Settings Tab */}
        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
              <CardDescription>
                Configure API behavior, rate limiting, and request handling
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={apiForm.handleSubmit((data) => saveConfiguration('api_settings', data))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="max_query_length">Max Query Length</Label>
                    <Input
                      id="max_query_length"
                      type="number"
                      {...apiForm.register('max_query_length', { valueAsNumber: true })}
                    />
                    <p className="text-sm text-gray-500">Maximum characters allowed in a query</p>
                    {apiForm.formState.errors.max_query_length && (
                      <p className="text-sm text-red-600">{apiForm.formState.errors.max_query_length.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="default_max_results">Default Max Results</Label>
                    <Input
                      id="default_max_results"
                      type="number"
                      {...apiForm.register('default_max_results', { valueAsNumber: true })}
                    />
                    <p className="text-sm text-gray-500">Default number of search results to return</p>
                    {apiForm.formState.errors.default_max_results && (
                      <p className="text-sm text-red-600">{apiForm.formState.errors.default_max_results.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rate_limit_per_minute">Rate Limit (per minute)</Label>
                    <Input
                      id="rate_limit_per_minute"
                      type="number"
                      {...apiForm.register('rate_limit_per_minute', { valueAsNumber: true })}
                    />
                    <p className="text-sm text-gray-500">Maximum requests per minute per user</p>
                    {apiForm.formState.errors.rate_limit_per_minute && (
                      <p className="text-sm text-red-600">{apiForm.formState.errors.rate_limit_per_minute.message}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save API Settings
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Model Settings Tab */}
        <TabsContent value="model" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Model Configuration</CardTitle>
              <CardDescription>
                Configure AI models, embedding settings, and generation parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={modelForm.handleSubmit((data) => saveConfiguration('model_settings', data))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="embedding_model">Embedding Model</Label>
                    <Select
                      value={modelForm.watch('embedding_model')}
                      onValueChange={(value) => modelForm.setValue('embedding_model', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="text-embedding-004">text-embedding-004</SelectItem>
                        <SelectItem value="text-embedding-3-large">text-embedding-3-large</SelectItem>
                        <SelectItem value="text-embedding-3-small">text-embedding-3-small</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-500">Model used for generating embeddings</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="llm_model">Language Model</Label>
                    <Select
                      value={modelForm.watch('llm_model')}
                      onValueChange={(value) => modelForm.setValue('llm_model', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gemini-2.5-flash">Gemini 2.5 Flash</SelectItem>
                        <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                        <SelectItem value="gpt-4">GPT-4</SelectItem>
                        <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-500">Model used for generating responses</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="temperature">Temperature</Label>
                    <Input
                      id="temperature"
                      type="number"
                      step="0.1"
                      min="0"
                      max="2"
                      {...modelForm.register('temperature', { valueAsNumber: true })}
                    />
                    <p className="text-sm text-gray-500">Controls randomness in responses (0.0 - 2.0)</p>
                    {modelForm.formState.errors.temperature && (
                      <p className="text-sm text-red-600">{modelForm.formState.errors.temperature.message}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Model Settings
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Search Settings Tab */}
        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Search Configuration</CardTitle>
              <CardDescription>
                Configure vector search parameters and ranking algorithms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={searchForm.handleSubmit((data) => saveConfiguration('search_settings', data))} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="vector_search_k">Vector Search K</Label>
                    <Input
                      id="vector_search_k"
                      type="number"
                      {...searchForm.register('vector_search_k', { valueAsNumber: true })}
                    />
                    <p className="text-sm text-gray-500">Number of similar vectors to retrieve</p>
                    {searchForm.formState.errors.vector_search_k && (
                      <p className="text-sm text-red-600">{searchForm.formState.errors.vector_search_k.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="hybrid_search_alpha">Hybrid Search Alpha</Label>
                    <Input
                      id="hybrid_search_alpha"
                      type="number"
                      step="0.1"
                      min="0"
                      max="1"
                      {...searchForm.register('hybrid_search_alpha', { valueAsNumber: true })}
                    />
                    <p className="text-sm text-gray-500">Balance between semantic and keyword search (0.0 - 1.0)</p>
                    {searchForm.formState.errors.hybrid_search_alpha && (
                      <p className="text-sm text-red-600">{searchForm.formState.errors.hybrid_search_alpha.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="reranking_enabled"
                        checked={searchForm.watch('reranking_enabled')}
                        onCheckedChange={(checked) => searchForm.setValue('reranking_enabled', checked)}
                      />
                      <Label htmlFor="reranking_enabled">Enable Reranking</Label>
                    </div>
                    <p className="text-sm text-gray-500">Use reranking model to improve result relevance</p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Search Settings
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Tab */}
        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Configuration</CardTitle>
              <CardDescription>
                Advanced settings and environment variables
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Configuration Status</Label>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm">All configurations valid</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Last Updated</Label>
                    <p className="text-sm text-gray-600">
                      {new Date().toLocaleString()}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="raw_config">Raw Configuration (JSON)</Label>
                  <Textarea
                    id="raw_config"
                    value={JSON.stringify(config, null, 2)}
                    readOnly
                    rows={10}
                    className="font-mono text-sm"
                  />
                  <p className="text-sm text-gray-500">
                    Read-only view of the complete configuration
                  </p>
                </div>

                <div className="flex items-center space-x-2 pt-4 border-t">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <p className="text-sm text-gray-600">
                    Changes to configuration may require system restart to take effect
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
