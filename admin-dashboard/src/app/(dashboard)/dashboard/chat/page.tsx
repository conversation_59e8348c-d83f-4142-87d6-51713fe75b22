'use client';

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Send,
  Bot,
  User,
  RefreshCw,
  Settings,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Clock,
  FileText,
  Trash2,
} from 'lucide-react';
import { chatService } from '@/lib/api/services';
import { QueryRequest, QueryResponse, ConversationHistory } from '@/lib/types';
import { formatDate, formatDuration } from '@/lib/utils';
import { toast } from 'sonner';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  sources?: Array<{
    document_id: string;
    title: string;
    excerpt: string;
    score: number;
  }>;
  processingTime?: number;
}

interface ChatSettings {
  maxResults: number;
  includeSources: boolean;
  sessionId: string;
  model: string;
}

export default function ChatPage() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState<ChatSettings>({
    maxResults: 5,
    includeSources: true,
    sessionId: '',
    model: 'gemini-2.5-flash',
  });
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Create a new session when component mounts
    const createSession = async () => {
      try {
        const session = await chatService.createSession({
          admin_test: true,
          created_by: 'admin_dashboard',
        });
        setSettings(prev => ({ ...prev, sessionId: session.session_id }));
      } catch (error: any) {
        toast.error('Failed to create chat session: ' + error.message);
        // Use a mock session ID for development
        setSettings(prev => ({ ...prev, sessionId: 'admin_session_' + Date.now() }));
      }
    };

    createSession();
  }, []);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const request: QueryRequest = {
        query: inputValue,
        session_id: settings.sessionId,
        max_results: settings.maxResults,
        include_sources: settings.includeSources,
      };

      const startTime = Date.now();
      const response = await chatService.sendQuery(request);
      const processingTime = Date.now() - startTime;

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.answer,
        timestamp: new Date().toISOString(),
        sources: response.sources,
        processingTime,
      };

      setMessages(prev => [...prev, assistantMessage]);
      toast.success(`Response generated in ${formatDuration(processingTime)}`);
    } catch (error: any) {
      toast.error('Failed to send message: ' + error.message);
      
      // Mock response for development
      const mockResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'This is a mock response for development purposes. The actual RAG system would provide a detailed answer based on your legal document corpus.',
        timestamp: new Date().toISOString(),
        sources: [
          {
            document_id: 'doc_001',
            title: 'Legal Requirements Document',
            excerpt: 'This document outlines the legal requirements...',
            score: 0.95,
          },
          {
            document_id: 'doc_002',
            title: 'Contract Template',
            excerpt: 'Standard contract template for...',
            score: 0.87,
          },
        ],
        processingTime: 1250,
      };
      
      setMessages(prev => [...prev, mockResponse]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearChat = async () => {
    try {
      if (settings.sessionId) {
        await chatService.clearSession(settings.sessionId);
      }
      setMessages([]);
      toast.success('Chat cleared successfully');
    } catch (error: any) {
      toast.error('Failed to clear chat: ' + error.message);
      setMessages([]);
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Message copied to clipboard');
  };

  const handleRegenerateResponse = async (messageId: string) => {
    const messageIndex = messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    const userMessage = messages[messageIndex - 1];
    if (!userMessage || userMessage.type !== 'user') return;

    setIsLoading(true);
    try {
      const request: QueryRequest = {
        query: userMessage.content,
        session_id: settings.sessionId,
        max_results: settings.maxResults,
        include_sources: settings.includeSources,
      };

      const startTime = Date.now();
      const response = await chatService.regenerateResponse(request, 'User requested regeneration');
      const processingTime = Date.now() - startTime;

      const newMessage: ChatMessage = {
        ...messages[messageIndex],
        content: response.answer,
        sources: response.sources,
        processingTime,
        timestamp: new Date().toISOString(),
      };

      const newMessages = [...messages];
      newMessages[messageIndex] = newMessage;
      setMessages(newMessages);

      toast.success('Response regenerated successfully');
    } catch (error: any) {
      toast.error('Failed to regenerate response: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 h-full flex flex-col space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Chat Interface</h1>
          <p className="text-gray-600 mt-1">
            Test the RAG chatbot functionality and monitor responses
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button variant="outline" onClick={handleClearChat}>
            <Trash2 className="w-4 h-4 mr-2" />
            Clear Chat
          </Button>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Chat Interface */}
        <div className="lg:col-span-3 flex flex-col">
          <Card className="flex-1 flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bot className="w-5 h-5" />
                <span>RAG Legal Assistant</span>
                {settings.sessionId && (
                  <Badge variant="outline" className="ml-2">
                    Session: {settings.sessionId.slice(-8)}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Ask questions about your legal document corpus
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4 max-h-96">
                {messages.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>Start a conversation to test the RAG system</p>
                    <p className="text-sm mt-2">Try asking about legal requirements, contracts, or case law</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg p-4 ${
                          message.type === 'user'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <div className="flex items-start space-x-2">
                          {message.type === 'assistant' && <Bot className="w-5 h-5 mt-0.5" />}
                          {message.type === 'user' && <User className="w-5 h-5 mt-0.5" />}
                          <div className="flex-1">
                            <p className="text-sm">{message.content}</p>
                            
                            {/* Sources */}
                            {message.sources && message.sources.length > 0 && (
                              <div className="mt-3 space-y-2">
                                <p className="text-xs font-medium opacity-75">Sources:</p>
                                {message.sources.map((source, index) => (
                                  <div key={index} className="text-xs bg-white bg-opacity-20 rounded p-2">
                                    <div className="font-medium">{source.title}</div>
                                    <div className="opacity-75">{source.excerpt}</div>
                                    <div className="opacity-50">Score: {source.score.toFixed(2)}</div>
                                  </div>
                                ))}
                              </div>
                            )}
                            
                            {/* Metadata */}
                            <div className="flex items-center justify-between mt-2 text-xs opacity-75">
                              <span>{formatDate(message.timestamp)}</span>
                              {message.processingTime && (
                                <span>{formatDuration(message.processingTime)}</span>
                              )}
                            </div>
                            
                            {/* Actions */}
                            {message.type === 'assistant' && (
                              <div className="flex items-center space-x-2 mt-2">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleCopyMessage(message.content)}
                                  className="h-6 px-2 text-xs"
                                >
                                  <Copy className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleRegenerateResponse(message.id)}
                                  className="h-6 px-2 text-xs"
                                  disabled={isLoading}
                                >
                                  <RefreshCw className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 px-2 text-xs"
                                >
                                  <ThumbsUp className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 px-2 text-xs"
                                >
                                  <ThumbsDown className="w-3 h-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-lg p-4 max-w-[80%]">
                      <div className="flex items-center space-x-2">
                        <Bot className="w-5 h-5" />
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Input */}
              <div className="flex space-x-2">
                <Textarea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Ask a question about your legal documents..."
                  className="flex-1 min-h-[60px] resize-none"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  disabled={isLoading}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  className="px-6"
                >
                  {isLoading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Chat Settings</CardTitle>
              <CardDescription>Configure chat behavior</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="maxResults">Max Results</Label>
                <Select
                  value={settings.maxResults.toString()}
                  onValueChange={(value) => setSettings(prev => ({ ...prev, maxResults: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 results</SelectItem>
                    <SelectItem value="5">5 results</SelectItem>
                    <SelectItem value="10">10 results</SelectItem>
                    <SelectItem value="15">15 results</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="includeSources"
                  checked={settings.includeSources}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, includeSources: checked }))}
                />
                <Label htmlFor="includeSources">Include Sources</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Select
                  value={settings.model}
                  onValueChange={(value) => setSettings(prev => ({ ...prev, model: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gemini-2.5-flash">Gemini 2.5 Flash</SelectItem>
                    <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-2">Session Info</h4>
                <div className="text-xs text-gray-600 space-y-1">
                  <div>ID: {settings.sessionId.slice(-12) || 'Not connected'}</div>
                  <div>Messages: {messages.length}</div>
                  <div>Model: {settings.model}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
