'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  Users,
  Cpu,
  BarChart3,
} from 'lucide-react';
import { pipelineService } from '@/lib/api/services';
import { PipelineTask } from '@/lib/types';
import { formatDate, formatDuration } from '@/lib/utils';
import { toast } from 'sonner';

interface WorkerStatus {
  id: string;
  name: string;
  status: 'active' | 'idle' | 'offline';
  currentTask?: string;
  tasksCompleted: number;
  uptime: string;
  cpuUsage: number;
  memoryUsage: number;
}

interface PipelineStats {
  activeTasks: number;
  completedToday: number;
  failedToday: number;
  avgProcessingTime: number;
  totalWorkers: number;
  activeWorkers: number;
}

export default function PipelinesPage() {
  const [tasks, setTasks] = useState<PipelineTask[]>([]);
  const [workers, setWorkers] = useState<WorkerStatus[]>([]);
  const [stats, setStats] = useState<PipelineStats>({
    activeTasks: 0,
    completedToday: 0,
    failedToday: 0,
    avgProcessingTime: 0,
    totalWorkers: 0,
    activeWorkers: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedMode, setSelectedMode] = useState('full');
  const [selectedSources, setSelectedSources] = useState<string[]>([]);

  const loadPipelineData = async () => {
    setLoading(true);
    try {
      // Load active tasks
      const activeTasks = await pipelineService.getActiveTasks();
      const taskHistory = await pipelineService.getTaskHistory(50);
      const workerStatus = await pipelineService.getWorkerStatus();

      setTasks([...activeTasks.active_tasks, ...taskHistory.task_history]);
      
      // Mock worker data for development
      setWorkers([
        {
          id: 'worker-1',
          name: 'Pipeline Worker 1',
          status: 'active',
          currentTask: 'Processing legal documents',
          tasksCompleted: 45,
          uptime: '2d 14h 30m',
          cpuUsage: 65,
          memoryUsage: 78,
        },
        {
          id: 'worker-2',
          name: 'Pipeline Worker 2',
          status: 'idle',
          tasksCompleted: 32,
          uptime: '2d 14h 30m',
          cpuUsage: 12,
          memoryUsage: 45,
        },
        {
          id: 'worker-3',
          name: 'Pipeline Worker 3',
          status: 'offline',
          tasksCompleted: 0,
          uptime: '0m',
          cpuUsage: 0,
          memoryUsage: 0,
        },
      ]);

      setStats({
        activeTasks: activeTasks.active_tasks.length,
        completedToday: 12,
        failedToday: 1,
        avgProcessingTime: 1250,
        totalWorkers: 3,
        activeWorkers: 2,
      });

      toast.success('Pipeline data loaded successfully');
    } catch (error: any) {
      toast.error('Failed to load pipeline data: ' + error.message);
      
      // Mock data for development
      setTasks([
        {
          task_id: 'task_001',
          status: 'processing',
          progress: 65,
          message: 'Processing legal documents collection',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:35:00Z',
          source_ids: ['source_1', 'source_2'],
        },
        {
          task_id: 'task_002',
          status: 'completed',
          progress: 100,
          message: 'Contract templates indexing completed',
          created_at: '2024-01-15T09:00:00Z',
          updated_at: '2024-01-15T09:15:00Z',
          source_ids: ['source_3'],
        },
        {
          task_id: 'task_003',
          status: 'failed',
          progress: 45,
          message: 'Failed to process case law database',
          created_at: '2024-01-15T08:30:00Z',
          updated_at: '2024-01-15T08:45:00Z',
          source_ids: ['source_4'],
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleStartPipeline = async () => {
    try {
      const result = await pipelineService.startPipeline(selectedMode, selectedSources);
      toast.success(`Pipeline started with task ID: ${result.task_id}`);
      loadPipelineData();
    } catch (error: any) {
      toast.error('Failed to start pipeline: ' + error.message);
    }
  };

  const handleCancelTask = async (taskId: string) => {
    try {
      await pipelineService.cancelTask(taskId);
      toast.success('Task cancelled successfully');
      loadPipelineData();
    } catch (error: any) {
      toast.error('Failed to cancel task: ' + error.message);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'processing':
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800"><Activity className="w-3 h-3 mr-1" />Processing</Badge>;
      case 'failed':
      case 'error':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800"><Square className="w-3 h-3 mr-1" />Cancelled</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getWorkerStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'idle':
        return <Badge className="bg-yellow-100 text-yellow-800">Idle</Badge>;
      case 'offline':
        return <Badge className="bg-red-100 text-red-800">Offline</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  useEffect(() => {
    loadPipelineData();
    
    // Set up auto-refresh every 10 seconds for active tasks
    const interval = setInterval(() => {
      if (stats.activeTasks > 0) {
        loadPipelineData();
      }
    }, 10000);
    
    return () => clearInterval(interval);
  }, [stats.activeTasks]);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pipeline Management</h1>
          <p className="text-gray-600 mt-1">
            Monitor and control data processing pipelines
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadPipelineData} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Pipeline Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tasks</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeTasks}</div>
            <p className="text-xs text-muted-foreground">
              Currently processing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedToday}</div>
            <p className="text-xs text-muted-foreground">
              {stats.failedToday} failed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatDuration(stats.avgProcessingTime)}</div>
            <p className="text-xs text-muted-foreground">
              Per task
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Workers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeWorkers}/{stats.totalWorkers}</div>
            <p className="text-xs text-muted-foreground">
              Active workers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Pipeline Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Start New Pipeline</CardTitle>
          <CardDescription>
            Configure and start a new data processing pipeline
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Pipeline Mode</label>
              <Select value={selectedMode} onValueChange={setSelectedMode}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full">Full Processing</SelectItem>
                  <SelectItem value="incremental">Incremental Update</SelectItem>
                  <SelectItem value="reprocess">Reprocess Only</SelectItem>
                  <SelectItem value="validate">Validation Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1" />
            <Button onClick={handleStartPipeline} disabled={loading}>
              <Play className="w-4 h-4 mr-2" />
              Start Pipeline
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="workers">Workers</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        {/* Tasks Tab */}
        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pipeline Tasks</CardTitle>
              <CardDescription>
                Current and recent pipeline task execution status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Sources</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                        Loading tasks...
                      </TableCell>
                    </TableRow>
                  ) : tasks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        No tasks found
                      </TableCell>
                    </TableRow>
                  ) : (
                    tasks.map((task) => (
                      <TableRow key={task.task_id}>
                        <TableCell className="font-mono text-sm">{task.task_id}</TableCell>
                        <TableCell>{getStatusBadge(task.status)}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Progress value={task.progress} className="h-2" />
                            <span className="text-xs text-gray-500">{task.progress}%</span>
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">{task.message}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {task.source_ids?.length || 0} sources
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(task.created_at)}</TableCell>
                        <TableCell>
                          {task.status === 'processing' && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button size="sm" variant="outline" className="text-red-600">
                                  <Square className="w-3 h-3 mr-1" />
                                  Cancel
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Cancel Task</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to cancel this task? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleCancelTask(task.task_id)}>
                                    Cancel Task
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Workers Tab */}
        <TabsContent value="workers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pipeline Workers</CardTitle>
              <CardDescription>
                Monitor the status and performance of pipeline workers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Worker Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Current Task</TableHead>
                    <TableHead>Tasks Completed</TableHead>
                    <TableHead>Uptime</TableHead>
                    <TableHead>CPU Usage</TableHead>
                    <TableHead>Memory Usage</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workers.map((worker) => (
                    <TableRow key={worker.id}>
                      <TableCell className="font-medium">{worker.name}</TableCell>
                      <TableCell>{getWorkerStatusBadge(worker.status)}</TableCell>
                      <TableCell>{worker.currentTask || 'Idle'}</TableCell>
                      <TableCell>{worker.tasksCompleted}</TableCell>
                      <TableCell>{worker.uptime}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Progress value={worker.cpuUsage} className="h-2" />
                          <span className="text-xs text-gray-500">{worker.cpuUsage}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Progress value={worker.memoryUsage} className="h-2" />
                          <span className="text-xs text-gray-500">{worker.memoryUsage}%</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Task History</CardTitle>
              <CardDescription>
                Historical pipeline task execution records
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                Task history will be displayed here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Resource Usage</CardTitle>
                <CardDescription>System resource utilization</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">CPU Usage</span>
                    <span className="text-sm text-gray-600">45%</span>
                  </div>
                  <Progress value={45} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Memory Usage</span>
                    <span className="text-sm text-gray-600">68%</span>
                  </div>
                  <Progress value={68} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Disk I/O</span>
                    <span className="text-sm text-gray-600">32%</span>
                  </div>
                  <Progress value={32} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Pipeline performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Throughput</span>
                  <span className="text-sm text-gray-600">150 docs/min</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Success Rate</span>
                  <span className="text-sm text-gray-600">98.5%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Queue Length</span>
                  <span className="text-sm text-gray-600">12 tasks</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Error Rate</span>
                  <span className="text-sm text-gray-600">1.5%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
