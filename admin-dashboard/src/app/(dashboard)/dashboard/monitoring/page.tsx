'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import {
  Activity,
  Cpu,
  HardDrive,
  Wifi,
  Database,
  Server,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Zap,
} from 'lucide-react';
import { healthService } from '@/lib/api/services';
import { SystemStatus, SystemMetrics } from '@/lib/types';
import { formatBytes, formatDate } from '@/lib/utils';
import { toast } from 'sonner';

interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  uptime: string;
  lastCheck: string;
}

export default function MonitoringPage() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [metrics, setMetrics] = useState<SystemMetrics[]>([]);
  const [services, setServices] = useState<ServiceHealth[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const loadMonitoringData = async () => {
    try {
      setLoading(true);
      
      // Load system status
      const status = await healthService.getSystemStatus();
      setSystemStatus(status);

      // Load metrics history
      const metricsHistory = await healthService.getMetricsHistory(24);
      setMetrics(metricsHistory.metrics_history || []);

      // Mock services data
      setServices([
        {
          name: 'RAG API',
          status: 'healthy',
          responseTime: 45,
          uptime: '99.9%',
          lastCheck: new Date().toISOString(),
        },
        {
          name: 'Vector Database',
          status: 'healthy',
          responseTime: 12,
          uptime: '99.8%',
          lastCheck: new Date().toISOString(),
        },
        {
          name: 'Redis Cache',
          status: 'healthy',
          responseTime: 3,
          uptime: '100%',
          lastCheck: new Date().toISOString(),
        },
        {
          name: 'Google AI API',
          status: 'degraded',
          responseTime: 250,
          uptime: '98.5%',
          lastCheck: new Date().toISOString(),
        },
        {
          name: 'Cohere API',
          status: 'healthy',
          responseTime: 180,
          uptime: '99.2%',
          lastCheck: new Date().toISOString(),
        },
      ]);

      toast.success('Monitoring data loaded successfully');
    } catch (error: any) {
      toast.error('Failed to load monitoring data: ' + error.message);
      
      // Mock data for development
      setSystemStatus({
        status: 'healthy',
        components: {
          database: {
            redis: 'healthy',
            milvus: 'healthy',
          },
          external_apis: {
            google_ai: 'degraded',
            cohere: 'healthy',
          },
          workers: {
            active: 2,
            status: 'healthy',
          },
        },
        metrics: {
          response_time_ms: 45,
          memory_usage_mb: 1024,
          cpu_usage_percent: 35,
        },
      });

      // Generate mock metrics data
      const now = new Date();
      const mockMetrics = Array.from({ length: 24 }, (_, i) => {
        const timestamp = new Date(now.getTime() - (23 - i) * 60 * 60 * 1000);
        return {
          timestamp: timestamp.toISOString(),
          cpu_usage: 30 + Math.random() * 40,
          memory_usage: 50 + Math.random() * 30,
          disk_usage: 60 + Math.random() * 20,
          network_io: {
            bytes_sent: Math.random() * 1000000,
            bytes_received: Math.random() * 2000000,
          },
          active_connections: Math.floor(10 + Math.random() * 50),
          response_times: {
            avg: 40 + Math.random() * 20,
            p95: 80 + Math.random() * 40,
            p99: 120 + Math.random() * 60,
          },
        };
      });
      setMetrics(mockMetrics);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Healthy</Badge>;
      case 'degraded':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="w-3 h-3 mr-1" />Degraded</Badge>;
      case 'unhealthy':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="w-3 h-3 mr-1" />Unhealthy</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const formatChartData = (metrics: SystemMetrics[]) => {
    return metrics.map(metric => ({
      time: new Date(metric.timestamp).toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      cpu: metric.cpu_usage,
      memory: metric.memory_usage,
      disk: metric.disk_usage,
      connections: metric.active_connections,
      responseTime: metric.response_times.avg,
    }));
  };

  useEffect(() => {
    loadMonitoringData();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(loadMonitoringData, 30000); // Refresh every 30 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const chartData = formatChartData(metrics);
  const currentMetrics = systemStatus?.metrics;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Monitoring</h1>
          <p className="text-gray-600 mt-1">
            Real-time system health and performance monitoring
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={autoRefresh ? "default" : "outline"}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className="w-4 h-4 mr-2" />
            Auto Refresh
          </Button>
          <Button onClick={loadMonitoringData} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentMetrics?.cpu_usage_percent || 0}%</div>
            <Progress value={currentMetrics?.cpu_usage_percent || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes((currentMetrics?.memory_usage_mb || 0) * 1024 * 1024)}</div>
            <Progress value={65} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentMetrics?.response_time_ms || 0}ms</div>
            <p className="text-xs text-muted-foreground mt-2">
              <TrendingDown className="inline w-3 h-3 mr-1 text-green-600" />
              5% improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="mt-2">
              {getStatusBadge(systemStatus?.status || 'unknown')}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              All systems operational
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>CPU & Memory Usage</CardTitle>
                <CardDescription>System resource utilization over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cpu" stroke="#8884d8" name="CPU %" />
                    <Line type="monotone" dataKey="memory" stroke="#82ca9d" name="Memory %" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Times</CardTitle>
                <CardDescription>API response time trends</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="responseTime" stroke="#ffc658" fill="#ffc658" name="Response Time (ms)" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Active Connections</CardTitle>
              <CardDescription>Number of active connections over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="connections" fill="#8884d8" name="Active Connections" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Response Time</span>
                  <span className="text-sm text-gray-600">45ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">95th Percentile</span>
                  <span className="text-sm text-gray-600">120ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">99th Percentile</span>
                  <span className="text-sm text-gray-600">180ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Throughput</span>
                  <span className="text-sm text-gray-600">150 req/min</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Error Rate</span>
                  <span className="text-sm text-gray-600">0.1%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resource Utilization</CardTitle>
                <CardDescription>Current system resource usage</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">CPU Usage</span>
                    <span className="text-sm text-gray-600">{currentMetrics?.cpu_usage_percent || 0}%</span>
                  </div>
                  <Progress value={currentMetrics?.cpu_usage_percent || 0} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Memory Usage</span>
                    <span className="text-sm text-gray-600">65%</span>
                  </div>
                  <Progress value={65} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Disk Usage</span>
                    <span className="text-sm text-gray-600">45%</span>
                  </div>
                  <Progress value={45} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Network I/O</span>
                    <span className="text-sm text-gray-600">25%</span>
                  </div>
                  <Progress value={25} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Services Tab */}
        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Service Health</CardTitle>
              <CardDescription>
                Monitor the health and status of all system services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {services.map((service) => (
                  <div key={service.name} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Database className="w-5 h-5 text-gray-400" />
                        <span className="font-medium">{service.name}</span>
                      </div>
                      {getStatusBadge(service.status)}
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">{service.responseTime}ms</span>
                        <span className="text-gray-400 ml-1">response</span>
                      </div>
                      <div>
                        <span className="font-medium">{service.uptime}</span>
                        <span className="text-gray-400 ml-1">uptime</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Last check: </span>
                        <span>{formatDate(service.lastCheck)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>
                Recent alerts and system notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 border rounded-lg bg-yellow-50">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">High Response Time Detected</p>
                    <p className="text-xs text-gray-600">Google AI API response time exceeded 200ms threshold</p>
                    <p className="text-xs text-gray-400 mt-1">2 minutes ago</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 p-4 border rounded-lg bg-green-50">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">System Health Check Passed</p>
                    <p className="text-xs text-gray-600">All critical services are operational</p>
                    <p className="text-xs text-gray-400 mt-1">5 minutes ago</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 p-4 border rounded-lg">
                  <Activity className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Pipeline Task Completed</p>
                    <p className="text-xs text-gray-600">Legal documents processing finished successfully</p>
                    <p className="text-xs text-gray-400 mt-1">15 minutes ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
