'use client';

import { useEffect, useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  MessageSquare,
  Clock,
  Target,
  RefreshCw,
  Download,
  Calendar,
} from 'lucide-react';
import { analyticsService } from '@/lib/api/services';
import { QueryAnalytics, SessionAnalytics, PerformanceAnalytics, UsageSummary } from '@/lib/types';
import { formatDuration } from '@/lib/utils';
import { toast } from 'sonner';

// Dynamic import for Recharts to avoid SSR issues
import dynamic from 'next/dynamic';

const LineChart = dynamic(() => import('recharts').then((mod) => mod.LineChart), { ssr: false });
const Line = dynamic(() => import('recharts').then((mod) => mod.Line), { ssr: false });
const AreaChart = dynamic(() => import('recharts').then((mod) => mod.AreaChart), { ssr: false });
const Area = dynamic(() => import('recharts').then((mod) => mod.Area), { ssr: false });
const BarChart = dynamic(() => import('recharts').then((mod) => mod.BarChart), { ssr: false });
const Bar = dynamic(() => import('recharts').then((mod) => mod.Bar), { ssr: false });
const PieChart = dynamic(() => import('recharts').then((mod) => mod.PieChart), { ssr: false });
const Pie = dynamic(() => import('recharts').then((mod) => mod.Pie), { ssr: false });
const Cell = dynamic(() => import('recharts').then((mod) => mod.Cell), { ssr: false });
const XAxis = dynamic(() => import('recharts').then((mod) => mod.XAxis), { ssr: false });
const YAxis = dynamic(() => import('recharts').then((mod) => mod.YAxis), { ssr: false });
const CartesianGrid = dynamic(() => import('recharts').then((mod) => mod.CartesianGrid), { ssr: false });
const Tooltip = dynamic(() => import('recharts').then((mod) => mod.Tooltip), { ssr: false });
const Legend = dynamic(() => import('recharts').then((mod) => mod.Legend), { ssr: false });
const ResponsiveContainer = dynamic(() => import('recharts').then((mod) => mod.ResponsiveContainer), { ssr: false });

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Helper function to safely format numbers
const safeFormatNumber = (value: number | undefined | null): string => {
  if (value === undefined || value === null || isNaN(value)) return '0';
  return value.toLocaleString();
};

// Helper function to safely format dates
const safeFormatDate = (dateString: string | undefined | null): string => {
  if (!dateString) return 'Unknown';
  try {
    return new Date(dateString).toLocaleString();
  } catch {
    return 'Invalid Date';
  }
};

export default function AnalyticsPage() {
  const [queryAnalytics, setQueryAnalytics] = useState<QueryAnalytics | null>(null);
  const [sessionAnalytics, setSessionAnalytics] = useState<SessionAnalytics | null>(null);
  const [performanceAnalytics, setPerformanceAnalytics] = useState<PerformanceAnalytics | null>(null);
  const [usageSummary, setUsageSummary] = useState<UsageSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7');

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const days = parseInt(timeRange);
      const [queries, sessions, performance, summary] = await Promise.allSettled([
        analyticsService.getQueryAnalytics(days),
        analyticsService.getSessionAnalytics(days),
        analyticsService.getPerformanceAnalytics(days),
        analyticsService.getUsageSummary(),
      ]);

      if (queries.status === 'fulfilled') {
        setQueryAnalytics(queries.value);
      } else {
        console.warn('Failed to load query analytics:', queries.reason);
      }

      if (sessions.status === 'fulfilled') {
        setSessionAnalytics(sessions.value);
      } else {
        console.warn('Failed to load session analytics:', sessions.reason);
      }

      if (performance.status === 'fulfilled') {
        setPerformanceAnalytics(performance.value);
      } else {
        console.warn('Failed to load performance analytics:', performance.reason);
      }

      if (summary.status === 'fulfilled') {
        setUsageSummary(summary.value);
      } else {
        console.warn('Failed to load usage summary:', summary.reason);
      }

      // Check if any data was loaded successfully
      const hasData = queries.status === 'fulfilled' || sessions.status === 'fulfilled' ||
                     performance.status === 'fulfilled' || summary.status === 'fulfilled';

      if (hasData) {
        toast.success('Analytics data loaded successfully');
      } else {
        throw new Error('All analytics endpoints failed');
      }
    } catch (error: any) {
      console.error('Analytics loading error:', error);
      toast.error('Failed to load analytics, using mock data');

      // Generate mock data for development/fallback
      const generateMockData = () => {
        const days = parseInt(timeRange);

        const mockQueryAnalytics: QueryAnalytics = {
          period: `${timeRange} days`,
          total_queries: 1250,
          successful_queries: 1225,
          failed_queries: 25,
          average_response_time: 1250,
          query_trends: Array.from({ length: days }, (_, i) => ({
            date: new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            count: Math.floor(Math.random() * 200) + 50,
            avg_response_time: Math.floor(Math.random() * 500) + 800,
          })),
        };

        const mockSessionAnalytics: SessionAnalytics = {
          period: `${timeRange} days`,
          total_sessions: 450,
          active_sessions: 12,
          unique_users: 320,
          session_trends: Array.from({ length: days }, (_, i) => ({
            date: new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            sessions: Math.floor(Math.random() * 80) + 20,
            users: Math.floor(Math.random() * 60) + 15,
          })),
        };

        const mockPerformanceAnalytics: PerformanceAnalytics = {
          period: `${timeRange} days`,
          avg_response_time: 1250,
          cache_hit_rate: 0.85,
          error_rate: 0.02,
          performance_trends: Array.from({ length: days }, (_, i) => ({
            date: new Date(Date.now() - (days - 1 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            response_time: Math.floor(Math.random() * 500) + 800,
            cache_hit_rate: 0.8 + Math.random() * 0.15,
            error_rate: Math.random() * 0.05,
          })),
        };

        const mockUsageSummary: UsageSummary = {
          total_queries_today: 180,
          total_sessions_today: 45,
          active_sources: 8,
          system_health: 'healthy',
          recent_activity: [
            {
              type: 'query',
              message: 'High volume of legal document queries',
              timestamp: new Date().toISOString(),
            },
            {
              type: 'session',
              message: 'New user session started',
              timestamp: new Date(Date.now() - 300000).toISOString(),
            },
          ],
        };

        return { mockQueryAnalytics, mockSessionAnalytics, mockPerformanceAnalytics, mockUsageSummary };
      };

      const { mockQueryAnalytics, mockSessionAnalytics, mockPerformanceAnalytics, mockUsageSummary } = generateMockData();

      setQueryAnalytics(mockQueryAnalytics);
      setSessionAnalytics(mockSessionAnalytics);
      setPerformanceAnalytics(mockPerformanceAnalytics);
      setUsageSummary(mockUsageSummary);
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    const data = {
      queryAnalytics,
      sessionAnalytics,
      performanceAnalytics,
      usageSummary,
      exportedAt: new Date().toISOString(),
    };
    
    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rag-analytics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success('Analytics data exported successfully');
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  // Memoized computed values to prevent unnecessary re-renders
  const querySuccessRate = useMemo(() => {
    if (!queryAnalytics || queryAnalytics.total_queries === 0) return '0';
    return ((queryAnalytics.successful_queries / queryAnalytics.total_queries) * 100).toFixed(1);
  }, [queryAnalytics]);

  const pieChartData = useMemo(() => {
    if (!queryAnalytics) return [];
    return [
      { name: 'Successful', value: queryAnalytics.successful_queries },
      { name: 'Failed', value: queryAnalytics.failed_queries },
    ];
  }, [queryAnalytics]);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Monitor usage patterns, performance metrics, and system insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Last 24h</SelectItem>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadAnalytics} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleExportData} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{safeFormatNumber(queryAnalytics?.total_queries)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline w-3 h-3 mr-1 text-green-600" />
              {querySuccessRate}% success rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessionAnalytics?.active_sessions || 0}</div>
            <p className="text-xs text-muted-foreground">
              {sessionAnalytics?.unique_users || 0} unique users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(performanceAnalytics?.avg_response_time || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              <TrendingDown className="inline w-3 h-3 mr-1 text-green-600" />
              5% improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {((performanceAnalytics?.cache_hit_rate || 0) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Efficient caching
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="queries" className="space-y-4">
        <TabsList>
          <TabsTrigger value="queries">Query Analytics</TabsTrigger>
          <TabsTrigger value="sessions">Session Analytics</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="usage">Usage Patterns</TabsTrigger>
        </TabsList>

        {/* Query Analytics Tab */}
        <TabsContent value="queries" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Query Volume Trends</CardTitle>
                <CardDescription>Daily query volume over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={queryAnalytics?.query_trends || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="count" stroke="#8884d8" fill="#8884d8" name="Queries" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Time Trends</CardTitle>
                <CardDescription>Average response time over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={queryAnalytics?.query_trends || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="avg_response_time" stroke="#82ca9d" name="Response Time (ms)" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Query Success vs Failure</CardTitle>
              <CardDescription>Breakdown of successful and failed queries</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  {pieChartData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={pieChartData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label
                        >
                          <Cell fill="#00C49F" />
                          <Cell fill="#FF8042" />
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-[200px] text-gray-500">
                      No data available
                    </div>
                  )}
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Total Queries</span>
                    <Badge variant="outline">
                      {safeFormatNumber(queryAnalytics?.total_queries)}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Successful</span>
                    <Badge className="bg-green-100 text-green-800">
                      {safeFormatNumber(queryAnalytics?.successful_queries)}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Failed</span>
                    <Badge className="bg-red-100 text-red-800">
                      {safeFormatNumber(queryAnalytics?.failed_queries)}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Success Rate</span>
                    <Badge className="bg-blue-100 text-blue-800">{querySuccessRate}%</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Session Analytics Tab */}
        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Session and User Trends</CardTitle>
              <CardDescription>Daily sessions and unique users over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={sessionAnalytics?.session_trends || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="sessions" fill="#8884d8" name="Sessions" />
                  <Bar dataKey="users" fill="#82ca9d" name="Unique Users" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Response Time Trends</CardTitle>
                <CardDescription>System response time over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceAnalytics?.performance_trends || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="response_time" stroke="#8884d8" name="Response Time (ms)" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cache Hit Rate</CardTitle>
                <CardDescription>Cache performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceAnalytics?.performance_trends || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="cache_hit_rate" 
                      stroke="#00C49F" 
                      fill="#00C49F" 
                      name="Cache Hit Rate"
                      formatter={(value: number) => `${(value * 100).toFixed(1)}%`}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Error Rate Trends</CardTitle>
              <CardDescription>System error rate over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={performanceAnalytics?.performance_trends || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="error_rate" 
                    stroke="#FF8042" 
                    name="Error Rate"
                    formatter={(value: number) => `${(value * 100).toFixed(2)}%`}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Usage Patterns Tab */}
        <TabsContent value="usage" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Today's Summary</CardTitle>
                <CardDescription>Current day usage statistics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Queries Today</span>
                  <Badge variant="outline">
                    {usageSummary?.total_queries_today || usageSummary?.queries?.today || 0}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Sessions Today</span>
                  <Badge variant="outline">
                    {usageSummary?.total_sessions_today || usageSummary?.sessions?.active_sessions || 0}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Active Sources</span>
                  <Badge variant="outline">{usageSummary?.active_sources || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">System Health</span>
                  <Badge className="bg-green-100 text-green-800">
                    {usageSummary?.system_health || 'Unknown'}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest system activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {usageSummary?.recent_activity && Array.isArray(usageSummary.recent_activity) ? (
                    usageSummary.recent_activity.map((activity, index) => (
                      <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity?.message || 'Unknown activity'}</p>
                          <p className="text-xs text-gray-500">
                            {safeFormatDate(activity?.timestamp)}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No recent activity</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
