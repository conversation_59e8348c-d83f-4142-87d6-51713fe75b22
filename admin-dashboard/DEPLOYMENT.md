# RAG Chatbot Admin Dashboard - Deployment Guide

## Quick Start

The admin dashboard is now ready for use! Follow these steps to get started:

### 1. Prerequisites

- Node.js 18+ installed
- RAG Chatbot API server running (typically on `http://localhost:8000`)
- Modern web browser

### 2. Installation & Setup

```bash
# Navigate to the admin dashboard directory
cd admin-dashboard

# Install dependencies (if not already done)
npm install

# Start the development server
npm run dev
```

The dashboard will be available at: **http://localhost:3001**

### 3. First Login

1. Open http://localhost:3001 in your browser
2. You'll be redirected to the login page
3. Use these credentials for development:
   - **Username:** `admin`
   - **API Key:** Any non-empty value (e.g., `test-key`)
4. Click "Sign In"

### 4. Dashboard Features

Once logged in, you'll have access to:

#### 🏠 **Dashboard Overview** (`/dashboard`)
- Real-time system metrics
- Query statistics
- Quick action buttons
- Recent activity feed

#### 📁 **Data Sources** (`/dashboard/sources`)
- Add new data sources (Website, PDF, Text)
- Edit existing sources
- Enable/disable sources
- Bulk operations
- Monitor processing status

#### 🗄️ **Database** (`/dashboard/database`)
- View vector collections
- Search documents
- Monitor storage usage
- Database analytics

#### ⚙️ **Pipelines** (`/dashboard/pipelines`)
- Start/stop data processing
- Monitor active tasks
- View worker status
- Pipeline history

#### 📊 **Monitoring** (`/dashboard/monitoring`)
- Real-time system health
- Performance metrics
- Service status
- Resource usage charts

#### 💬 **Chat Interface** (`/dashboard/chat`)
- Test the RAG system
- Interactive chat
- Response time monitoring
- Source citations

#### 🔧 **Configuration** (`/dashboard/configuration`)
- API settings
- Model parameters
- Search configuration
- System settings

#### 📈 **Analytics** (`/dashboard/analytics`)
- Usage statistics
- Performance trends
- Query analytics
- Session data

## Production Deployment

### Environment Configuration

Create a `.env.local` file:

```env
NEXT_PUBLIC_API_BASE_URL=http://your-rag-api-server:8000
NEXT_PUBLIC_APP_ENV=production
```

### Build for Production

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "start"]
```

Build and run:

```bash
docker build -t rag-admin-dashboard .
docker run -p 3001:3001 rag-admin-dashboard
```

### Nginx Configuration

For production with Nginx:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## API Integration

### RAG Chatbot API Requirements

The dashboard expects the RAG Chatbot API to be running with these endpoints:

- `GET /health` - Basic health check
- `GET /admin/health` - Detailed system status
- `GET /admin/sources` - List data sources
- `POST /admin/sources` - Create data source
- `PUT /admin/sources/{id}` - Update data source
- `DELETE /admin/sources/{id}` - Delete data source
- `POST /admin/pipeline/start` - Start pipeline
- `GET /admin/pipeline/tasks/active` - Get active tasks
- `POST /chat/query` - Send chat query
- `GET /admin/analytics/summary` - Get usage summary

### Authentication

For production, configure proper API key authentication:

1. Set up API key validation in your RAG Chatbot API
2. Update the authentication logic in `src/lib/stores/auth.ts`
3. Configure environment variables for API keys

## Troubleshooting

### Common Issues

1. **Dashboard won't load**
   - Check if Node.js is installed (`node --version`)
   - Verify dependencies are installed (`npm install`)
   - Check the console for errors

2. **Can't connect to API**
   - Ensure RAG Chatbot API is running
   - Check the API URL in environment variables
   - Verify CORS settings on the API server

3. **Authentication fails**
   - For development, any non-empty API key should work
   - For production, ensure API key validation is properly configured

4. **Charts not displaying**
   - Check browser console for JavaScript errors
   - Ensure recharts library is properly installed

### Development Mode

For development with mock data:

```bash
# The dashboard includes mock data fallbacks
# when API calls fail, so you can develop
# without the RAG API running
npm run dev
```

### Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Security Considerations

### Production Security

1. **API Keys**: Use secure, randomly generated API keys
2. **HTTPS**: Always use HTTPS in production
3. **CORS**: Configure proper CORS settings
4. **Rate Limiting**: Implement rate limiting on API endpoints
5. **Input Validation**: Validate all user inputs
6. **Error Handling**: Don't expose sensitive information in error messages

### Environment Variables

Never commit sensitive data to version control:

```env
# ❌ Don't commit
ADMIN_API_KEY=super-secret-key

# ✅ Use environment-specific values
NEXT_PUBLIC_API_BASE_URL=https://api.yourcompany.com
```

## Monitoring & Maintenance

### Health Checks

The dashboard includes built-in health monitoring:

- System status indicators
- API connectivity checks
- Performance metrics
- Error tracking

### Logs

Monitor application logs for:

- Authentication failures
- API connection issues
- JavaScript errors
- Performance problems

### Updates

To update the dashboard:

1. Pull latest changes
2. Run `npm install` for new dependencies
3. Run `npm run build` to rebuild
4. Restart the application

## Support

For issues or questions:

1. Check the browser console for errors
2. Review the API server logs
3. Verify network connectivity
4. Check the troubleshooting section above

The dashboard is designed to be robust and user-friendly, with comprehensive error handling and fallback mechanisms for a smooth user experience.
