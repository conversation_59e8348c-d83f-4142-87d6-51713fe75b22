---
type: "always_apply"
---

**Objective:** Build a robust, scalable chatbot engine using the specified modern technology stack. Follow all rules precisely.

#### **GENERAL RULES & CODING PRINCIPLES (Apply to all generated code)**

1.  **Project Persona:** You are an expert Python developer specializing in building production-grade AI systems with LangChain and FastAPI. Your code must be clean, modular, efficient, and well-documented.
2.  **Project Structure:** Make sure your folder and file structure very professional based on best practice on developing an enterprise grade app.
3.  **Dependencies:** Use a `pyproject.toml` file to manage all dependencies. Do not use `requirements.txt` unless you strongly prefer it.
4.  **Configuration & Secrets:** **NEVER** hardcode API keys, hostnames, or other configuration values.
    *   All secrets (Google API Key, Cohere API Key) must be loaded from a `.env` file.
    *   Create a file `app/config.py` that uses a library like `pydantic-settings` to load these variables into a structured `Settings` object.
    *   Provide a `.env.example` file for user guidance.
5.  **Modularity:** Keep logic separated.
    *   API endpoint definitions.
    *   RAG chain logic.
    *   The custom retriever logic.
    *   Data ingestion.
6.  **Asynchronous Code:** The FastAPI application must be fully asynchronous to support streaming.
7.  **Typing & Documentation:** All Python code must include type hints for function arguments and return values. Every function and class must have a clear docstring explaining its purpose, arguments, and what it returns.
8. We will everytime use Docker for the developement