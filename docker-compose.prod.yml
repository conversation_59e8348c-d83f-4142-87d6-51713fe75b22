version: '3.8'

services:
  # Milvus Vector Database (Production)
  etcd:
    container_name: milvus-etcd-prod
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data_prod:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  minio:
    container_name: milvus-minio-prod
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY:-minioadmin}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY:-minioadmin}
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data_prod:/data
    command: minio server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  milvus:
    container_name: milvus-standalone-prod
    image: milvusdb/milvus:v2.3.0
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data_prod:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"
    restart: always
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # Redis for caching and job queue (Production)
  redis:
    container_name: rag-redis-prod
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data_prod:/data
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # RAG Chatbot API (Production)
  api:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.api
      target: production
    container_name: rag-chatbot-api-prod
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DEBUG=false
      - PYTHONPATH=/app
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data:ro
      - ./chatbot-engine/config:/app/chatbot-engine/config:ro
      - api_logs:/app/chatbot-engine/logs
    depends_on:
      milvus:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Celery Worker for background tasks (Production)
  worker:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.worker
      target: production
    container_name: rag-chatbot-worker-prod
    environment:
      - ENVIRONMENT=production
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - PYTHONPATH=/app
      - C_FORCE_ROOT=1
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data
      - ./chatbot-engine/config:/app/chatbot-engine/config:ro
      - worker_logs:/app/chatbot-engine/logs
    depends_on:
      milvus:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: always
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Nginx Load Balancer (Production)
  nginx:
    image: nginx:alpine
    container_name: rag-chatbot-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./chatbot-engine/docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./chatbot-engine/docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: always

volumes:
  etcd_data_prod:
  minio_data_prod:
  milvus_data_prod:
  redis_data_prod:
  api_logs:
  worker_logs:
