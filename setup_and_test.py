#!/usr/bin/env python3
"""
RAG Chatbot Setup and Test Script

This script helps you set up and test your RAG chatbot system.
It provides a simple interface to configure, test, and validate the system.
"""

import os
import sys
import time
import subprocess
import requests
import json
from pathlib import Path
from typing import Dict, List, Optional

class RAGChatbotSetup:
    """Setup and test the RAG chatbot system."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.project_root = Path(__file__).parent
        self.config_dir = self.project_root / "chatbot-engine" / "config"
        
    def check_docker_services(self) -> bool:
        """Check if Docker services are running."""
        print("🐳 Checking Docker services...")
        
        try:
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.dev.yml", "ps"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                output = result.stdout
                # Check for key services
                services = ["milvus", "redis", "minio"]
                running_services = []
                
                for service in services:
                    if service in output and "Up" in output:
                        running_services.append(service)
                
                print(f"   ✅ Running services: {', '.join(running_services)}")
                
                if len(running_services) >= 2:  # At least 2 core services
                    return True
                else:
                    print("   ⚠️  Some services may not be running")
                    return False
            else:
                print("   ❌ Failed to check Docker services")
                return False
                
        except FileNotFoundError:
            print("   ❌ Docker Compose not found")
            return False
        except Exception as e:
            print(f"   ❌ Error checking Docker services: {e}")
            return False
    
    def start_docker_services(self) -> bool:
        """Start Docker services."""
        print("🚀 Starting Docker services...")
        
        try:
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.dev.yml", "up", "-d"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                print("   ✅ Docker services started")
                print("   ⏳ Waiting for services to initialize...")
                time.sleep(15)  # Give services time to start
                return True
            else:
                print(f"   ❌ Failed to start services: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error starting Docker services: {e}")
            return False
    
    def check_api_server(self) -> bool:
        """Check if API server is running."""
        print("🔌 Checking API server...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"   ✅ API server is running: {health_data.get('status')}")
                return True
            else:
                print(f"   ❌ API server returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("   ❌ API server is not running")
            return False
        except Exception as e:
            print(f"   ❌ Error checking API server: {e}")
            return False
    
    def start_api_server(self) -> bool:
        """Start the API server."""
        print("🚀 Starting API server...")
        print("   ℹ️  This will start the server in the background")
        print("   ℹ️  Check logs with: docker-compose -f docker-compose.dev.yml logs -f api")
        
        try:
            # Check if we're in the right directory structure
            api_dir = self.project_root / "chatbot-engine"
            if not api_dir.exists():
                print("   ❌ chatbot-engine directory not found")
                return False
            
            # Start API server using docker-compose
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.dev.yml", "up", "-d", "api"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                print("   ✅ API server started")
                print("   ⏳ Waiting for server to initialize...")
                time.sleep(10)
                return self.check_api_server()
            else:
                print(f"   ❌ Failed to start API server: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error starting API server: {e}")
            return False
    
    def setup_environment(self) -> bool:
        """Set up environment variables."""
        print("🔧 Setting up environment...")
        
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if not env_file.exists():
            if env_example.exists():
                print("   📋 Copying .env.example to .env")
                try:
                    with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                        dst.write(src.read())
                    print("   ✅ Created .env file")
                except Exception as e:
                    print(f"   ❌ Failed to create .env file: {e}")
                    return False
            else:
                print("   📝 Creating basic .env file")
                env_content = """# RAG Chatbot Environment Variables
GOOGLE_API_KEY=your_google_ai_api_key_here
COHERE_API_KEY=your_cohere_api_key_here
SECRET_KEY=your_secret_key_here
ADMIN_API_KEY=admin_token_123
ENVIRONMENT=development
LOG_LEVEL=INFO
"""
                try:
                    with open(env_file, 'w') as f:
                        f.write(env_content)
                    print("   ✅ Created basic .env file")
                except Exception as e:
                    print(f"   ❌ Failed to create .env file: {e}")
                    return False
        else:
            print("   ✅ .env file already exists")
        
        print("   ⚠️  Please edit .env file with your actual API keys")
        return True
    
    def test_infrastructure(self) -> bool:
        """Test infrastructure components."""
        print("🧪 Testing infrastructure...")
        
        test_script = self.project_root / "test_infrastructure.py"
        if test_script.exists():
            try:
                result = subprocess.run(
                    [sys.executable, str(test_script)],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                
                if result.returncode == 0:
                    print("   ✅ Infrastructure tests passed")
                    return True
                else:
                    print("   ❌ Infrastructure tests failed")
                    print(f"   Error: {result.stderr}")
                    return False
            except Exception as e:
                print(f"   ❌ Error running infrastructure tests: {e}")
                return False
        else:
            print("   ⚠️  Infrastructure test script not found")
            return False
    
    def add_sample_data_source(self) -> bool:
        """Add a sample data source for testing."""
        print("📚 Adding sample data source...")
        
        # Check if API is accessible
        if not self.check_api_server():
            print("   ❌ API server not available")
            return False
        
        # Add a test source
        test_source = {
            "name": "German Basic Law (Test)",
            "source_type": "website",
            "url": "https://www.gesetze-im-internet.de/gg/",
            "crawl_depth": 1,
            "enabled": True,
            "metadata": {
                "test": True,
                "category": "constitutional_law",
                "language": "de"
            }
        }
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer admin_token_123"  # Default test token
            }
            
            response = requests.post(
                f"{self.base_url}/admin/sources",
                headers=headers,
                json=test_source,
                timeout=10
            )
            
            if response.status_code == 200:
                source_data = response.json()
                print(f"   ✅ Added test source: {source_data.get('name')}")
                return True
            elif response.status_code == 409:
                print("   ℹ️  Test source already exists")
                return True
            else:
                print(f"   ❌ Failed to add test source: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error adding test source: {e}")
            return False
    
    def trigger_indexing(self) -> bool:
        """Trigger data indexing."""
        print("🔄 Triggering data indexing...")
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer admin_token_123"
            }
            
            response = requests.post(
                f"{self.base_url}/admin/reindex",
                headers=headers,
                json={"force": False},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Indexing started: {result.get('message')}")
                print(f"   🆔 Task ID: {result.get('task_id')}")
                return True
            else:
                print(f"   ❌ Failed to trigger indexing: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error triggering indexing: {e}")
            return False
    
    def test_chat_query(self) -> bool:
        """Test a simple chat query."""
        print("💬 Testing chat functionality...")
        
        try:
            # Create session
            session_response = requests.post(
                f"{self.base_url}/chat/sessions",
                headers={"Content-Type": "application/json"},
                json={"metadata": {"test": True}},
                timeout=10
            )
            
            if session_response.status_code != 200:
                print("   ❌ Failed to create chat session")
                return False
            
            session_id = session_response.json()["session_id"]
            
            # Send test query
            query_data = {
                "query": "What is law?",
                "session_id": session_id,
                "max_results": 3,
                "include_sources": True
            }
            
            response = requests.post(
                f"{self.base_url}/chat/query",
                headers={"Content-Type": "application/json"},
                json=query_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('answer', '')
                sources = result.get('sources', [])
                
                print(f"   ✅ Chat query successful")
                print(f"   📝 Answer length: {len(answer)} characters")
                print(f"   📚 Sources found: {len(sources)}")
                
                if len(answer) > 10:  # Basic validation
                    return True
                else:
                    print("   ⚠️  Answer seems too short")
                    return False
            else:
                print(f"   ❌ Chat query failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error testing chat: {e}")
            return False
    
    def run_complete_setup(self) -> bool:
        """Run complete setup and testing process."""
        print("🚀 RAG Chatbot Complete Setup and Test")
        print("=" * 60)
        
        steps = [
            ("Environment Setup", self.setup_environment),
            ("Docker Services", self.check_docker_services),
            ("Infrastructure Test", self.test_infrastructure),
            ("API Server", self.check_api_server),
            ("Sample Data Source", self.add_sample_data_source),
            ("Data Indexing", self.trigger_indexing),
            ("Chat Test", self.test_chat_query),
        ]
        
        results = []
        
        for step_name, step_func in steps:
            print(f"\n📋 Step: {step_name}")
            print("-" * 40)
            
            try:
                success = step_func()
                results.append((step_name, success))
                
                if not success and step_name in ["Docker Services", "API Server"]:
                    print(f"   🔧 Attempting to start {step_name.lower()}...")
                    if step_name == "Docker Services":
                        success = self.start_docker_services()
                    elif step_name == "API Server":
                        success = self.start_api_server()
                    
                    results[-1] = (step_name, success)
                
            except Exception as e:
                print(f"   ❌ Step failed with error: {e}")
                results.append((step_name, False))
        
        # Summary
        print("\n📊 Setup Summary")
        print("=" * 60)
        
        passed_steps = 0
        for step_name, success in results:
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"{step_name}: {status}")
            if success:
                passed_steps += 1
        
        total_steps = len(results)
        success_rate = passed_steps / total_steps
        
        print(f"\n🎯 Overall Result: {passed_steps}/{total_steps} steps completed ({success_rate:.1%})")
        
        if success_rate >= 0.8:
            print("🎉 Setup completed successfully!")
            print("\n🔗 Access Points:")
            print(f"   - API Documentation: {self.base_url}/docs")
            print(f"   - Health Check: {self.base_url}/health")
            print(f"   - Chat API: {self.base_url}/chat/query")
            print("\n📝 Next Steps:")
            print("   1. Edit .env file with your API keys")
            print("   2. Add your data sources to config/sources.yaml")
            print("   3. Run: python examples/api_usage_examples.py")
            print("   4. Run: python test_knowledge_graph.py")
        else:
            print("⚠️  Setup completed with issues. Please check the failed steps.")
            print("\n🔧 Troubleshooting:")
            print("   - Ensure Docker is running")
            print("   - Check port availability (8000, 19530, 6379)")
            print("   - Verify API keys in .env file")
            print("   - Check logs: docker-compose -f docker-compose.dev.yml logs")
        
        return success_rate >= 0.8


def main():
    """Main function."""
    setup = RAGChatbotSetup()
    
    try:
        success = setup.run_complete_setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
