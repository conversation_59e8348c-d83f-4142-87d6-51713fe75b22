version: '3.8'

services:
  # Milvus Vector Database
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data:/data
    command: minio server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  milvus:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.3.0
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

  # Redis for caching and job queue
  redis:
    container_name: rag-redis
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RAG Chatbot API
  api:
    build: .
    container_name: rag-chatbot-api
    ports:
      - "8000:8000"
    environment:
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data
      - ./chatbot-engine/config:/app/chatbot-engine/config
      - ./chatbot-engine/logs:/app/chatbot-engine/logs
    depends_on:
      milvus:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: unless-stopped

  # Celery Worker for background tasks
  worker:
    build: .
    container_name: rag-chatbot-worker
    command: celery -A src.shared.celery_app worker --loglevel=info --queues=default,crawling,processing,embeddings,storage,orchestration
    environment:
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data
      - ./chatbot-engine/config:/app/chatbot-engine/config
      - ./chatbot-engine/logs:/app/chatbot-engine/logs
    depends_on:
      milvus:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: unless-stopped

  # Celery Beat for scheduled tasks (optional)
  beat:
    build: .
    container_name: rag-chatbot-beat
    command: celery -A src.shared.celery_app beat --loglevel=info
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data
      - ./chatbot-engine/config:/app/chatbot-engine/config
      - ./chatbot-engine/logs:/app/chatbot-engine/logs
    depends_on:
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: unless-stopped

  # Flower for Celery monitoring (optional)
  flower:
    build: .
    container_name: rag-chatbot-flower
    command: celery -A src.shared.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      redis:
        condition: service_healthy
    env_file:
      - .env
    restart: unless-stopped

volumes:
  etcd_data:
  minio_data:
  milvus_data:
  redis_data:
