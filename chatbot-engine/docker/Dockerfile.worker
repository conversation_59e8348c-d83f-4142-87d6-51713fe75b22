# Multi-stage Dockerfile for Celery worker service - Production Optimized
# Stage 1: Build dependencies
FROM python:3.11-slim as builder

# Set build environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install dependencies
COPY requirements.prod.txt requirements.txt ./
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.prod.txt

# Stage 2: Production runtime
FROM python:3.11-slim as production

# Security: Install security updates and minimal runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    ENVIRONMENT=production \
    C_FORCE_ROOT=1

# Create non-root user with specific UID/GID for security
RUN groupadd -r -g 1002 workergroup && \
    useradd -r -u 1002 -g workergroup -d /app -s /bin/bash worker

# Create app directory and set permissions
WORKDIR /app
RUN mkdir -p /app/chatbot-engine/logs /app/chatbot-engine/data && \
    chown -R worker:workergroup /app

# Copy entrypoint script
COPY --chown=worker:workergroup chatbot-engine/docker/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Copy application code with proper ownership
COPY --chown=worker:workergroup . .

# Switch to non-root user
USER worker

# Health check for worker with improved configuration
HEALTHCHECK --interval=60s --timeout=30s --start-period=30s --retries=3 \
    CMD celery -A chatbot-engine.src.shared.celery_app inspect ping -d celery@$HOSTNAME || exit 1

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]

# Production-optimized worker command
CMD ["celery", "-A", "chatbot-engine.src.shared.celery_app", "worker", \
     "--loglevel=info", \
     "--concurrency=2", \
     "--prefetch-multiplier=1", \
     "--max-tasks-per-child=1000", \
     "--time-limit=300", \
     "--soft-time-limit=240"]
