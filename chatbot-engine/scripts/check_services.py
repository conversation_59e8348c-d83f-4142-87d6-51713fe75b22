#!/usr/bin/env python3
"""Health check script for Docker services integration."""

import asyncio
import sys
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from shared.config import get_settings
    from shared.logging_config import get_logger
    from offline_pipeline.storage.vector_store import MilvusVectorStore
    from offline_pipeline.storage.metadata_store import MetadataStore
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    print("Make sure you're running this from the project root directory.")
    sys.exit(1)

logger = get_logger(__name__)


async def check_redis_connection():
    """Check Redis connection."""
    try:
        import redis
        settings = get_settings()
        
        r = redis.Redis(
            host=settings.redis_host,
            port=settings.redis_port,
            db=settings.redis_db,
            socket_timeout=5
        )
        
        # Test connection
        r.ping()
        
        # Test basic operations
        r.set("health_check", "ok", ex=10)
        value = r.get("health_check")
        
        if value == b"ok":
            print("✅ Redis: Connection successful")
            return True
        else:
            print("❌ Redis: Basic operations failed")
            return False
            
    except Exception as e:
        print(f"❌ Redis: Connection failed - {e}")
        return False


async def check_milvus_connection():
    """Check Milvus connection."""
    try:
        vector_store = MilvusVectorStore()
        await vector_store.initialize()
        
        # Test collection operations
        stats = await vector_store.get_collection_stats()
        
        await vector_store.close()
        
        print("✅ Milvus: Connection successful")
        print(f"   Collection: {stats.get('collection_name', 'N/A')}")
        print(f"   Entities: {stats.get('total_entities', 0)}")
        return True
        
    except Exception as e:
        print(f"❌ Milvus: Connection failed - {e}")
        return False


async def check_metadata_store():
    """Check metadata store (SQLite)."""
    try:
        metadata_store = MetadataStore()
        await metadata_store.initialize()
        
        # Test basic operations
        stats = await metadata_store.get_processing_stats()
        
        print("✅ Metadata Store: Connection successful")
        print(f"   Total sources: {stats.get('total_sources', 0)}")
        return True
        
    except Exception as e:
        print(f"❌ Metadata Store: Connection failed - {e}")
        return False


async def check_celery_broker():
    """Check Celery broker (Redis) connection."""
    try:
        from celery import Celery
        settings = get_settings()
        
        # Create temporary Celery app for testing
        test_app = Celery(
            'health_check',
            broker=f'redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}'
        )
        
        # Check broker connection
        inspect = test_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print("✅ Celery Broker: Connection successful")
            print(f"   Active workers: {len(stats)}")
            return True
        else:
            print("⚠️  Celery Broker: Connected but no workers found")
            return True  # Broker is working, workers might not be started yet
            
    except Exception as e:
        print(f"❌ Celery Broker: Connection failed - {e}")
        return False


def check_environment_variables():
    """Check required environment variables."""
    try:
        settings = get_settings()
        issues = []
        
        # Check API keys
        if not settings.google_api_key or settings.google_api_key == "test_google_key":
            issues.append("GOOGLE_API_KEY not configured")
        
        if not settings.cohere_api_key or settings.cohere_api_key == "test_cohere_key":
            issues.append("COHERE_API_KEY not configured")
        
        # Check data directories
        data_dir = Path(settings.data_dir)
        if not data_dir.exists():
            issues.append(f"Data directory does not exist: {data_dir}")
        
        if issues:
            print("⚠️  Environment Variables: Issues found")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ Environment Variables: All required variables configured")
            return True
            
    except Exception as e:
        print(f"❌ Environment Variables: Check failed - {e}")
        return False


async def check_data_directories():
    """Check data directories exist and are writable."""
    try:
        settings = get_settings()
        directories = [
            settings.data_dir,
            settings.crawled_data_dir,
            settings.processed_data_dir,
            settings.indexes_dir
        ]
        
        all_good = True
        for dir_path in directories:
            path = Path(dir_path)
            
            if not path.exists():
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    print(f"✅ Created directory: {dir_path}")
                except Exception as e:
                    print(f"❌ Cannot create directory {dir_path}: {e}")
                    all_good = False
            else:
                # Test write permissions
                try:
                    test_file = path / ".write_test"
                    test_file.write_text("test")
                    test_file.unlink()
                    print(f"✅ Directory writable: {dir_path}")
                except Exception as e:
                    print(f"❌ Directory not writable {dir_path}: {e}")
                    all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Data Directories: Check failed - {e}")
        return False


async def main():
    """Run all health checks."""
    print("🔍 Checking Docker Services Integration...")
    print("=" * 50)
    
    checks = [
        ("Environment Variables", check_environment_variables),
        ("Data Directories", check_data_directories),
        ("Redis Connection", check_redis_connection),
        ("Milvus Connection", check_milvus_connection),
        ("Metadata Store", check_metadata_store),
        ("Celery Broker", check_celery_broker),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}:")
        try:
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}: Unexpected error - {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 HEALTH CHECK SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All services are healthy and ready!")
        return 0
    else:
        print("⚠️  Some services have issues. Check the details above.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n❌ Health check interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        sys.exit(1)
