#!/bin/bash

# Docker utility script for RAG Legal Chatbot
# This script provides common Docker operations for development and production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Copying from .env.example"
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_warning "Please edit .env file with your API keys before continuing."
            exit 1
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
}

# Function to build Docker images
build_images() {
    local env=${1:-dev}
    print_header "Building Docker Images for $env environment"
    
    if [ "$env" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml build --no-cache
    else
        docker-compose -f docker-compose.dev.yml build --no-cache
    fi
    
    print_status "Docker images built successfully"
}

# Function to start services
start_services() {
    local env=${1:-dev}
    print_header "Starting Services for $env environment"
    
    check_docker
    check_env_file
    
    if [ "$env" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose -f docker-compose.dev.yml up -d
    fi
    
    print_status "Services started successfully"
    print_status "API will be available at: http://localhost:8000"
    
    if [ "$env" = "dev" ]; then
        print_status "Flower (Celery monitoring) will be available at: http://localhost:5555"
    fi
}

# Function to stop services
stop_services() {
    local env=${1:-dev}
    print_header "Stopping Services for $env environment"
    
    if [ "$env" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose -f docker-compose.dev.yml down
    fi
    
    print_status "Services stopped successfully"
}

# Function to restart services
restart_services() {
    local env=${1:-dev}
    print_header "Restarting Services for $env environment"
    
    stop_services $env
    start_services $env
}

# Function to show logs
show_logs() {
    local env=${1:-dev}
    local service=${2:-}
    
    print_header "Showing Logs for $env environment"
    
    if [ "$env" = "prod" ]; then
        if [ -n "$service" ]; then
            docker-compose -f docker-compose.prod.yml logs -f $service
        else
            docker-compose -f docker-compose.prod.yml logs -f
        fi
    else
        if [ -n "$service" ]; then
            docker-compose -f docker-compose.dev.yml logs -f $service
        else
            docker-compose -f docker-compose.dev.yml logs -f
        fi
    fi
}

# Function to show service status
show_status() {
    local env=${1:-dev}
    print_header "Service Status for $env environment"
    
    if [ "$env" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose -f docker-compose.dev.yml ps
    fi
}

# Function to clean up Docker resources
cleanup() {
    print_header "Cleaning up Docker resources"
    
    print_status "Stopping all containers..."
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    print_status "Removing unused images..."
    docker image prune -f
    
    print_status "Removing unused volumes..."
    docker volume prune -f
    
    print_status "Removing unused networks..."
    docker network prune -f
    
    print_status "Cleanup completed"
}

# Function to backup data
backup_data() {
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    print_header "Backing up data to $backup_dir"
    
    mkdir -p $backup_dir
    
    # Backup Milvus data
    if docker volume ls | grep -q milvus_data; then
        print_status "Backing up Milvus data..."
        docker run --rm -v milvus_data_dev:/data -v $(pwd)/$backup_dir:/backup alpine tar czf /backup/milvus_data.tar.gz -C /data .
    fi
    
    # Backup Redis data
    if docker volume ls | grep -q redis_data; then
        print_status "Backing up Redis data..."
        docker run --rm -v redis_data_dev:/data -v $(pwd)/$backup_dir:/backup alpine tar czf /backup/redis_data.tar.gz -C /data .
    fi
    
    # Backup application data
    if [ -d "./chatbot-engine/data" ]; then
        print_status "Backing up application data..."
        tar czf $backup_dir/app_data.tar.gz ./chatbot-engine/data
    fi
    
    print_status "Backup completed: $backup_dir"
}

# Function to restore data
restore_data() {
    local backup_dir=$1
    if [ -z "$backup_dir" ]; then
        print_error "Please specify backup directory"
        exit 1
    fi
    
    if [ ! -d "$backup_dir" ]; then
        print_error "Backup directory not found: $backup_dir"
        exit 1
    fi
    
    print_header "Restoring data from $backup_dir"
    
    # Stop services first
    stop_services dev
    stop_services prod
    
    # Restore Milvus data
    if [ -f "$backup_dir/milvus_data.tar.gz" ]; then
        print_status "Restoring Milvus data..."
        docker run --rm -v milvus_data_dev:/data -v $(pwd)/$backup_dir:/backup alpine tar xzf /backup/milvus_data.tar.gz -C /data
    fi
    
    # Restore Redis data
    if [ -f "$backup_dir/redis_data.tar.gz" ]; then
        print_status "Restoring Redis data..."
        docker run --rm -v redis_data_dev:/data -v $(pwd)/$backup_dir:/backup alpine tar xzf /backup/redis_data.tar.gz -C /data
    fi
    
    # Restore application data
    if [ -f "$backup_dir/app_data.tar.gz" ]; then
        print_status "Restoring application data..."
        tar xzf $backup_dir/app_data.tar.gz
    fi
    
    print_status "Data restoration completed"
}

# Function to show help
show_help() {
    echo "Docker Utility Script for RAG Legal Chatbot"
    echo ""
    echo "Usage: $0 <command> [environment] [options]"
    echo ""
    echo "Commands:"
    echo "  build [dev|prod]           Build Docker images"
    echo "  start [dev|prod]           Start services"
    echo "  stop [dev|prod]            Stop services"
    echo "  restart [dev|prod]         Restart services"
    echo "  logs [dev|prod] [service]  Show logs"
    echo "  status [dev|prod]          Show service status"
    echo "  cleanup                    Clean up Docker resources"
    echo "  backup                     Backup data volumes"
    echo "  restore <backup_dir>       Restore data from backup"
    echo "  help                       Show this help message"
    echo ""
    echo "Environment:"
    echo "  dev    Development environment (default)"
    echo "  prod   Production environment"
    echo ""
    echo "Examples:"
    echo "  $0 start dev               Start development environment"
    echo "  $0 logs prod api           Show production API logs"
    echo "  $0 backup                  Backup all data"
    echo "  $0 restore ./backups/20240101_120000"
}

# Main script logic
case "${1:-help}" in
    build)
        build_images ${2:-dev}
        ;;
    start)
        start_services ${2:-dev}
        ;;
    stop)
        stop_services ${2:-dev}
        ;;
    restart)
        restart_services ${2:-dev}
        ;;
    logs)
        show_logs ${2:-dev} $3
        ;;
    status)
        show_status ${2:-dev}
        ;;
    cleanup)
        cleanup
        ;;
    backup)
        backup_data
        ;;
    restore)
        restore_data $2
        ;;
    help|*)
        show_help
        ;;
esac
