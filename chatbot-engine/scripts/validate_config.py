#!/usr/bin/env python3
"""
Configuration validation script for RAG Legal Chatbot.

This script validates the configuration files and environment setup.
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, <PERSON><PERSON>
import yaml
from pydantic import ValidationError

# Add the src directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from shared.config import Settings, get_settings
    from shared.utils import load_yaml_file
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    print("Make sure you're running this from the project root directory.")
    sys.exit(1)


def validate_yaml_files() -> List[Tuple[str, bool, str]]:
    """Validate YAML configuration files."""
    config_files = [
        "chatbot-engine/config/app_config.yaml",
        "chatbot-engine/config/development.yaml",
        "chatbot-engine/config/production.yaml",
        "chatbot-engine/config/sources.yaml",
    ]
    
    results = []
    for config_file in config_files:
        try:
            data = load_yaml_file(config_file)
            if data:
                results.append((config_file, True, "Valid YAML"))
            else:
                results.append((config_file, False, "Empty or invalid YAML"))
        except Exception as e:
            results.append((config_file, False, f"Error: {e}"))
    
    return results


def validate_environment_variables() -> List[Tuple[str, bool, str]]:
    """Validate required environment variables."""
    required_vars = [
        ("GOOGLE_API_KEY", "Google AI API key"),
        ("COHERE_API_KEY", "Cohere API key"),
        ("SECRET_KEY", "Application secret key"),
    ]
    
    optional_vars = [
        ("MILVUS_HOST", "Milvus database host"),
        ("REDIS_HOST", "Redis host"),
        ("ENVIRONMENT", "Application environment"),
    ]
    
    results = []
    
    # Check required variables
    for var_name, description in required_vars:
        value = os.getenv(var_name)
        if not value:
            results.append((var_name, False, f"Missing: {description}"))
        elif value.startswith("your_"):
            results.append((var_name, False, f"Not configured: {description}"))
        else:
            results.append((var_name, True, f"Set: {description}"))
    
    # Check optional variables
    for var_name, description in optional_vars:
        value = os.getenv(var_name)
        if value:
            results.append((var_name, True, f"Set: {description}"))
        else:
            results.append((var_name, False, f"Using default: {description}"))
    
    return results


def validate_settings_model() -> Tuple[bool, str]:
    """Validate that the Settings model can be instantiated."""
    try:
        settings = get_settings()
        return True, "Settings model validation successful"
    except ValidationError as e:
        return False, f"Settings validation failed: {e}"
    except Exception as e:
        return False, f"Unexpected error: {e}"


def validate_directory_structure() -> List[Tuple[str, bool, str]]:
    """Validate that required directories exist."""
    required_dirs = [
        "chatbot-engine/src",
        "chatbot-engine/config",
        "chatbot-engine/data",
        "chatbot-engine/tests",
        "chatbot-engine/scripts",
        "chatbot-engine/data/sources",
        "chatbot-engine/data/crawled",
        "chatbot-engine/data/processed",
        "chatbot-engine/data/indexes",
        "chatbot-engine/logs",
    ]
    
    results = []
    for directory in required_dirs:
        path = Path(directory)
        if path.exists() and path.is_dir():
            results.append((directory, True, "Directory exists"))
        else:
            results.append((directory, False, "Directory missing"))
    
    return results


def validate_dependencies() -> List[Tuple[str, bool, str]]:
    """Validate that required Python packages are installed."""
    required_packages = [
        "langchain",
        "fastapi",
        "uvicorn",
        "pydantic",
        "pymilvus",
        "redis",
        "celery",
        "pyyaml",
        "python-dotenv",
    ]
    
    results = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            results.append((package, True, "Package installed"))
        except ImportError:
            results.append((package, False, "Package not installed"))
    
    return results


def print_results(title: str, results: List[Tuple[str, bool, str]]) -> int:
    """Print validation results."""
    print(f"\n{title}")
    print("=" * len(title))
    
    failed_count = 0
    for item, success, message in results:
        status = "✓" if success else "❌"
        print(f"{status} {item}: {message}")
        if not success:
            failed_count += 1
    
    return failed_count


def main() -> None:
    """Main validation function."""
    print("🔍 Validating RAG Legal Chatbot configuration...\n")
    
    total_failures = 0
    
    # Validate YAML files
    yaml_results = validate_yaml_files()
    total_failures += print_results("YAML Configuration Files", yaml_results)
    
    # Validate environment variables
    env_results = validate_environment_variables()
    total_failures += print_results("Environment Variables", env_results)
    
    # Validate settings model
    settings_success, settings_message = validate_settings_model()
    total_failures += print_results("Settings Model", [("Settings", settings_success, settings_message)])
    
    # Validate directory structure
    dir_results = validate_directory_structure()
    total_failures += print_results("Directory Structure", dir_results)
    
    # Validate dependencies
    dep_results = validate_dependencies()
    total_failures += print_results("Python Dependencies", dep_results)
    
    # Summary
    print(f"\n{'='*50}")
    if total_failures == 0:
        print("🎉 All validations passed! Configuration is ready.")
        sys.exit(0)
    else:
        print(f"❌ {total_failures} validation(s) failed. Please fix the issues above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
