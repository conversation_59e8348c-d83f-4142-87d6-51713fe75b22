#!/usr/bin/env python3
"""
Environment setup script for RAG Legal Chatbot.

This script helps set up the development environment by:
1. Creating necessary directories
2. Checking for required environment variables
3. Validating API keys
4. Setting up initial configuration
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import yaml


def create_directories() -> None:
    """Create necessary directories for the application."""
    directories = [
        "chatbot-engine/data/sources",
        "chatbot-engine/data/crawled",
        "chatbot-engine/data/processed",
        "chatbot-engine/data/indexes",
        "chatbot-engine/logs",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")


def check_python_version() -> bool:
    """Check if Python version is compatible."""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    print(f"✓ Python version: {sys.version}")
    return True


def check_environment_file() -> bool:
    """Check if .env file exists and has required variables."""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found. Please copy .env.example to .env and configure it.")
        return False
    
    required_vars = [
        "GOOGLE_API_KEY",
        "COHERE_API_KEY",
        "SECRET_KEY",
    ]
    
    missing_vars = []
    with open(env_file, 'r') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content or f"{var}=your_" in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing or unconfigured environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✓ Environment file configured")
    return True


def check_docker() -> bool:
    """Check if Docker and Docker Compose are available."""
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
        subprocess.run(["docker-compose", "--version"], capture_output=True, check=True)
        print("✓ Docker and Docker Compose are available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker or Docker Compose not found. Please install Docker.")
        return False


def install_dependencies() -> bool:
    """Install Python dependencies."""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✓ Python dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def validate_api_keys() -> Dict[str, bool]:
    """Validate API keys by making test requests."""
    results = {}
    
    # Note: This is a placeholder for actual API validation
    # In a real implementation, you would make test API calls
    
    google_api_key = os.getenv("GOOGLE_API_KEY")
    cohere_api_key = os.getenv("COHERE_API_KEY")
    
    results["google"] = bool(google_api_key and not google_api_key.startswith("your_"))
    results["cohere"] = bool(cohere_api_key and not cohere_api_key.startswith("your_"))
    
    for service, valid in results.items():
        status = "✓" if valid else "❌"
        print(f"{status} {service.title()} API key: {'Valid' if valid else 'Invalid or missing'}")
    
    return results


def setup_git_hooks() -> None:
    """Set up Git pre-commit hooks for code quality."""
    hooks_dir = Path(".git/hooks")
    if hooks_dir.exists():
        pre_commit_hook = hooks_dir / "pre-commit"
        hook_content = """#!/bin/sh
# Pre-commit hook for code quality checks
make format
make lint
make type-check
"""
        with open(pre_commit_hook, 'w') as f:
            f.write(hook_content)
        pre_commit_hook.chmod(0o755)
        print("✓ Git pre-commit hooks set up")
    else:
        print("⚠️  Git repository not found, skipping hooks setup")


def create_sample_data() -> None:
    """Create sample data files for testing."""
    sources_file = Path("chatbot-engine/data/sources/sample.txt")
    sources_file.parent.mkdir(parents=True, exist_ok=True)
    
    sample_content = """
    § 1 BGB - Beginn der Rechtsfähigkeit
    Die Rechtsfähigkeit des Menschen beginnt mit der Vollendung der Geburt.
    
    § 2 BGB - Eintritt der Volljährigkeit
    Die Volljährigkeit tritt mit der Vollendung des 18. Lebensjahres ein.
    """
    
    with open(sources_file, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print("✓ Sample data created")


def main() -> None:
    """Main setup function."""
    print("🚀 Setting up RAG Legal Chatbot environment...\n")
    
    checks = [
        ("Python version", check_python_version),
        ("Environment file", check_environment_file),
        ("Docker availability", check_docker),
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"Checking {name}...")
        if not check_func():
            all_passed = False
        print()
    
    if not all_passed:
        print("❌ Some checks failed. Please fix the issues above before continuing.")
        sys.exit(1)
    
    print("Setting up environment...")
    create_directories()
    
    if input("Install Python dependencies? (y/N): ").lower() == 'y':
        install_dependencies()
    
    print("\nValidating API keys...")
    validate_api_keys()
    
    setup_git_hooks()
    create_sample_data()
    
    print("\n🎉 Environment setup complete!")
    print("\nNext steps:")
    print("1. Start services: docker-compose up -d")
    print("2. Run tests: make test")
    print("3. Start development server: make start-api")


if __name__ == "__main__":
    main()
