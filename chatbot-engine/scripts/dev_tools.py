#!/usr/bin/env python3
"""
Development tools script for RAG Legal Chatbot.

This script provides common development tasks like running tests,
code formatting, linting, and other quality checks.
"""

import subprocess
import sys
import argparse
from pathlib import Path
from typing import List, Optional


def run_command(cmd: List[str], cwd: Optional[str] = None) -> int:
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, cwd=cwd, check=False)
        return result.returncode
    except FileNotFoundError:
        print(f"Error: Command not found: {cmd[0]}")
        return 1


def format_code() -> int:
    """Format code using black and isort."""
    print("🎨 Formatting code...")
    
    # Run black
    black_result = run_command([
        "black", 
        "chatbot-engine/src", 
        "chatbot-engine/tests",
        "--line-length", "88"
    ])
    
    # Run isort
    isort_result = run_command([
        "isort", 
        "chatbot-engine/src", 
        "chatbot-engine/tests",
        "--profile", "black",
        "--line-length", "88"
    ])
    
    if black_result == 0 and isort_result == 0:
        print("✅ Code formatting completed successfully")
        return 0
    else:
        print("❌ Code formatting failed")
        return 1


def lint_code() -> int:
    """Run linting checks."""
    print("🔍 Running linting checks...")
    
    # Run flake8
    flake8_result = run_command([
        "flake8", 
        "chatbot-engine/src",
        "--max-line-length", "88",
        "--extend-ignore", "E203,W503"
    ])
    
    if flake8_result == 0:
        print("✅ Linting passed")
        return 0
    else:
        print("❌ Linting failed")
        return 1


def type_check() -> int:
    """Run type checking with mypy."""
    print("🔬 Running type checks...")
    
    mypy_result = run_command([
        "mypy", 
        "chatbot-engine/src",
        "--ignore-missing-imports"
    ])
    
    if mypy_result == 0:
        print("✅ Type checking passed")
        return 0
    else:
        print("❌ Type checking failed")
        return 1


def run_tests(test_type: str = "all") -> int:
    """Run tests."""
    print(f"🧪 Running {test_type} tests...")
    
    cmd = ["pytest", "-v"]
    
    if test_type == "unit":
        cmd.extend(["-m", "unit", "chatbot-engine/tests/unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration", "chatbot-engine/tests/integration"])
    elif test_type == "e2e":
        cmd.extend(["-m", "e2e", "chatbot-engine/tests/e2e"])
    elif test_type == "all":
        cmd.append("chatbot-engine/tests")
    else:
        print(f"Unknown test type: {test_type}")
        return 1
    
    # Add coverage if running all tests
    if test_type == "all":
        cmd.extend([
            "--cov=chatbot-engine/src",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
    
    result = run_command(cmd)
    
    if result == 0:
        print("✅ Tests passed")
        return 0
    else:
        print("❌ Tests failed")
        return 1


def security_check() -> int:
    """Run security checks with bandit."""
    print("🔒 Running security checks...")
    
    bandit_result = run_command([
        "bandit", 
        "-r", "chatbot-engine/src",
        "-f", "json",
        "-o", "security-report.json"
    ])
    
    if bandit_result == 0:
        print("✅ Security checks passed")
        return 0
    else:
        print("❌ Security issues found - check security-report.json")
        return 1


def check_dependencies() -> int:
    """Check for outdated dependencies."""
    print("📦 Checking dependencies...")
    
    # Check for outdated packages
    pip_result = run_command(["pip", "list", "--outdated"])
    
    # Check for security vulnerabilities
    safety_result = run_command(["safety", "check"])
    
    if pip_result == 0 and safety_result == 0:
        print("✅ Dependencies are up to date and secure")
        return 0
    else:
        print("⚠️  Some dependencies may need attention")
        return 1


def full_check() -> int:
    """Run all quality checks."""
    print("🚀 Running full quality check suite...")
    
    checks = [
        ("Format", format_code),
        ("Lint", lint_code),
        ("Type Check", type_check),
        ("Tests", lambda: run_tests("all")),
        ("Security", security_check),
    ]
    
    failed_checks = []
    
    for check_name, check_func in checks:
        print(f"\n--- {check_name} ---")
        if check_func() != 0:
            failed_checks.append(check_name)
    
    print(f"\n{'='*50}")
    if not failed_checks:
        print("🎉 All quality checks passed!")
        return 0
    else:
        print(f"❌ Failed checks: {', '.join(failed_checks)}")
        return 1


def setup_pre_commit() -> int:
    """Set up pre-commit hooks."""
    print("🪝 Setting up pre-commit hooks...")
    
    # Install pre-commit
    install_result = run_command(["pip", "install", "pre-commit"])
    if install_result != 0:
        return install_result
    
    # Install hooks
    hooks_result = run_command(["pre-commit", "install"])
    
    if hooks_result == 0:
        print("✅ Pre-commit hooks installed successfully")
        return 0
    else:
        print("❌ Failed to install pre-commit hooks")
        return 1


def clean_cache() -> int:
    """Clean Python cache files."""
    print("🧹 Cleaning cache files...")
    
    # Remove __pycache__ directories
    pycache_result = run_command([
        "find", ".", "-type", "d", "-name", "__pycache__", "-exec", "rm", "-rf", "{}", "+"
    ])
    
    # Remove .pyc files
    pyc_result = run_command([
        "find", ".", "-name", "*.pyc", "-delete"
    ])
    
    # Remove pytest cache
    pytest_cache = Path(".pytest_cache")
    if pytest_cache.exists():
        run_command(["rm", "-rf", str(pytest_cache)])
    
    # Remove mypy cache
    mypy_cache = Path(".mypy_cache")
    if mypy_cache.exists():
        run_command(["rm", "-rf", str(mypy_cache)])
    
    print("✅ Cache cleaned")
    return 0


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Development tools for RAG Legal Chatbot")
    parser.add_argument(
        "command",
        choices=[
            "format", "lint", "type-check", "test", "security", 
            "deps", "full-check", "setup-hooks", "clean"
        ],
        help="Command to run"
    )
    parser.add_argument(
        "--test-type",
        choices=["unit", "integration", "e2e", "all"],
        default="all",
        help="Type of tests to run (only for 'test' command)"
    )
    
    args = parser.parse_args()
    
    # Map commands to functions
    commands = {
        "format": format_code,
        "lint": lint_code,
        "type-check": type_check,
        "test": lambda: run_tests(args.test_type),
        "security": security_check,
        "deps": check_dependencies,
        "full-check": full_check,
        "setup-hooks": setup_pre_commit,
        "clean": clean_cache,
    }
    
    if args.command in commands:
        exit_code = commands[args.command]()
        sys.exit(exit_code)
    else:
        print(f"Unknown command: {args.command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
