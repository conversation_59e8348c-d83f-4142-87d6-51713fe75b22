#!/usr/bin/env python3
"""Initialize services and prepare for offline pipeline execution."""

import asyncio
import sys
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from shared.config import get_settings
    from shared.logging_config import get_logger
    from offline_pipeline.storage.vector_store import MilvusVectorStore
    from offline_pipeline.storage.metadata_store import MetadataStore
    from offline_pipeline.source_manager import DataSourceManager
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    print("Make sure you're running this from the project root directory.")
    sys.exit(1)

logger = get_logger(__name__)


async def wait_for_services(max_wait=300):
    """Wait for services to be ready."""
    print("⏳ Waiting for services to be ready...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            # Check Redis
            import redis
            settings = get_settings()
            r = redis.Redis(
                host=settings.redis_host,
                port=settings.redis_port,
                db=settings.redis_db,
                socket_timeout=2
            )
            r.ping()
            
            # Check Milvus
            vector_store = MilvusVectorStore()
            await vector_store.initialize()
            await vector_store.close()
            
            print("✅ All services are ready!")
            return True
            
        except Exception as e:
            print(f"⏳ Services not ready yet: {e}")
            await asyncio.sleep(5)
    
    print("❌ Services did not become ready within timeout")
    return False


async def initialize_vector_store():
    """Initialize the vector store."""
    try:
        print("🔧 Initializing vector store...")
        
        vector_store = MilvusVectorStore()
        await vector_store.initialize()
        
        # Get collection stats
        stats = await vector_store.get_collection_stats()
        print(f"✅ Vector store initialized: {stats.get('collection_name')}")
        print(f"   Entities: {stats.get('total_entities', 0)}")
        
        await vector_store.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize vector store: {e}")
        return False


async def initialize_metadata_store():
    """Initialize the metadata store."""
    try:
        print("🔧 Initializing metadata store...")
        
        metadata_store = MetadataStore()
        await metadata_store.initialize()
        
        # Get stats
        stats = await metadata_store.get_processing_stats()
        print(f"✅ Metadata store initialized")
        print(f"   Total sources: {stats.get('total_sources', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize metadata store: {e}")
        return False


async def sync_data_sources():
    """Sync data sources from configuration."""
    try:
        print("🔧 Syncing data sources...")
        
        source_manager = DataSourceManager()
        await source_manager.initialize()
        
        # Sync sources from config to database
        success = await source_manager.sync_sources_to_database()
        
        if success:
            # Get all sources
            sources = await source_manager.get_all_sources()
            enabled_sources = [s for s in sources if s.enabled]
            
            print(f"✅ Data sources synced")
            print(f"   Total sources: {len(sources)}")
            print(f"   Enabled sources: {len(enabled_sources)}")
            
            # List enabled sources
            if enabled_sources:
                print("   Enabled sources:")
                for source in enabled_sources:
                    print(f"     - {source.name} ({source.source_type})")
            
            return True
        else:
            print("❌ Failed to sync data sources")
            return False
        
    except Exception as e:
        print(f"❌ Failed to sync data sources: {e}")
        return False


async def validate_configuration():
    """Validate the configuration."""
    try:
        print("🔧 Validating configuration...")
        
        source_manager = DataSourceManager()
        await source_manager.initialize()
        
        validation_result = await source_manager.validate_sources()
        
        if validation_result.get("error"):
            print(f"❌ Configuration validation failed: {validation_result['error']}")
            return False
        
        total_sources = validation_result.get("total_sources", 0)
        valid_sources = validation_result.get("valid_sources", 0)
        invalid_sources = validation_result.get("invalid_sources", [])
        
        print(f"✅ Configuration validated")
        print(f"   Total sources: {total_sources}")
        print(f"   Valid sources: {valid_sources}")
        print(f"   Invalid sources: {len(invalid_sources)}")
        
        if invalid_sources:
            print("   Issues found:")
            for invalid in invalid_sources:
                print(f"     - {invalid['source_name']}: {', '.join(invalid['issues'])}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


def create_data_directories():
    """Create necessary data directories."""
    try:
        print("🔧 Creating data directories...")
        
        settings = get_settings()
        directories = [
            settings.data_dir,
            settings.crawled_data_dir,
            settings.processed_data_dir,
            settings.indexes_dir,
            Path(settings.data_dir) / "logs"
        ]
        
        created_count = 0
        for dir_path in directories:
            path = Path(dir_path)
            if not path.exists():
                path.mkdir(parents=True, exist_ok=True)
                created_count += 1
                print(f"   Created: {dir_path}")
        
        if created_count > 0:
            print(f"✅ Created {created_count} directories")
        else:
            print("✅ All directories already exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create directories: {e}")
        return False


async def test_celery_connection():
    """Test Celery connection."""
    try:
        print("🔧 Testing Celery connection...")
        
        from celery import Celery
        settings = get_settings()
        
        # Create test Celery app
        test_app = Celery(
            'init_test',
            broker=f'redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}'
        )
        
        # Test broker connection
        inspect = test_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print(f"✅ Celery broker connected with {len(stats)} workers")
        else:
            print("⚠️  Celery broker connected but no workers found")
            print("   Start workers with: celery -A src.shared.celery_app worker")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery connection failed: {e}")
        return False


async def main():
    """Run initialization sequence."""
    print("🚀 Initializing RAG Chatbot Services")
    print("=" * 50)
    
    # Step 1: Create directories
    if not create_data_directories():
        return 1
    
    # Step 2: Wait for services
    if not await wait_for_services():
        return 1
    
    # Step 3: Initialize stores
    if not await initialize_metadata_store():
        return 1
    
    if not await initialize_vector_store():
        return 1
    
    # Step 4: Sync data sources
    if not await sync_data_sources():
        return 1
    
    # Step 5: Validate configuration
    if not await validate_configuration():
        print("⚠️  Configuration has issues but continuing...")
    
    # Step 6: Test Celery
    await test_celery_connection()
    
    print("\n" + "=" * 50)
    print("🎉 INITIALIZATION COMPLETE!")
    print("=" * 50)
    print("✅ All services are initialized and ready")
    print("✅ Data sources are synced")
    print("✅ Vector store is ready")
    print("✅ Metadata store is ready")
    print("\n📋 Next steps:")
    print("   1. Start Celery workers: make start-worker")
    print("   2. Run pipeline: python -m src.offline_pipeline.pipeline")
    print("   3. Monitor with Flower: http://localhost:5555")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n❌ Initialization interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        sys.exit(1)
