"""
Configuration management service for admin interface.

This module provides comprehensive configuration management capabilities including:
- Dynamic configuration updates
- Configuration validation
- Hot-reload capabilities
- Configuration backup and restore
"""

import asyncio
import json
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from copy import deepcopy

import sys

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.config import get_settings, Settings
from shared.logging_config import get_logger
from shared.utils import load_yaml_file, save_yaml_file

logger = get_logger(__name__)


class ConfigManager:
    """
    Configuration management service for admin interface.
    
    Provides dynamic configuration management with validation,
    hot-reload capabilities, and backup/restore functionality.
    """
    
    def __init__(self):
        """Initialize the configuration manager."""
        self.settings = get_settings()
        self._config_cache: Dict[str, Any] = {}
        self._config_history: List[Dict[str, Any]] = []
        self._max_history_size = 50
        
        # Configuration file paths
        self.config_dir = Path("config")
        self.app_config_path = self.config_dir / "app_config.yaml"
        self.sources_config_path = self.config_dir / "sources.yaml"
        self.development_config_path = self.config_dir / "development.yaml"
        self.production_config_path = self.config_dir / "production.yaml"
        
        logger.info("Configuration manager initialized")
    
    async def initialize(self):
        """Initialize the configuration manager."""
        try:
            # Load current configurations into cache
            await self._load_all_configs()
            logger.info("Configuration manager initialization completed")
        except Exception as e:
            logger.error(f"Configuration manager initialization failed: {e}", exc_info=True)
            raise
    
    async def _load_all_configs(self):
        """Load all configuration files into cache."""
        try:
            config_files = {
                "app_config": self.app_config_path,
                "sources": self.sources_config_path,
                "development": self.development_config_path,
                "production": self.production_config_path
            }
            
            for config_name, config_path in config_files.items():
                if config_path.exists():
                    config_data = load_yaml_file(config_path)
                    self._config_cache[config_name] = config_data
                    logger.info(f"Loaded configuration: {config_name}")
                else:
                    logger.warning(f"Configuration file not found: {config_path}")
                    self._config_cache[config_name] = {}
        except Exception as e:
            logger.error(f"Error loading configurations: {e}", exc_info=True)
            raise
    
    async def get_config(self, config_name: str) -> Dict[str, Any]:
        """Get a specific configuration."""
        try:
            if config_name not in self._config_cache:
                await self._load_all_configs()
            
            return deepcopy(self._config_cache.get(config_name, {}))
        except Exception as e:
            logger.error(f"Error getting configuration {config_name}: {e}", exc_info=True)
            return {}
    
    async def update_config(
        self, 
        config_name: str, 
        config_data: Dict[str, Any],
        validate: bool = True
    ) -> Dict[str, Any]:
        """Update a configuration."""
        try:
            # Validate configuration if requested
            if validate:
                validation_result = await self.validate_config(config_name, config_data)
                if not validation_result["valid"]:
                    raise ValueError(f"Configuration validation failed: {validation_result['errors']}")
            
            # Backup current configuration
            current_config = self._config_cache.get(config_name, {})
            backup_entry = {
                "config_name": config_name,
                "timestamp": datetime.now(),
                "previous_config": deepcopy(current_config),
                "action": "update"
            }
            self._add_to_history(backup_entry)
            
            # Update configuration
            self._config_cache[config_name] = deepcopy(config_data)
            
            # Save to file
            config_path = self._get_config_path(config_name)
            if config_path:
                save_yaml_file(config_path, config_data)
                logger.info(f"Updated configuration: {config_name}")
            
            return {
                "success": True,
                "config_name": config_name,
                "updated_at": datetime.now().isoformat(),
                "message": f"Configuration {config_name} updated successfully"
            }
        except Exception as e:
            logger.error(f"Error updating configuration {config_name}: {e}", exc_info=True)
            raise
    
    async def validate_config(self, config_name: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a configuration."""
        try:
            validation_result = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "config_name": config_name
            }
            
            if config_name == "app_config":
                validation_result = await self._validate_app_config(config_data)
            elif config_name == "sources":
                validation_result = await self._validate_sources_config(config_data)
            elif config_name in ["development", "production"]:
                validation_result = await self._validate_environment_config(config_data)
            else:
                validation_result["warnings"].append(f"No specific validation for config: {config_name}")
            
            logger.info(f"Validated configuration: {config_name} - Valid: {validation_result['valid']}")
            return validation_result
        except Exception as e:
            logger.error(f"Error validating configuration {config_name}: {e}", exc_info=True)
            return {
                "valid": False,
                "errors": [str(e)],
                "warnings": [],
                "config_name": config_name
            }
    
    async def _validate_app_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate application configuration."""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "config_name": "app_config"
        }
        
        # Required sections
        required_sections = ["app", "api", "logging", "milvus", "redis"]
        for section in required_sections:
            if section not in config_data:
                validation_result["errors"].append(f"Missing required section: {section}")
                validation_result["valid"] = False
        
        # Validate API configuration
        if "api" in config_data:
            api_config = config_data["api"]
            if "port" in api_config:
                port = api_config["port"]
                if not isinstance(port, int) or port < 1 or port > 65535:
                    validation_result["errors"].append("API port must be an integer between 1 and 65535")
                    validation_result["valid"] = False
        
        # Validate Milvus configuration
        if "milvus" in config_data:
            milvus_config = config_data["milvus"]
            required_milvus_fields = ["host", "port", "collection_name"]
            for field in required_milvus_fields:
                if field not in milvus_config:
                    validation_result["errors"].append(f"Missing required Milvus field: {field}")
                    validation_result["valid"] = False
        
        # Validate Redis configuration
        if "redis" in config_data:
            redis_config = config_data["redis"]
            required_redis_fields = ["host", "port", "db"]
            for field in required_redis_fields:
                if field not in redis_config:
                    validation_result["errors"].append(f"Missing required Redis field: {field}")
                    validation_result["valid"] = False
        
        return validation_result
    
    async def _validate_sources_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate sources configuration."""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "config_name": "sources"
        }
        
        if "sources" not in config_data:
            validation_result["errors"].append("Missing 'sources' section")
            validation_result["valid"] = False
            return validation_result
        
        sources = config_data["sources"]
        if not isinstance(sources, list):
            validation_result["errors"].append("Sources must be a list")
            validation_result["valid"] = False
            return validation_result
        
        for i, source in enumerate(sources):
            if not isinstance(source, dict):
                validation_result["errors"].append(f"Source {i} must be a dictionary")
                validation_result["valid"] = False
                continue
            
            # Required fields
            required_fields = ["name", "type"]
            for field in required_fields:
                if field not in source:
                    validation_result["errors"].append(f"Source {i} missing required field: {field}")
                    validation_result["valid"] = False
            
            # Validate source type
            if "type" in source:
                valid_types = ["website", "pdf", "text"]
                if source["type"] not in valid_types:
                    validation_result["errors"].append(f"Source {i} has invalid type. Must be one of: {valid_types}")
                    validation_result["valid"] = False
            
            # Validate URL or path
            if source.get("type") == "website" and "url" not in source:
                validation_result["errors"].append(f"Website source {i} must have a URL")
                validation_result["valid"] = False
            elif source.get("type") in ["pdf", "text"] and "path" not in source:
                validation_result["errors"].append(f"File source {i} must have a path")
                validation_result["valid"] = False
        
        return validation_result
    
    async def _validate_environment_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate environment-specific configuration."""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "config_name": "environment"
        }
        
        # Basic validation for environment configs
        if "debug" in config_data and not isinstance(config_data["debug"], bool):
            validation_result["errors"].append("Debug setting must be a boolean")
            validation_result["valid"] = False
        
        if "log_level" in config_data:
            valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
            if config_data["log_level"] not in valid_levels:
                validation_result["errors"].append(f"Log level must be one of: {valid_levels}")
                validation_result["valid"] = False
        
        return validation_result
    
    async def backup_config(self, config_name: str) -> Dict[str, Any]:
        """Create a backup of a configuration."""
        try:
            current_config = await self.get_config(config_name)
            
            backup_entry = {
                "config_name": config_name,
                "timestamp": datetime.now(),
                "config_data": current_config,
                "action": "backup"
            }
            
            self._add_to_history(backup_entry)
            
            return {
                "success": True,
                "config_name": config_name,
                "backup_timestamp": backup_entry["timestamp"].isoformat(),
                "message": f"Configuration {config_name} backed up successfully"
            }
        except Exception as e:
            logger.error(f"Error backing up configuration {config_name}: {e}", exc_info=True)
            raise
    
    async def restore_config(self, config_name: str, backup_timestamp: str) -> Dict[str, Any]:
        """Restore a configuration from backup."""
        try:
            # Find the backup
            target_timestamp = datetime.fromisoformat(backup_timestamp)
            backup_entry = None
            
            for entry in self._config_history:
                if (entry["config_name"] == config_name and 
                    entry["timestamp"] == target_timestamp):
                    backup_entry = entry
                    break
            
            if not backup_entry:
                raise ValueError(f"Backup not found for {config_name} at {backup_timestamp}")
            
            # Restore configuration
            if "config_data" in backup_entry:
                config_data = backup_entry["config_data"]
            elif "previous_config" in backup_entry:
                config_data = backup_entry["previous_config"]
            else:
                raise ValueError("Invalid backup entry")
            
            await self.update_config(config_name, config_data, validate=True)
            
            return {
                "success": True,
                "config_name": config_name,
                "restored_from": backup_timestamp,
                "message": f"Configuration {config_name} restored successfully"
            }
        except Exception as e:
            logger.error(f"Error restoring configuration {config_name}: {e}", exc_info=True)
            raise
    
    async def get_config_history(self, config_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get configuration change history."""
        try:
            if config_name:
                history = [
                    entry for entry in self._config_history
                    if entry["config_name"] == config_name
                ]
            else:
                history = self._config_history.copy()
            
            # Sort by timestamp (most recent first)
            history.sort(key=lambda x: x["timestamp"], reverse=True)
            
            # Convert timestamps to ISO strings
            for entry in history:
                entry["timestamp"] = entry["timestamp"].isoformat()
            
            return history
        except Exception as e:
            logger.error(f"Error getting configuration history: {e}", exc_info=True)
            return []
    
    async def get_all_configs(self) -> Dict[str, Any]:
        """Get all configurations."""
        try:
            return {
                "app_config": await self.get_config("app_config"),
                "sources": await self.get_config("sources"),
                "development": await self.get_config("development"),
                "production": await self.get_config("production")
            }
        except Exception as e:
            logger.error(f"Error getting all configurations: {e}", exc_info=True)
            return {}
    
    def _get_config_path(self, config_name: str) -> Optional[Path]:
        """Get the file path for a configuration."""
        config_paths = {
            "app_config": self.app_config_path,
            "sources": self.sources_config_path,
            "development": self.development_config_path,
            "production": self.production_config_path
        }
        return config_paths.get(config_name)
    
    def _add_to_history(self, entry: Dict[str, Any]):
        """Add an entry to configuration history."""
        self._config_history.append(entry)
        
        # Limit history size
        if len(self._config_history) > self._max_history_size:
            self._config_history.pop(0)
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            # Save any pending changes
            logger.info("Configuration manager cleanup completed")
        except Exception as e:
            logger.error(f"Error during configuration manager cleanup: {e}", exc_info=True)
