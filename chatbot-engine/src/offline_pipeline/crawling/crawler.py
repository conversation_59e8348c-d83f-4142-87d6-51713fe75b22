"""Web crawler implementation for legal documents."""

import asyncio
import aiohttp
import aiofiles
from urllib.parse import urljoin, urlparse, parse_qs
from urllib.robotparser import RobotFileParser
import re
import time
from typing import List, Dict, Any, Optional, Set
from pathlib import Path
from bs4 import BeautifulSoup

from shared.models import DataSource
from shared.config import get_settings
from shared.logging_config import get_logger
from shared.utils import ensure_directory_exists, sanitize_filename

logger = get_logger(__name__)


class WebCrawler:
    """Web crawler for legal document websites."""

    def __init__(self):
        self.settings = get_settings()
        self.output_dir = Path(self.settings.crawled_data_dir)
        ensure_directory_exists(str(self.output_dir))

        # Crawling configuration
        self.delay_between_requests = 1.0
        self.max_concurrent_requests = 5
        self.timeout = 30
        self.user_agent = "RAG-Legal-Chatbot/1.0"
        self.respect_robots_txt = True

        # Session management
        self.session: Optional[aiohttp.ClientSession] = None
        self.visited_urls: Set[str] = set()
        self.robots_cache: Dict[str, RobotFileParser] = {}

    async def __aenter__(self):
        """Async context manager entry."""
        await self._create_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._close_session()

    async def _create_session(self):
        """Create aiohttp session with proper configuration."""
        connector = aiohttp.TCPConnector(limit=self.max_concurrent_requests)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        headers = {"User-Agent": self.user_agent}

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        )

    async def _close_session(self):
        """Close aiohttp session."""
        if self.session:
            await self.session.close()

    async def crawl_website(self, source: DataSource) -> List[str]:
        """
        Crawl a website and return list of downloaded file paths.

        Args:
            source: DataSource configuration for the website

        Returns:
            List of file paths to downloaded content
        """
        if not source.url:
            raise ValueError("Website source must have a URL")

        logger.info(f"Starting crawl of {source.url}")

        try:
            if not self.session:
                await self._create_session()

            # Check robots.txt if enabled
            if self.respect_robots_txt and not await self._can_fetch(source.url):
                logger.warning(f"Robots.txt disallows crawling {source.url}")
                return []

            # Perform the actual crawl
            return await self._crawl_recursive(source)

        except Exception as e:
            logger.error(f"Error crawling {source.url}: {e}")
            raise
    
    async def _can_fetch(self, url: str) -> bool:
        """Check if URL can be fetched according to robots.txt."""
        try:
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            if base_url not in self.robots_cache:
                robots_url = urljoin(base_url, "/robots.txt")

                try:
                    async with self.session.get(robots_url) as response:
                        if response.status == 200:
                            robots_content = await response.text()
                            rp = RobotFileParser()
                            rp.set_url(robots_url)
                            rp.read_lines(robots_content.splitlines())
                            self.robots_cache[base_url] = rp
                        else:
                            # If robots.txt doesn't exist, allow crawling
                            self.robots_cache[base_url] = None
                except Exception:
                    # If we can't fetch robots.txt, allow crawling
                    self.robots_cache[base_url] = None

            robots_parser = self.robots_cache[base_url]
            if robots_parser:
                return robots_parser.can_fetch(self.user_agent, url)
            return True

        except Exception as e:
            logger.warning(f"Error checking robots.txt for {url}: {e}")
            return True

    async def _crawl_recursive(self, source: DataSource) -> List[str]:
        """Perform recursive crawling of the website."""
        downloaded_files = []
        urls_to_visit = [source.url]
        current_depth = 0
        max_depth = source.crawl_depth or 2

        site_dir = self.output_dir / sanitize_filename(source.name)
        ensure_directory_exists(str(site_dir))

        while urls_to_visit and current_depth <= max_depth:
            current_level_urls = urls_to_visit.copy()
            urls_to_visit = []

            logger.info(f"Crawling depth {current_depth}, {len(current_level_urls)} URLs")

            # Process URLs in batches to respect rate limits
            for i in range(0, len(current_level_urls), self.max_concurrent_requests):
                batch = current_level_urls[i:i + self.max_concurrent_requests]

                # Process batch concurrently
                tasks = [self._fetch_and_save_page(url, site_dir, current_depth) for url in batch]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                for url, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        logger.error(f"Error processing {url}: {result}")
                        continue

                    if result:
                        file_path, links = result
                        downloaded_files.append(file_path)

                        # Add new links for next depth level
                        if current_depth < max_depth:
                            for link in links:
                                if link not in self.visited_urls and self._is_valid_link(link, source.url):
                                    urls_to_visit.append(link)

                # Delay between batches
                if i + self.max_concurrent_requests < len(current_level_urls):
                    await asyncio.sleep(self.delay_between_requests)

            current_depth += 1

        logger.info(f"Crawl completed: {len(downloaded_files)} files downloaded")
        return downloaded_files

    async def _fetch_and_save_page(self, url: str, site_dir: Path, depth: int) -> Optional[tuple]:
        """Fetch a page and save it to disk, returning file path and extracted links."""
        if url in self.visited_urls:
            return None

        self.visited_urls.add(url)

        try:
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.warning(f"HTTP {response.status} for {url}")
                    return None

                content_type = response.headers.get('content-type', '').lower()
                if 'text/html' not in content_type:
                    logger.info(f"Skipping non-HTML content: {url}")
                    return None

                content = await response.text()

                # Generate filename
                filename = self._generate_filename(url, depth)
                file_path = site_dir / filename

                # Save content
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    await f.write(content)

                # Extract links
                links = self._extract_links(content, url)

                logger.info(f"Saved {url} -> {file_path}")
                return str(file_path), links

        except Exception as e:
            logger.error(f"Error fetching {url}: {e}")
            return None

    def _generate_filename(self, url: str, depth: int) -> str:
        """Generate a safe filename for the URL."""
        parsed = urlparse(url)

        # Use path or default to index
        path = parsed.path.strip('/')
        if not path or path.endswith('/'):
            path = 'index'

        # Clean up path
        path = re.sub(r'[^\w\-_.]', '_', path)
        path = re.sub(r'_+', '_', path)

        # Add depth prefix and ensure .html extension
        filename = f"depth_{depth}_{path}"
        if not filename.endswith('.html'):
            filename += '.html'

        return filename

    def _extract_links(self, html_content: str, base_url: str) -> List[str]:
        """Extract links from HTML content."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []

            for link in soup.find_all('a', href=True):
                href = link['href']
                absolute_url = urljoin(base_url, href)
                links.append(absolute_url)

            return links

        except Exception as e:
            logger.error(f"Error extracting links from {base_url}: {e}")
            return []

    def _is_valid_link(self, url: str, base_url: str) -> bool:
        """Check if a link is valid for crawling."""
        try:
            parsed_url = urlparse(url)
            parsed_base = urlparse(base_url)

            # Only crawl same domain
            if parsed_url.netloc != parsed_base.netloc:
                return False

            # Skip certain file types
            skip_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.exe'}
            if any(url.lower().endswith(ext) for ext in skip_extensions):
                return False

            # Skip fragments and query parameters that indicate non-content pages
            if '#' in url or any(param in url.lower() for param in ['print=', 'download=', 'export=']):
                return False

            return True

        except Exception:
            return False

    async def crawl_multiple_sources(self, sources: List[DataSource]) -> Dict[str, List[str]]:
        """
        Crawl multiple website sources.

        Args:
            sources: List of DataSource configurations

        Returns:
            Dictionary mapping source IDs to lists of downloaded file paths
        """
        results = {}

        # Ensure session is created
        if not self.session:
            await self._create_session()

        try:
            for source in sources:
                if source.source_type == "website" and source.enabled:
                    try:
                        logger.info(f"Starting crawl for source: {source.name}")
                        file_paths = await self.crawl_website(source)
                        results[source.id or source.name] = file_paths

                        # Clear visited URLs between sources
                        self.visited_urls.clear()

                    except Exception as e:
                        logger.error(f"Failed to crawl {source.name}: {e}")
                        results[source.id or source.name] = []
        finally:
            # Clean up session if we created it
            await self._close_session()

        return results


class DocumentDownloader:
    """Download and manage document files."""
    
    def __init__(self):
        self.settings = get_settings()
        self.output_dir = Path(self.settings.crawled_data_dir)
        ensure_directory_exists(str(self.output_dir))
    
    async def download_pdf(self, url: str, filename: str) -> str:
        """
        Download a PDF file from URL.
        
        Args:
            url: URL of the PDF file
            filename: Local filename to save as
            
        Returns:
            Path to downloaded file
        """
        import aiohttp
        
        file_path = self.output_dir / filename
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    response.raise_for_status()
                    
                    with open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
            
            logger.info(f"Downloaded PDF: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Error downloading PDF from {url}: {e}")
            raise
    
    def copy_local_file(self, source_path: str, target_name: str) -> str:
        """
        Copy a local file to the crawled data directory.
        
        Args:
            source_path: Path to source file
            target_name: Target filename
            
        Returns:
            Path to copied file
        """
        import shutil
        
        source = Path(source_path)
        if not source.exists():
            raise FileNotFoundError(f"Source file not found: {source_path}")
        
        target_path = self.output_dir / target_name
        shutil.copy2(source, target_path)
        
        logger.info(f"Copied file: {source_path} -> {target_path}")
        return str(target_path)
