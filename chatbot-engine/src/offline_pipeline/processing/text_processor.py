"""Text processing and chunking for legal documents."""

import re
from typing import List, Dict, Any, Optional
from pathlib import Path

from langchain.text_splitter import RecursiveCharacterTextSplitter
from bs4 import BeautifulSoup
import pypdf

from shared.models import DocumentChunk
from shared.config import get_settings
from shared.logging_config import get_logger
from shared.utils import generate_id, generate_hash

logger = get_logger(__name__)


class TextExtractor:
    """Extract text from various document formats."""
    
    def __init__(self):
        self.settings = get_settings()
    
    def extract_from_html(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from HTML file.
        
        Args:
            file_path: Path to HTML file
            
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract title
            title = soup.title.string if soup.title else Path(file_path).stem
            
            # Extract main text
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return {
                "text": text,
                "title": title,
                "source_type": "html",
                "file_path": file_path,
                "metadata": {
                    "title": title,
                    "file_size": len(content),
                    "encoding": "utf-8"
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from HTML {file_path}: {e}")
            raise
    
    def extract_from_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF file.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            text_parts = []
            metadata = {}
            
            with open(file_path, 'rb') as f:
                pdf_reader = pypdf.PdfReader(f)
                
                # Extract metadata
                if pdf_reader.metadata:
                    metadata.update({
                        "title": pdf_reader.metadata.get("/Title", ""),
                        "author": pdf_reader.metadata.get("/Author", ""),
                        "subject": pdf_reader.metadata.get("/Subject", ""),
                        "creator": pdf_reader.metadata.get("/Creator", ""),
                    })
                
                metadata["num_pages"] = len(pdf_reader.pages)
                
                # Extract text from all pages
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_parts.append(page_text)
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num} of {file_path}: {e}")
            
            full_text = "\n\n".join(text_parts)
            title = metadata.get("title") or Path(file_path).stem
            
            return {
                "text": full_text,
                "title": title,
                "source_type": "pdf",
                "file_path": file_path,
                "metadata": metadata
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}")
            raise
    
    def extract_from_text(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from plain text file.
        
        Args:
            file_path: Path to text file
            
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            title = Path(file_path).stem
            
            return {
                "text": text,
                "title": title,
                "source_type": "text",
                "file_path": file_path,
                "metadata": {
                    "title": title,
                    "file_size": len(text),
                    "encoding": "utf-8"
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from file {file_path}: {e}")
            raise


class TextChunker:
    """Chunk text into smaller pieces for vector storage."""
    
    def __init__(self):
        self.settings = get_settings()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.settings.chunk_size,
            chunk_overlap=self.settings.chunk_overlap,
            separators=["\n\n", "\n", "§", ".", ",", " "],
            length_function=len,
        )
    
    def chunk_document(self, extracted_doc: Dict[str, Any], source_id: str) -> List[DocumentChunk]:
        """
        Chunk a document into smaller pieces.
        
        Args:
            extracted_doc: Document data from TextExtractor
            source_id: ID of the data source
            
        Returns:
            List of DocumentChunk objects
        """
        try:
            text = extracted_doc["text"]
            if not text.strip():
                logger.warning(f"Empty text in document: {extracted_doc.get('file_path')}")
                return []
            
            # Split text into chunks
            text_chunks = self.text_splitter.split_text(text)
            
            chunks = []
            for i, chunk_text in enumerate(text_chunks):
                if len(chunk_text.strip()) < 50:  # Skip very short chunks
                    continue
                
                chunk = DocumentChunk(
                    id=generate_id(),
                    content=chunk_text.strip(),
                    source_id=source_id,
                    source_url=extracted_doc.get("file_path"),
                    metadata={
                        "title": extracted_doc.get("title", ""),
                        "source_type": extracted_doc.get("source_type", ""),
                        "chunk_index": i,
                        "total_chunks": len(text_chunks),
                        "file_path": extracted_doc.get("file_path", ""),
                        **extracted_doc.get("metadata", {})
                    }
                )
                chunks.append(chunk)
            
            logger.info(f"Created {len(chunks)} chunks from document: {extracted_doc.get('title')}")
            return chunks
            
        except Exception as e:
            logger.error(f"Error chunking document: {e}")
            raise


class LegalTextProcessor:
    """Specialized processor for legal documents."""
    
    def __init__(self):
        self.extractor = TextExtractor()
        self.chunker = TextChunker()
    
    def process_file(self, file_path: str, source_id: str) -> List[DocumentChunk]:
        """
        Process a single file and return document chunks.
        
        Args:
            file_path: Path to the file to process
            source_id: ID of the data source
            
        Returns:
            List of DocumentChunk objects
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Determine file type and extract text
        suffix = file_path.suffix.lower()
        
        if suffix == '.html' or suffix == '.htm':
            extracted_doc = self.extractor.extract_from_html(str(file_path))
        elif suffix == '.pdf':
            extracted_doc = self.extractor.extract_from_pdf(str(file_path))
        elif suffix == '.txt':
            extracted_doc = self.extractor.extract_from_text(str(file_path))
        else:
            raise ValueError(f"Unsupported file type: {suffix}")
        
        # Chunk the document
        chunks = self.chunker.chunk_document(extracted_doc, source_id)
        
        return chunks
    
    def process_multiple_files(self, file_paths: List[str], source_id: str) -> List[DocumentChunk]:
        """
        Process multiple files and return all document chunks.
        
        Args:
            file_paths: List of file paths to process
            source_id: ID of the data source
            
        Returns:
            List of all DocumentChunk objects
        """
        all_chunks = []
        
        for file_path in file_paths:
            try:
                chunks = self.process_file(file_path, source_id)
                all_chunks.extend(chunks)
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
        
        logger.info(f"Processed {len(file_paths)} files, created {len(all_chunks)} chunks")
        return all_chunks
