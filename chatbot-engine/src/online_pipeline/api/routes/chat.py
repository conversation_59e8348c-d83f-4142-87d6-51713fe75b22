"""Chat endpoints for the RAG Legal Chatbot."""

import asyncio
import time
import uuid
from typing import As<PERSON><PERSON>enerator, Optional

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON>PBearer

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.models import QueryRequest, QueryResponse
from shared.logging_config import get_logger
from online_pipeline.rag_pipeline import RAGPipeline

logger = get_logger(__name__)
router = APIRouter()
security = HTTPBearer(auto_error=False)

# Global RAG pipeline instance
_rag_pipeline: Optional[RAGPipeline] = None


async def get_rag_pipeline() -> RAGPipeline:
    """Get the RAG pipeline instance."""
    global _rag_pipeline

    if _rag_pipeline is None:
        _rag_pipeline = RAGPipeline()
        await _rag_pipeline.initialize()

    return _rag_pipeline


@router.post("/sessions")
async def create_session(metadata: Optional[dict] = None):
    """Create a new chat session."""
    try:
        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        # Create new session
        session_id = await rag_pipeline.create_session(metadata)

        return {
            "session_id": session_id,
            "created_at": time.time(),
            "metadata": metadata or {}
        }
    except Exception as e:
        logger.error(f"Error creating session: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error creating session")


@router.post("/regenerate", response_model=QueryResponse)
async def regenerate_response(request: QueryRequest, feedback: Optional[str] = None):
    """Regenerate a response with optional feedback."""
    try:
        logger.info(f"Regenerating response for query: {request.query[:100]}...")

        # Ensure session ID is set
        if not request.session_id:
            request.session_id = str(uuid.uuid4())

        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        # Regenerate response
        response = await rag_pipeline.regenerate_response(request, feedback)

        logger.info(f"Response regenerated successfully in {response.processing_time:.3f}s")
        return response

    except Exception as e:
        logger.error(f"Error regenerating response: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error regenerating response")


@router.post("/query", response_model=QueryResponse)
async def query_chat(request: QueryRequest):
    """Process a chat query and return a complete response."""
    try:
        logger.info(f"Processing query: {request.query[:100]}...")

        # Ensure session ID is set
        if not request.session_id:
            request.session_id = str(uuid.uuid4())

        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        # Process query through RAG pipeline
        response = await rag_pipeline.process_query(request)

        logger.info(f"Query processed successfully in {response.processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error processing query")


@router.post("/stream")
async def stream_chat(request: QueryRequest):
    """Process a chat query and return a streaming response."""
    try:
        logger.info(f"Processing streaming query: {request.query[:100]}...")

        # Ensure session ID is set
        if not request.session_id:
            request.session_id = str(uuid.uuid4())

        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        async def generate_response():
            try:
                async for chunk in rag_pipeline.process_streaming_query(request):
                    yield f"data: {chunk}\n\n"
                yield "data: [DONE]\n\n"
            except Exception as e:
                logger.error(f"Error in streaming generation: {e}", exc_info=True)
                yield f"data: [ERROR: {str(e)}]\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # Disable nginx buffering
            }
        )

    except Exception as e:
        logger.error(f"Error processing streaming query: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error processing streaming query")


@router.get("/sessions/{session_id}/history")
async def get_chat_history(session_id: str, limit: Optional[int] = None):
    """Get chat history for a session."""
    try:
        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        # Get conversation history
        history = await rag_pipeline.get_conversation_history(session_id, limit)

        return {
            "session_id": session_id,
            "messages": history
        }
    except Exception as e:
        logger.error(f"Error retrieving chat history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error retrieving chat history")


@router.delete("/sessions/{session_id}")
async def clear_chat_session(session_id: str):
    """Clear a chat session."""
    try:
        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        # Delete session
        success = await rag_pipeline.session_manager.delete_session(session_id)

        if success:
            logger.info(f"Session {session_id} cleared successfully")
            return {"message": f"Session {session_id} cleared successfully"}
        else:
            raise HTTPException(status_code=404, detail="Session not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing session: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error clearing session")


@router.get("/health")
async def health_check():
    """Health check for the chat service."""
    try:
        # Get RAG pipeline
        rag_pipeline = await get_rag_pipeline()

        # Check pipeline health
        health_status = await rag_pipeline.health_check()

        return {
            "status": "healthy" if health_status.get("overall", False) else "unhealthy",
            "components": health_status.get("components", {}),
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}", exc_info=True)
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }
