"""
Result fusion for combining vector and keyword search results.

This module implements various fusion strategies to combine results from
different retrieval methods (vector similarity and keyword matching) to
provide the best overall retrieval performance.
"""

import math
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum

from shared.logging_config import get_logger
from shared.models import DocumentChunk
from .vector_retriever import RetrievalResult
from .keyword_retriever import KeywordRetrievalResult

logger = get_logger(__name__)


class FusionStrategy(Enum):
    """Available fusion strategies."""
    RRF = "reciprocal_rank_fusion"  # Reciprocal Rank Fusion
    WEIGHTED_SUM = "weighted_sum"   # Weighted sum of normalized scores
    BORDA_COUNT = "borda_count"     # Borda count voting
    COMBSUM = "combsum"             # Simple sum combination
    COMBMNZ = "combmnz"             # CombMNZ (multiply by number of methods)


class FusedResult:
    """Fused result from multiple retrieval methods."""
    
    def __init__(self, chunk: DocumentChunk, final_score: float, rank: int):
        self.chunk = chunk
        self.final_score = final_score
        self.rank = rank
        self.component_scores = {}
        self.component_ranks = {}
        self.matched_terms = []
        self.retrieval_methods = []
        
    def add_component_result(self, method: str, score: float, rank: int, matched_terms: List[str] = None):
        """Add a component result from a specific retrieval method."""
        self.component_scores[method] = score
        self.component_ranks[method] = rank
        self.retrieval_methods.append(method)
        
        if matched_terms:
            self.matched_terms.extend(matched_terms)
            # Remove duplicates while preserving order
            seen = set()
            self.matched_terms = [x for x in self.matched_terms if not (x in seen or seen.add(x))]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "content": self.chunk.content,
            "source_id": self.chunk.source_id,
            "source_url": self.chunk.source_url,
            "metadata": self.chunk.metadata,
            "final_score": self.final_score,
            "rank": self.rank,
            "component_scores": self.component_scores,
            "component_ranks": self.component_ranks,
            "matched_terms": self.matched_terms,
            "retrieval_methods": self.retrieval_methods
        }


class ResultFusion:
    """
    Combines results from multiple retrieval methods using various fusion strategies.
    
    This class implements several fusion algorithms to optimally combine
    results from vector similarity search and keyword-based search.
    """
    
    def __init__(self, strategy: FusionStrategy = FusionStrategy.RRF):
        """
        Initialize the result fusion component.
        
        Args:
            strategy: The fusion strategy to use
        """
        self.strategy = strategy
        self.logger = get_logger(__name__)
        
    def fuse_results(self,
                    vector_results: List[RetrievalResult],
                    keyword_results: List[KeywordRetrievalResult],
                    top_k: int = 10,
                    vector_weight: float = 0.6,
                    keyword_weight: float = 0.4) -> List[FusedResult]:
        """
        Fuse results from vector and keyword retrieval.
        
        Args:
            vector_results: Results from vector similarity search
            keyword_results: Results from keyword search
            top_k: Number of top results to return
            vector_weight: Weight for vector search results (0.0 to 1.0)
            keyword_weight: Weight for keyword search results (0.0 to 1.0)
            
        Returns:
            List of fused results sorted by final score
        """
        try:
            start_time = datetime.utcnow()
            
            # Normalize weights
            total_weight = vector_weight + keyword_weight
            if total_weight > 0:
                vector_weight = vector_weight / total_weight
                keyword_weight = keyword_weight / total_weight
            else:
                vector_weight = 0.5
                keyword_weight = 0.5
            
            # Apply fusion strategy
            if self.strategy == FusionStrategy.RRF:
                fused_results = self._reciprocal_rank_fusion(
                    vector_results, keyword_results, top_k
                )
            elif self.strategy == FusionStrategy.WEIGHTED_SUM:
                fused_results = self._weighted_sum_fusion(
                    vector_results, keyword_results, top_k, vector_weight, keyword_weight
                )
            elif self.strategy == FusionStrategy.BORDA_COUNT:
                fused_results = self._borda_count_fusion(
                    vector_results, keyword_results, top_k
                )
            elif self.strategy == FusionStrategy.COMBSUM:
                fused_results = self._combsum_fusion(
                    vector_results, keyword_results, top_k
                )
            elif self.strategy == FusionStrategy.COMBMNZ:
                fused_results = self._combmnz_fusion(
                    vector_results, keyword_results, top_k
                )
            else:
                raise ValueError(f"Unknown fusion strategy: {self.strategy}")
            
            fusion_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"Result fusion completed: {len(fused_results)} results in {fusion_time:.3f}s using {self.strategy.value}")
            
            return fused_results
            
        except Exception as e:
            logger.error(f"Error in result fusion: {e}", exc_info=True)
            raise
    
    def _reciprocal_rank_fusion(self,
                               vector_results: List[RetrievalResult],
                               keyword_results: List[KeywordRetrievalResult],
                               top_k: int,
                               k: int = 60) -> List[FusedResult]:
        """
        Reciprocal Rank Fusion (RRF) algorithm.
        
        RRF score = sum(1 / (k + rank)) for each method where document appears
        """
        # Collect all unique chunks
        chunk_scores = {}
        
        # Process vector results
        for result in vector_results:
            chunk_id = result.chunk.id
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = {
                    'chunk': result.chunk,
                    'rrf_score': 0,
                    'methods': {}
                }
            
            rrf_contribution = 1.0 / (k + result.rank)
            chunk_scores[chunk_id]['rrf_score'] += rrf_contribution
            chunk_scores[chunk_id]['methods']['vector'] = {
                'score': result.score,
                'rank': result.rank,
                'matched_terms': []
            }
        
        # Process keyword results
        for result in keyword_results:
            chunk_id = result.chunk.id
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = {
                    'chunk': result.chunk,
                    'rrf_score': 0,
                    'methods': {}
                }
            
            rrf_contribution = 1.0 / (k + result.rank)
            chunk_scores[chunk_id]['rrf_score'] += rrf_contribution
            chunk_scores[chunk_id]['methods']['keyword'] = {
                'score': result.score,
                'rank': result.rank,
                'matched_terms': result.matched_terms
            }
        
        # Sort by RRF score and create fused results
        sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1]['rrf_score'], reverse=True)
        
        fused_results = []
        for rank, (chunk_id, data) in enumerate(sorted_chunks[:top_k]):
            fused_result = FusedResult(
                chunk=data['chunk'],
                final_score=data['rrf_score'],
                rank=rank + 1
            )
            
            # Add component results
            for method, method_data in data['methods'].items():
                fused_result.add_component_result(
                    method=method,
                    score=method_data['score'],
                    rank=method_data['rank'],
                    matched_terms=method_data['matched_terms']
                )
            
            fused_results.append(fused_result)
        
        return fused_results
    
    def _weighted_sum_fusion(self,
                            vector_results: List[RetrievalResult],
                            keyword_results: List[KeywordRetrievalResult],
                            top_k: int,
                            vector_weight: float,
                            keyword_weight: float) -> List[FusedResult]:
        """Weighted sum of normalized scores."""
        # Normalize scores
        vector_scores = self._normalize_scores([r.score for r in vector_results])
        keyword_scores = self._normalize_scores([r.score for r in keyword_results])
        
        # Collect all unique chunks
        chunk_scores = {}
        
        # Process vector results
        for i, result in enumerate(vector_results):
            chunk_id = result.chunk.id
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = {
                    'chunk': result.chunk,
                    'weighted_score': 0,
                    'methods': {}
                }
            
            normalized_score = vector_scores[i] if i < len(vector_scores) else 0
            chunk_scores[chunk_id]['weighted_score'] += vector_weight * normalized_score
            chunk_scores[chunk_id]['methods']['vector'] = {
                'score': result.score,
                'rank': result.rank,
                'matched_terms': []
            }
        
        # Process keyword results
        for i, result in enumerate(keyword_results):
            chunk_id = result.chunk.id
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = {
                    'chunk': result.chunk,
                    'weighted_score': 0,
                    'methods': {}
                }
            
            normalized_score = keyword_scores[i] if i < len(keyword_scores) else 0
            chunk_scores[chunk_id]['weighted_score'] += keyword_weight * normalized_score
            chunk_scores[chunk_id]['methods']['keyword'] = {
                'score': result.score,
                'rank': result.rank,
                'matched_terms': result.matched_terms
            }
        
        # Sort and create results
        sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1]['weighted_score'], reverse=True)
        
        fused_results = []
        for rank, (chunk_id, data) in enumerate(sorted_chunks[:top_k]):
            fused_result = FusedResult(
                chunk=data['chunk'],
                final_score=data['weighted_score'],
                rank=rank + 1
            )
            
            for method, method_data in data['methods'].items():
                fused_result.add_component_result(
                    method=method,
                    score=method_data['score'],
                    rank=method_data['rank'],
                    matched_terms=method_data['matched_terms']
                )
            
            fused_results.append(fused_result)
        
        return fused_results
    
    def _borda_count_fusion(self,
                           vector_results: List[RetrievalResult],
                           keyword_results: List[KeywordRetrievalResult],
                           top_k: int) -> List[FusedResult]:
        """Borda count voting fusion."""
        chunk_scores = {}
        max_rank = max(len(vector_results), len(keyword_results))
        
        # Process vector results
        for result in vector_results:
            chunk_id = result.chunk.id
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = {
                    'chunk': result.chunk,
                    'borda_score': 0,
                    'methods': {}
                }
            
            borda_points = max_rank - result.rank + 1
            chunk_scores[chunk_id]['borda_score'] += borda_points
            chunk_scores[chunk_id]['methods']['vector'] = {
                'score': result.score,
                'rank': result.rank,
                'matched_terms': []
            }
        
        # Process keyword results
        for result in keyword_results:
            chunk_id = result.chunk.id
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = {
                    'chunk': result.chunk,
                    'borda_score': 0,
                    'methods': {}
                }
            
            borda_points = max_rank - result.rank + 1
            chunk_scores[chunk_id]['borda_score'] += borda_points
            chunk_scores[chunk_id]['methods']['keyword'] = {
                'score': result.score,
                'rank': result.rank,
                'matched_terms': result.matched_terms
            }
        
        # Sort and create results
        sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1]['borda_score'], reverse=True)
        
        fused_results = []
        for rank, (chunk_id, data) in enumerate(sorted_chunks[:top_k]):
            fused_result = FusedResult(
                chunk=data['chunk'],
                final_score=data['borda_score'],
                rank=rank + 1
            )
            
            for method, method_data in data['methods'].items():
                fused_result.add_component_result(
                    method=method,
                    score=method_data['score'],
                    rank=method_data['rank'],
                    matched_terms=method_data['matched_terms']
                )
            
            fused_results.append(fused_result)
        
        return fused_results
    
    def _combsum_fusion(self,
                       vector_results: List[RetrievalResult],
                       keyword_results: List[KeywordRetrievalResult],
                       top_k: int) -> List[FusedResult]:
        """CombSUM fusion - simple sum of normalized scores."""
        return self._weighted_sum_fusion(vector_results, keyword_results, top_k, 0.5, 0.5)
    
    def _combmnz_fusion(self,
                       vector_results: List[RetrievalResult],
                       keyword_results: List[KeywordRetrievalResult],
                       top_k: int) -> List[FusedResult]:
        """CombMNZ fusion - sum of scores multiplied by number of methods."""
        # First get CombSUM results
        combsum_results = self._combsum_fusion(vector_results, keyword_results, top_k * 2)  # Get more for reranking
        
        # Multiply by number of methods that retrieved each document
        for result in combsum_results:
            num_methods = len(result.retrieval_methods)
            result.final_score *= num_methods
        
        # Re-sort and limit to top_k
        combsum_results.sort(key=lambda x: x.final_score, reverse=True)
        
        # Update ranks
        for i, result in enumerate(combsum_results[:top_k]):
            result.rank = i + 1
        
        return combsum_results[:top_k]
    
    def _normalize_scores(self, scores: List[float]) -> List[float]:
        """Normalize scores to [0, 1] range using min-max normalization."""
        if not scores:
            return []
        
        min_score = min(scores)
        max_score = max(scores)
        
        if max_score == min_score:
            return [1.0] * len(scores)
        
        return [(score - min_score) / (max_score - min_score) for score in scores]

    def set_strategy(self, strategy: FusionStrategy):
        """Change the fusion strategy."""
        self.strategy = strategy
        logger.info(f"Fusion strategy changed to: {strategy.value}")

    def get_fusion_stats(self, fused_results: List[FusedResult]) -> Dict[str, Any]:
        """Get statistics about the fusion results."""
        if not fused_results:
            return {}

        method_counts = {}
        total_methods = 0

        for result in fused_results:
            for method in result.retrieval_methods:
                method_counts[method] = method_counts.get(method, 0) + 1
                total_methods += 1

        return {
            "total_results": len(fused_results),
            "method_distribution": method_counts,
            "average_methods_per_result": total_methods / len(fused_results),
            "fusion_strategy": self.strategy.value,
            "score_range": {
                "min": min(r.final_score for r in fused_results),
                "max": max(r.final_score for r in fused_results),
                "avg": sum(r.final_score for r in fused_results) / len(fused_results)
            }
        }
