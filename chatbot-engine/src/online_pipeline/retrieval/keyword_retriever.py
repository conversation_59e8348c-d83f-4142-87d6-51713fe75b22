"""
Keyword-based retrieval using BM25 for exact term matching.

This module provides keyword-based search capabilities using BM25 algorithm
for finding documents that contain specific terms from the query.
"""

import asyncio
import pickle
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from pathlib import Path

from rank_bm25 import BM25Okapi
import redis.asyncio as redis

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import DocumentChunk, ProcessedQuery
from offline_pipeline.storage.metadata_store import MetadataStore

logger = get_logger(__name__)


class KeywordRetrievalResult:
    """Result from keyword retrieval."""
    
    def __init__(self, chunk: DocumentChunk, score: float, rank: int, matched_terms: List[str]):
        self.chunk = chunk
        self.score = score
        self.rank = rank
        self.matched_terms = matched_terms
        self.retrieval_method = "keyword"
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "content": self.chunk.content,
            "source_id": self.chunk.source_id,
            "source_url": self.chunk.source_url,
            "metadata": self.chunk.metadata,
            "score": self.score,
            "rank": self.rank,
            "matched_terms": self.matched_terms,
            "retrieval_method": self.retrieval_method
        }


class KeywordRetriever:
    """
    Keyword-based retrieval using BM25 algorithm.
    
    This class handles exact term matching and keyword-based search
    using the BM25 ranking function for finding documents that contain
    specific terms from the user query.
    """
    
    def __init__(self):
        """Initialize the keyword retriever."""
        self.settings = get_settings()
        self.bm25_index = None
        self.document_chunks = []
        self.tokenized_corpus = []
        self.redis_client = None
        self.metadata_store = None
        self._initialized = False
        self._index_cache_key = "bm25_index_cache"
        self._corpus_cache_key = "bm25_corpus_cache"
        
    async def initialize(self):
        """Initialize the keyword retriever and load/build BM25 index."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing keyword retriever...")
            
            # Initialize Redis client
            self.redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                decode_responses=False  # We need binary data for pickle
            )
            
            # Initialize metadata store
            self.metadata_store = MetadataStore()
            await self.metadata_store.initialize()
            
            # Try to load cached index
            if await self._load_cached_index():
                logger.info("Loaded BM25 index from cache")
            else:
                # Build new index
                await self._build_index()
                await self._cache_index()
                logger.info("Built and cached new BM25 index")
            
            self._initialized = True
            logger.info("Keyword retriever initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize keyword retriever: {e}", exc_info=True)
            raise
    
    async def _load_cached_index(self) -> bool:
        """Load BM25 index from Redis cache."""
        try:
            # Check if cache exists and is recent
            index_data = await self.redis_client.get(self._index_cache_key)
            corpus_data = await self.redis_client.get(self._corpus_cache_key)
            
            if not index_data or not corpus_data:
                return False
            
            # Deserialize data
            cached_index = pickle.loads(index_data)
            cached_corpus = pickle.loads(corpus_data)
            
            # Validate cache structure
            if not all(key in cached_index for key in ['bm25_params', 'chunks', 'tokenized_corpus']):
                return False
            
            # Restore BM25 index
            self.document_chunks = cached_index['chunks']
            self.tokenized_corpus = cached_index['tokenized_corpus']
            
            # Recreate BM25 index with cached parameters
            self.bm25_index = BM25Okapi(self.tokenized_corpus)
            
            return True
            
        except Exception as e:
            logger.warning(f"Failed to load cached BM25 index: {e}")
            return False
    
    async def _build_index(self):
        """Build BM25 index from document chunks."""
        try:
            logger.info("Building BM25 index from document chunks...")
            
            # Get all document chunks from metadata store
            chunks = await self.metadata_store.get_all_chunks()
            
            if not chunks:
                logger.warning("No document chunks found for BM25 indexing")
                self.document_chunks = []
                self.tokenized_corpus = []
                self.bm25_index = BM25Okapi([])
                return
            
            # Tokenize documents
            self.document_chunks = chunks
            self.tokenized_corpus = []
            
            for chunk in chunks:
                tokens = self._tokenize_text(chunk.content)
                self.tokenized_corpus.append(tokens)
            
            # Build BM25 index
            self.bm25_index = BM25Okapi(self.tokenized_corpus)
            
            logger.info(f"Built BM25 index with {len(chunks)} documents")
            
        except Exception as e:
            logger.error(f"Error building BM25 index: {e}", exc_info=True)
            raise
    
    async def _cache_index(self):
        """Cache BM25 index in Redis."""
        try:
            # Prepare cache data
            cache_data = {
                'bm25_params': {
                    'k1': self.bm25_index.k1,
                    'b': self.bm25_index.b,
                    'epsilon': self.bm25_index.epsilon
                },
                'chunks': self.document_chunks,
                'tokenized_corpus': self.tokenized_corpus
            }
            
            # Serialize and cache
            serialized_data = pickle.dumps(cache_data)
            
            # Set cache with expiration (24 hours)
            await self.redis_client.setex(
                self._index_cache_key, 
                86400,  # 24 hours
                serialized_data
            )
            
            logger.info("BM25 index cached successfully")
            
        except Exception as e:
            logger.warning(f"Failed to cache BM25 index: {e}")
    
    def _tokenize_text(self, text: str) -> List[str]:
        """Tokenize text for BM25 indexing."""
        # Simple tokenization - can be enhanced with proper NLP
        import re
        
        # Convert to lowercase and split on non-alphanumeric characters
        tokens = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out very short tokens
        tokens = [token for token in tokens if len(token) > 2]
        
        return tokens
    
    async def retrieve(self, 
                      processed_query: ProcessedQuery, 
                      top_k: int = 10,
                      score_threshold: float = 0.0) -> List[KeywordRetrievalResult]:
        """
        Retrieve relevant documents using BM25 keyword search.
        
        Args:
            processed_query: The processed query with analysis
            top_k: Number of top results to return
            score_threshold: Minimum BM25 score threshold
            
        Returns:
            List of keyword retrieval results sorted by BM25 score
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            start_time = datetime.utcnow()
            
            # Get search terms
            search_terms = processed_query.search_terms.get("keyword_search", processed_query.sanitized_query)
            query_tokens = self._tokenize_text(search_terms)
            
            if not query_tokens:
                logger.warning("No valid tokens found in query for keyword search")
                return []
            
            # Get BM25 scores
            scores = self.bm25_index.get_scores(query_tokens)
            
            # Get top-k results
            top_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)[:top_k]
            
            # Create results
            results = []
            for rank, idx in enumerate(top_indices):
                score = scores[idx]
                
                if score < score_threshold:
                    continue
                
                chunk = self.document_chunks[idx]
                matched_terms = self._find_matched_terms(chunk.content, query_tokens)
                
                result = KeywordRetrievalResult(
                    chunk=chunk,
                    score=score,
                    rank=rank + 1,
                    matched_terms=matched_terms
                )
                results.append(result)
            
            retrieval_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"Keyword retrieval completed: {len(results)} results in {retrieval_time:.3f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in keyword retrieval: {e}", exc_info=True)
            raise
    
    def _find_matched_terms(self, text: str, query_tokens: List[str]) -> List[str]:
        """Find which query terms are present in the text."""
        text_tokens = set(self._tokenize_text(text))
        matched = [token for token in query_tokens if token in text_tokens]
        return matched
    
    async def search_exact_phrase(self, phrase: str, top_k: int = 10) -> List[KeywordRetrievalResult]:
        """
        Search for exact phrase matches.
        
        Args:
            phrase: Exact phrase to search for
            top_k: Maximum number of results
            
        Returns:
            List of chunks containing the exact phrase
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            phrase_lower = phrase.lower()
            results = []
            
            for i, chunk in enumerate(self.document_chunks):
                if phrase_lower in chunk.content.lower():
                    # Calculate a simple relevance score based on phrase frequency
                    score = chunk.content.lower().count(phrase_lower)
                    
                    result = KeywordRetrievalResult(
                        chunk=chunk,
                        score=score,
                        rank=len(results) + 1,
                        matched_terms=[phrase]
                    )
                    results.append(result)
                    
                    if len(results) >= top_k:
                        break
            
            # Sort by score
            results.sort(key=lambda x: x.score, reverse=True)
            
            # Update ranks
            for i, result in enumerate(results):
                result.rank = i + 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error in exact phrase search: {e}", exc_info=True)
            raise
    
    async def refresh_index(self):
        """Refresh the BM25 index with latest document chunks."""
        try:
            logger.info("Refreshing BM25 index...")
            
            # Clear cache
            await self.redis_client.delete(self._index_cache_key)
            await self.redis_client.delete(self._corpus_cache_key)
            
            # Rebuild index
            await self._build_index()
            await self._cache_index()
            
            logger.info("BM25 index refreshed successfully")
            
        except Exception as e:
            logger.error(f"Error refreshing BM25 index: {e}", exc_info=True)
            raise
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """Get keyword retrieval statistics."""
        if not self._initialized:
            await self.initialize()
            
        return {
            "total_documents": len(self.document_chunks),
            "average_document_length": sum(len(tokens) for tokens in self.tokenized_corpus) / len(self.tokenized_corpus) if self.tokenized_corpus else 0,
            "vocabulary_size": len(set(token for tokens in self.tokenized_corpus for token in tokens)),
            "bm25_parameters": {
                "k1": self.bm25_index.k1 if self.bm25_index else None,
                "b": self.bm25_index.b if self.bm25_index else None,
                "epsilon": self.bm25_index.epsilon if self.bm25_index else None
            }
        }
    
    async def health_check(self) -> bool:
        """Check if the keyword retriever is healthy."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test basic functionality
            if not self.bm25_index or not self.document_chunks:
                return False
            
            # Test a simple query
            test_tokens = ["test"]
            scores = self.bm25_index.get_scores(test_tokens)
            
            return len(scores) == len(self.document_chunks)
            
        except Exception as e:
            logger.error(f"Keyword retriever health check failed: {e}")
            return False
    
    async def close(self):
        """Close connections and cleanup resources."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            if self.metadata_store:
                await self.metadata_store.close()
            
            self._initialized = False
            logger.info("Keyword retriever closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing keyword retriever: {e}", exc_info=True)
