"""
Vector-based retrieval using Milvus for semantic search.

This module provides semantic search capabilities using vector embeddings
stored in Milvus database for finding contextually relevant documents.
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import DocumentChunk, ProcessedQuery
from offline_pipeline.storage.vector_store import MilvusVectorStore
from offline_pipeline.embeddings import create_embedding_processor

logger = get_logger(__name__)


class RetrievalResult:
    """Result from vector retrieval."""
    
    def __init__(self, chunk: DocumentChunk, score: float, rank: int):
        self.chunk = chunk
        self.score = score
        self.rank = rank
        self.retrieval_method = "vector"
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "content": self.chunk.content,
            "source_id": self.chunk.source_id,
            "source_url": self.chunk.source_url,
            "metadata": self.chunk.metadata,
            "score": self.score,
            "rank": self.rank,
            "retrieval_method": self.retrieval_method
        }


class VectorRetriever:
    """
    Vector-based semantic retrieval using Milvus.
    
    This class handles semantic search using vector embeddings to find
    contextually relevant documents based on query meaning rather than
    exact keyword matches.
    """
    
    def __init__(self):
        """Initialize the vector retriever."""
        self.settings = get_settings()
        self.vector_store = None
        self.embedding_processor = None
        self._initialized = False
        
    async def initialize(self):
        """Initialize the vector store and embedding processor."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing vector retriever...")
            
            # Initialize vector store
            self.vector_store = MilvusVectorStore()
            await self.vector_store.initialize()
            
            # Initialize embedding processor
            self.embedding_processor = create_embedding_processor()
            
            self._initialized = True
            logger.info("Vector retriever initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector retriever: {e}", exc_info=True)
            raise
    
    async def retrieve(self, 
                      processed_query: ProcessedQuery, 
                      top_k: int = 10,
                      score_threshold: float = 0.7) -> List[RetrievalResult]:
        """
        Retrieve relevant documents using vector similarity search.
        
        Args:
            processed_query: The processed query with analysis
            top_k: Number of top results to return
            score_threshold: Minimum similarity score threshold
            
        Returns:
            List of retrieval results sorted by relevance score
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            start_time = datetime.utcnow()
            
            # Generate embedding for the query
            query_embedding = await self._generate_query_embedding(processed_query)
            
            # Search in vector store
            search_results = await self.vector_store.search(
                query_vector=query_embedding,
                top_k=top_k,
                score_threshold=score_threshold
            )
            
            # Convert to retrieval results
            results = []
            for i, (chunk, score) in enumerate(search_results):
                result = RetrievalResult(
                    chunk=chunk,
                    score=score,
                    rank=i + 1
                )
                results.append(result)
            
            retrieval_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(f"Vector retrieval completed: {len(results)} results in {retrieval_time:.3f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in vector retrieval: {e}", exc_info=True)
            raise
    
    async def _generate_query_embedding(self, processed_query: ProcessedQuery) -> List[float]:
        """Generate embedding for the processed query."""
        try:
            # Use the main query for embedding
            query_text = processed_query.search_terms.get("vector_search", processed_query.sanitized_query)
            
            # Generate embedding
            embeddings = await self.embedding_processor.generate_embeddings([query_text])
            
            if not embeddings or not embeddings[0]:
                raise ValueError("Failed to generate query embedding")
            
            return embeddings[0]
            
        except Exception as e:
            logger.error(f"Error generating query embedding: {e}", exc_info=True)
            raise
    
    async def retrieve_similar_chunks(self, 
                                    chunk_id: str, 
                                    top_k: int = 5) -> List[RetrievalResult]:
        """
        Retrieve chunks similar to a given chunk.
        
        Args:
            chunk_id: ID of the reference chunk
            top_k: Number of similar chunks to return
            
        Returns:
            List of similar chunks
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            # Get the reference chunk
            reference_chunk = await self.vector_store.get_chunk_by_id(chunk_id)
            if not reference_chunk or not reference_chunk.embedding:
                raise ValueError(f"Chunk {chunk_id} not found or has no embedding")
            
            # Search for similar chunks
            search_results = await self.vector_store.search(
                query_vector=reference_chunk.embedding,
                top_k=top_k + 1,  # +1 to exclude the reference chunk itself
                score_threshold=0.5
            )
            
            # Filter out the reference chunk and convert to results
            results = []
            for i, (chunk, score) in enumerate(search_results):
                if chunk.id != chunk_id:  # Exclude the reference chunk
                    result = RetrievalResult(
                        chunk=chunk,
                        score=score,
                        rank=len(results) + 1
                    )
                    results.append(result)
                    
                    if len(results) >= top_k:
                        break
            
            return results
            
        except Exception as e:
            logger.error(f"Error retrieving similar chunks: {e}", exc_info=True)
            raise
    
    async def get_retrieval_stats(self) -> Dict[str, Any]:
        """Get retrieval statistics."""
        if not self._initialized:
            await self.initialize()
            
        try:
            stats = await self.vector_store.get_collection_stats()
            return {
                "total_vectors": stats.get("total_count", 0),
                "index_type": stats.get("index_type", "unknown"),
                "metric_type": stats.get("metric_type", "unknown"),
                "dimension": stats.get("dimension", 0)
            }
        except Exception as e:
            logger.error(f"Error getting retrieval stats: {e}", exc_info=True)
            return {}
    
    async def health_check(self) -> bool:
        """Check if the vector retriever is healthy."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test basic functionality
            test_embedding = [0.1] * 768  # Test embedding
            await self.vector_store.search(
                query_vector=test_embedding,
                top_k=1,
                score_threshold=0.0
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Vector retriever health check failed: {e}")
            return False
    
    async def close(self):
        """Close connections and cleanup resources."""
        try:
            if self.vector_store:
                await self.vector_store.close()
            
            self._initialized = False
            logger.info("Vector retriever closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing vector retriever: {e}", exc_info=True)
