"""
Complete RAG Pipeline for Online Query Processing.

This module orchestrates the entire online pipeline from query processing
through retrieval, generation, and response delivery with session management.
"""

import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import QueryRequest, QueryResponse, ProcessedQuery

from .query_processor import QueryProcessor
from .retrieval.hybrid_retriever import HybridRetriever, RetrievalMode
from .generation.response_generator import ResponseGenerator
from .session_manager import SessionManager
from .cache_manager import CacheManager

logger = get_logger(__name__)


class RAGPipeline:
    """
    Complete RAG pipeline for processing legal queries.
    
    This class orchestrates the entire online pipeline including:
    - Query processing and analysis
    - Hybrid retrieval (vector + keyword search)
    - Response generation with LLM
    - Session management and conversation history
    - Streaming response delivery
    """
    
    def __init__(self):
        """Initialize the RAG pipeline."""
        self.settings = get_settings()
        
        # Initialize components
        self.query_processor = QueryProcessor()
        self.hybrid_retriever = HybridRetriever()
        self.response_generator = ResponseGenerator()
        self.session_manager = SessionManager()
        self.cache_manager = CacheManager()

        self._initialized = False

        # Configuration
        self.default_top_k = getattr(self.settings, 'default_retrieval_top_k', 10)
        self.enable_session_management = getattr(self.settings, 'enable_session_management', True)
        self.enable_caching = getattr(self.settings, 'enable_caching', True)
        
    async def initialize(self):
        """Initialize all pipeline components."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing RAG pipeline...")
            
            # Initialize all components in parallel
            await asyncio.gather(
                self.hybrid_retriever.initialize(),
                self.response_generator.initialize(),
                self.session_manager.initialize() if self.enable_session_management else asyncio.sleep(0),
                self.cache_manager.initialize() if self.enable_caching else asyncio.sleep(0)
            )
            
            self._initialized = True
            logger.info("RAG pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG pipeline: {e}", exc_info=True)
            raise
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        """
        Process a complete query through the RAG pipeline.
        
        Args:
            request: The query request
            
        Returns:
            Complete query response
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            start_time = datetime.utcnow()
            
            logger.info(f"Processing query through RAG pipeline: {request.query[:100]}...")

            # Step 1: Check cache for processed query
            processed_query = None
            if self.enable_caching:
                processed_query = await self.cache_manager.get_cached_processed_query(request.query)

            # Process query if not cached
            if not processed_query:
                processed_query = await self.query_processor.process_query(request)

                # Cache the processed query
                if self.enable_caching:
                    await self.cache_manager.cache_processed_query(request.query, processed_query)
            
            # Step 2: Get conversation context if session management is enabled
            conversation_history = []
            if self.enable_session_management and request.session_id:
                conversation_history = await self.session_manager.get_context_for_query(
                    request.session_id
                )
            
            # Step 3: Check cache for retrieval results
            query_cache_key = f"{processed_query.sanitized_query}_{request.max_results or self.default_top_k}"
            retrieval_results = None

            if self.enable_caching:
                cached_results = await self.cache_manager.get_cached_retrieval_results(query_cache_key)
                if cached_results:
                    # Convert cached results back to FusedResult objects (simplified)
                    logger.info("Using cached retrieval results")

            # Retrieve documents if not cached
            if not retrieval_results:
                retrieval_results = await self.hybrid_retriever.retrieve(
                    processed_query=processed_query,
                    top_k=request.max_results or self.default_top_k,
                    mode=self._determine_retrieval_mode(processed_query)
                )

                # Cache the retrieval results
                if self.enable_caching:
                    await self.cache_manager.cache_retrieval_results(query_cache_key, retrieval_results)
            
            # Step 4: Check cache for response
            response_cache_key = f"{processed_query.sanitized_query}_{len(retrieval_results)}"
            response = None

            if self.enable_caching:
                response = await self.cache_manager.get_cached_response(response_cache_key, request.session_id)
                if response:
                    logger.info("Using cached response")

            # Generate response if not cached
            if not response:
                response = await self.response_generator.generate_response(
                    processed_query=processed_query,
                    retrieved_results=retrieval_results,
                    conversation_history=conversation_history,
                    session_id=request.session_id
                )

                # Cache the response
                if self.enable_caching:
                    await self.cache_manager.cache_response(response_cache_key, response)
            
            # Step 5: Update session if session management is enabled
            if self.enable_session_management and request.session_id:
                await self.session_manager.add_conversation_turn(
                    session_id=request.session_id,
                    processed_query=processed_query,
                    response=response
                )
            
            # Step 6: Add pipeline metadata
            total_time = (datetime.utcnow() - start_time).total_seconds()
            response.metadata.update({
                "pipeline_processing_time": total_time,
                "query_processing_time": processed_query.processing_time,
                "retrieval_count": len(retrieval_results),
                "session_enabled": self.enable_session_management,
                "conversation_context_turns": len(conversation_history)
            })
            
            logger.info(f"RAG pipeline completed in {total_time:.3f}s")
            return response
            
        except Exception as e:
            logger.error(f"Error in RAG pipeline: {e}", exc_info=True)
            raise
    
    async def process_streaming_query(self, request: QueryRequest) -> AsyncGenerator[str, None]:
        """
        Process a query with streaming response delivery.
        
        Args:
            request: The query request
            
        Yields:
            Response chunks as they are generated
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            logger.info(f"Processing streaming query: {request.query[:100]}...")
            
            # Step 1: Process and analyze the query
            processed_query = await self.query_processor.process_query(request)
            
            # Step 2: Get conversation context
            conversation_history = []
            if self.enable_session_management and request.session_id:
                conversation_history = await self.session_manager.get_context_for_query(
                    request.session_id
                )
            
            # Step 3: Retrieve relevant documents
            retrieval_results = await self.hybrid_retriever.retrieve(
                processed_query=processed_query,
                top_k=request.max_results or self.default_top_k,
                mode=self._determine_retrieval_mode(processed_query)
            )
            
            # Step 4: Stream response generation
            complete_response = ""
            async for chunk in self.response_generator.generate_streaming_response(
                processed_query=processed_query,
                retrieved_results=retrieval_results,
                conversation_history=conversation_history,
                session_id=request.session_id
            ):
                complete_response += chunk
                yield chunk
            
            # Step 5: Update session after streaming is complete (background task)
            if self.enable_session_management and request.session_id:
                # Create a mock response object for session storage
                mock_response = QueryResponse(
                    answer=complete_response,
                    sources=[],  # Sources will be processed separately
                    session_id=request.session_id,
                    processing_time=0.0,  # Will be updated
                    metadata={}
                )
                
                asyncio.create_task(
                    self.session_manager.add_conversation_turn(
                        session_id=request.session_id,
                        processed_query=processed_query,
                        response=mock_response
                    )
                )
            
        except Exception as e:
            logger.error(f"Error in streaming RAG pipeline: {e}", exc_info=True)
            yield f"\n\n[Error: {str(e)}]"
    
    def _determine_retrieval_mode(self, processed_query: ProcessedQuery) -> RetrievalMode:
        """Determine the optimal retrieval mode based on query characteristics."""
        # This could be made configurable or use ML-based decision making
        query_type = processed_query.query_type
        complexity = processed_query.complexity
        
        if query_type in ["definition_request", "statute_lookup"]:
            return RetrievalMode.KEYWORD_ONLY
        elif query_type in ["legal_question"] and complexity == "complex":
            return RetrievalMode.VECTOR_ONLY
        else:
            return RetrievalMode.HYBRID
    
    async def create_session(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a new session.
        
        Args:
            metadata: Optional session metadata
            
        Returns:
            Session ID
        """
        if not self.enable_session_management:
            raise ValueError("Session management is disabled")
        
        if not self._initialized:
            await self.initialize()
        
        return await self.session_manager.create_session(metadata)
    
    async def get_conversation_history(self, 
                                     session_id: str, 
                                     limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get conversation history for a session.
        
        Args:
            session_id: Session identifier
            limit: Maximum number of turns to return
            
        Returns:
            List of conversation turns
        """
        if not self.enable_session_management:
            return []
        
        if not self._initialized:
            await self.initialize()
        
        return await self.session_manager.get_conversation_history(session_id, limit)
    
    async def regenerate_response(self,
                                 request: QueryRequest,
                                 feedback: Optional[str] = None) -> QueryResponse:
        """
        Regenerate a response with optional feedback.
        
        Args:
            request: Original query request
            feedback: User feedback for improvement
            
        Returns:
            Regenerated response
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Process query
            processed_query = await self.query_processor.process_query(request)
            
            # Get conversation context
            conversation_history = []
            if self.enable_session_management and request.session_id:
                conversation_history = await self.session_manager.get_context_for_query(
                    request.session_id
                )
            
            # Retrieve documents
            retrieval_results = await self.hybrid_retriever.retrieve(
                processed_query=processed_query,
                top_k=request.max_results or self.default_top_k
            )
            
            # Regenerate response
            response = await self.response_generator.regenerate_response(
                processed_query=processed_query,
                retrieved_results=retrieval_results,
                feedback=feedback,
                session_id=request.session_id
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error regenerating response: {e}", exc_info=True)
            raise
    
    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Get stats from all components
            retrieval_stats_task = self.hybrid_retriever.get_retrieval_stats()
            generation_stats_task = self.response_generator.get_generation_stats()
            session_stats_task = (
                self.session_manager.get_session_stats()
                if self.enable_session_management
                else asyncio.sleep(0)
            )
            cache_stats_task = (
                self.cache_manager.get_cache_stats()
                if self.enable_caching
                else asyncio.sleep(0)
            )

            retrieval_stats, generation_stats, session_stats, cache_stats = await asyncio.gather(
                retrieval_stats_task, generation_stats_task, session_stats_task, cache_stats_task
            )

            return {
                "pipeline_config": {
                    "default_top_k": self.default_top_k,
                    "session_management_enabled": self.enable_session_management,
                    "caching_enabled": self.enable_caching,
                    "initialized": self._initialized
                },
                "retrieval_stats": retrieval_stats,
                "generation_stats": generation_stats,
                "session_stats": session_stats if self.enable_session_management else {},
                "cache_stats": cache_stats if self.enable_caching else {}
            }
            
        except Exception as e:
            logger.error(f"Error getting pipeline stats: {e}", exc_info=True)
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all pipeline components."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Check health of all components
            retrieval_health_task = self.hybrid_retriever.health_check()
            generation_health_task = self.response_generator.health_check()
            session_health_task = (
                self.session_manager.health_check()
                if self.enable_session_management
                else asyncio.sleep(0)
            )
            cache_health_task = (
                self.cache_manager.health_check()
                if self.enable_caching
                else asyncio.sleep(0)
            )

            retrieval_health, generation_health, session_health, cache_health = await asyncio.gather(
                retrieval_health_task, generation_health_task, session_health_task, cache_health_task
            )

            overall_health = (
                retrieval_health.get("overall", False) if isinstance(retrieval_health, dict) else retrieval_health
            ) and generation_health

            if self.enable_session_management:
                overall_health = overall_health and session_health

            if self.enable_caching:
                overall_health = overall_health and cache_health

            return {
                "overall": overall_health,
                "components": {
                    "retrieval": retrieval_health,
                    "generation": generation_health,
                    "session_management": session_health if self.enable_session_management else "disabled",
                    "caching": cache_health if self.enable_caching else "disabled"
                }
            }
            
        except Exception as e:
            logger.error(f"RAG pipeline health check failed: {e}")
            return {
                "overall": False,
                "error": str(e)
            }
    
    async def refresh_indexes(self):
        """Refresh all search indexes."""
        if not self._initialized:
            await self.initialize()
        
        await self.hybrid_retriever.refresh_indexes()
        logger.info("RAG pipeline indexes refreshed")
    
    async def close(self):
        """Close all pipeline components."""
        try:
            await asyncio.gather(
                self.hybrid_retriever.close(),
                self.response_generator.close(),
                self.session_manager.close() if self.enable_session_management else asyncio.sleep(0),
                self.cache_manager.close() if self.enable_caching else asyncio.sleep(0)
            )
            
            self._initialized = False
            logger.info("RAG pipeline closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing RAG pipeline: {e}", exc_info=True)
