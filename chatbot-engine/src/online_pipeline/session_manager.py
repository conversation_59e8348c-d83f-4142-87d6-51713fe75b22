"""
Session management for conversation history and context tracking.

This module handles user sessions, conversation history, context management,
and state persistence for the RAG chatbot system.
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

import redis.asyncio as redis

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import ProcessedQuery, QueryResponse

logger = get_logger(__name__)


@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation."""
    turn_id: str
    query: str
    response: str
    query_metadata: Dict[str, Any]
    response_metadata: Dict[str, Any]
    timestamp: datetime
    processing_time: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with JSON-serializable types."""
        return {
            "turn_id": self.turn_id,
            "query": self.query,
            "response": self.response,
            "query_metadata": self.query_metadata,
            "response_metadata": self.response_metadata,
            "timestamp": self.timestamp.isoformat(),
            "processing_time": self.processing_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationTurn':
        """Create from dictionary."""
        return cls(
            turn_id=data["turn_id"],
            query=data["query"],
            response=data["response"],
            query_metadata=data["query_metadata"],
            response_metadata=data["response_metadata"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            processing_time=data["processing_time"]
        )


@dataclass
class Session:
    """Represents a user session."""
    session_id: str
    created_at: datetime
    last_activity: datetime
    conversation_turns: List[ConversationTurn]
    context: Dict[str, Any]
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with JSON-serializable types."""
        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "conversation_turns": [turn.to_dict() for turn in self.conversation_turns],
            "context": self.context,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """Create from dictionary."""
        return cls(
            session_id=data["session_id"],
            created_at=datetime.fromisoformat(data["created_at"]),
            last_activity=datetime.fromisoformat(data["last_activity"]),
            conversation_turns=[
                ConversationTurn.from_dict(turn_data) 
                for turn_data in data["conversation_turns"]
            ],
            context=data["context"],
            metadata=data["metadata"]
        )


class SessionManager:
    """
    Manages user sessions and conversation history.
    
    This class handles session creation, conversation tracking, context management,
    and persistence using Redis for scalable session storage.
    """
    
    def __init__(self):
        """Initialize the session manager."""
        self.settings = get_settings()
        self.redis_client = None
        self._initialized = False
        
        # Configuration
        self.session_ttl = getattr(self.settings, 'session_ttl_hours', 24) * 3600  # Convert to seconds
        self.max_conversation_turns = getattr(self.settings, 'max_conversation_turns', 50)
        self.context_window_size = getattr(self.settings, 'context_window_size', 10)
        
        # Redis key prefixes
        self.session_prefix = "session:"
        self.session_index_prefix = "session_index:"
        
    async def initialize(self):
        """Initialize the session manager."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing session manager...")
            
            # Initialize Redis client
            self.redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self._initialized = True
            logger.info("Session manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize session manager: {e}", exc_info=True)
            raise
    
    async def create_session(self, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a new session.
        
        Args:
            metadata: Optional metadata for the session
            
        Returns:
            Session ID
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            session_id = str(uuid.uuid4())
            now = datetime.utcnow()
            
            session = Session(
                session_id=session_id,
                created_at=now,
                last_activity=now,
                conversation_turns=[],
                context={},
                metadata=metadata or {}
            )
            
            # Store session in Redis
            await self._store_session(session)
            
            logger.info(f"Created new session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}", exc_info=True)
            raise
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """
        Get a session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session object or None if not found
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            session_key = f"{self.session_prefix}{session_id}"
            session_data = await self.redis_client.get(session_key)
            
            if not session_data:
                return None
            
            session_dict = json.loads(session_data)
            session = Session.from_dict(session_dict)
            
            return session
            
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {e}", exc_info=True)
            return None
    
    async def add_conversation_turn(self,
                                  session_id: str,
                                  processed_query: ProcessedQuery,
                                  response: QueryResponse) -> bool:
        """
        Add a conversation turn to the session.
        
        Args:
            session_id: Session identifier
            processed_query: The processed query
            response: The generated response
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = await self.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for adding conversation turn")
                return False
            
            # Create conversation turn
            turn = ConversationTurn(
                turn_id=str(uuid.uuid4()),
                query=processed_query.original_query,
                response=response.answer,
                query_metadata={
                    "sanitized_query": processed_query.sanitized_query,
                    "query_type": processed_query.query_type,
                    "complexity": processed_query.complexity,
                    "key_terms": processed_query.key_terms,
                    "entities": processed_query.entities,
                    "processing_time": processed_query.processing_time
                },
                response_metadata=response.metadata,
                timestamp=datetime.utcnow(),
                processing_time=response.processing_time
            )
            
            # Add turn to session
            session.conversation_turns.append(turn)
            session.last_activity = datetime.utcnow()
            
            # Limit conversation history
            if len(session.conversation_turns) > self.max_conversation_turns:
                session.conversation_turns = session.conversation_turns[-self.max_conversation_turns:]
            
            # Update context
            await self._update_session_context(session, turn)
            
            # Store updated session
            await self._store_session(session)
            
            logger.info(f"Added conversation turn to session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding conversation turn to session {session_id}: {e}", exc_info=True)
            return False
    
    async def get_conversation_history(self,
                                     session_id: str,
                                     limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get conversation history for a session.
        
        Args:
            session_id: Session identifier
            limit: Maximum number of turns to return
            
        Returns:
            List of conversation turns
        """
        try:
            session = await self.get_session(session_id)
            if not session:
                return []
            
            turns = session.conversation_turns
            if limit:
                turns = turns[-limit:]
            
            return [
                {
                    "timestamp": turn.timestamp.isoformat(),
                    "query": turn.query,
                    "response": turn.response,
                    "processing_time": turn.processing_time
                }
                for turn in turns
            ]
            
        except Exception as e:
            logger.error(f"Error getting conversation history for session {session_id}: {e}", exc_info=True)
            return []
    
    async def get_context_for_query(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get relevant context for a new query.
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of relevant conversation turns for context
        """
        try:
            session = await self.get_session(session_id)
            if not session or not session.conversation_turns:
                return []
            
            # Get recent turns within context window
            recent_turns = session.conversation_turns[-self.context_window_size:]
            
            return [
                {
                    "query": turn.query,
                    "response": turn.response[:500] + "..." if len(turn.response) > 500 else turn.response,
                    "timestamp": turn.timestamp.isoformat()
                }
                for turn in recent_turns
            ]
            
        except Exception as e:
            logger.error(f"Error getting context for session {session_id}: {e}", exc_info=True)
            return []
    
    async def update_session_metadata(self,
                                    session_id: str,
                                    metadata: Dict[str, Any]) -> bool:
        """
        Update session metadata.
        
        Args:
            session_id: Session identifier
            metadata: Metadata to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = await self.get_session(session_id)
            if not session:
                return False
            
            session.metadata.update(metadata)
            session.last_activity = datetime.utcnow()
            
            await self._store_session(session)
            
            logger.info(f"Updated metadata for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating session metadata {session_id}: {e}", exc_info=True)
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session_key = f"{self.session_prefix}{session_id}"
            result = await self.redis_client.delete(session_key)
            
            if result:
                logger.info(f"Deleted session {session_id}")
                return True
            else:
                logger.warning(f"Session {session_id} not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {e}", exc_info=True)
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions.
        
        Returns:
            Number of sessions cleaned up
        """
        try:
            # Redis TTL handles automatic cleanup, but we can implement
            # additional cleanup logic here if needed
            
            # For now, just return 0 as Redis handles TTL automatically
            return 0
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}", exc_info=True)
            return 0
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics."""
        try:
            # Get all session keys
            session_keys = await self.redis_client.keys(f"{self.session_prefix}*")
            
            total_sessions = len(session_keys)
            active_sessions = 0
            total_turns = 0
            
            # Sample a few sessions for detailed stats
            sample_size = min(10, total_sessions)
            if sample_size > 0:
                sample_keys = session_keys[:sample_size]
                
                for key in sample_keys:
                    session_data = await self.redis_client.get(key)
                    if session_data:
                        session_dict = json.loads(session_data)
                        last_activity = datetime.fromisoformat(session_dict["last_activity"])
                        
                        # Consider active if activity within last hour
                        if datetime.utcnow() - last_activity < timedelta(hours=1):
                            active_sessions += 1
                        
                        total_turns += len(session_dict["conversation_turns"])
            
            return {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "average_turns_per_session": total_turns / sample_size if sample_size > 0 else 0,
                "session_ttl_hours": self.session_ttl / 3600,
                "max_conversation_turns": self.max_conversation_turns,
                "context_window_size": self.context_window_size
            }
            
        except Exception as e:
            logger.error(f"Error getting session stats: {e}", exc_info=True)
            return {}
    
    async def _store_session(self, session: Session):
        """Store session in Redis with TTL."""
        try:
            session_key = f"{self.session_prefix}{session.session_id}"
            session_data = json.dumps(session.to_dict())
            
            await self.redis_client.setex(session_key, self.session_ttl, session_data)
            
        except Exception as e:
            logger.error(f"Error storing session {session.session_id}: {e}", exc_info=True)
            raise
    
    async def _update_session_context(self, session: Session, turn: ConversationTurn):
        """Update session context based on the new conversation turn."""
        try:
            # Extract key information for context
            context_update = {
                "last_query_type": turn.query_metadata.get("query_type"),
                "last_complexity": turn.query_metadata.get("complexity"),
                "recent_topics": turn.query_metadata.get("key_terms", []),
                "last_entities": turn.query_metadata.get("entities", {}),
                "turn_count": len(session.conversation_turns)
            }
            
            session.context.update(context_update)
            
        except Exception as e:
            logger.warning(f"Error updating session context: {e}")
    
    async def health_check(self) -> bool:
        """Check if the session manager is healthy."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test Redis connection
            await self.redis_client.ping()
            return True
            
        except Exception as e:
            logger.error(f"Session manager health check failed: {e}")
            return False
    
    async def close(self):
        """Close connections and cleanup resources."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            self._initialized = False
            logger.info("Session manager closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing session manager: {e}", exc_info=True)
