"""
Query Processing Service for the Online Pipeline.

This module handles query understanding, preprocessing, and routing for the RAG system.
It includes query validation, intent detection, and preprocessing for optimal retrieval.
"""

import re
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from enum import Enum

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import QueryRequest, ProcessedQuery

logger = get_logger(__name__)


class QueryType(Enum):
    """Types of queries the system can handle."""
    LEGAL_QUESTION = "legal_question"
    CASE_LOOKUP = "case_lookup"
    STATUTE_LOOKUP = "statute_lookup"
    DEFINITION_REQUEST = "definition_request"
    PROCEDURAL_QUESTION = "procedural_question"
    GENERAL_LEGAL = "general_legal"
    UNKNOWN = "unknown"


class QueryComplexity(Enum):
    """Complexity levels for queries."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"


class QueryProcessor:
    """
    Processes and analyzes user queries for optimal retrieval and response generation.
    
    This class handles:
    - Query validation and sanitization
    - Intent detection and classification
    - Query expansion and preprocessing
    - Context extraction and preparation
    """
    
    def __init__(self):
        """Initialize the query processor."""
        self.settings = get_settings()
        self._legal_keywords = self._load_legal_keywords()
        self._stop_words = self._load_stop_words()
        
    def _load_legal_keywords(self) -> Dict[str, List[str]]:
        """Load legal domain keywords for classification."""
        return {
            "contract": ["vertrag", "vereinbarung", "kontrakt", "abkommen", "contract"],
            "tort": ["delikt", "schaden", "haftung", "tort", "liability"],
            "property": ["eigentum", "besitz", "immobilie", "property", "ownership"],
            "family": ["familie", "ehe", "scheidung", "family", "marriage", "divorce"],
            "criminal": ["strafrecht", "verbrechen", "criminal", "crime", "straftat"],
            "commercial": ["handelsrecht", "gesellschaft", "commercial", "business"],
            "constitutional": ["verfassung", "grundrecht", "constitutional", "rights"],
            "administrative": ["verwaltung", "behörde", "administrative", "authority"],
            "procedure": ["verfahren", "prozess", "procedure", "process", "klage"],
            "evidence": ["beweis", "evidence", "nachweis", "proof"]
        }
    
    def _load_stop_words(self) -> set:
        """Load stop words for query processing."""
        german_stop_words = {
            "der", "die", "das", "und", "oder", "aber", "wenn", "dann", "weil",
            "dass", "ich", "du", "er", "sie", "es", "wir", "ihr", "sie",
            "ein", "eine", "einer", "eines", "dem", "den", "des", "im", "am",
            "ist", "sind", "war", "waren", "hat", "haben", "wird", "werden",
            "kann", "könnte", "soll", "sollte", "muss", "müssen", "darf"
        }
        
        english_stop_words = {
            "the", "and", "or", "but", "if", "then", "because", "that",
            "i", "you", "he", "she", "it", "we", "they", "a", "an",
            "is", "are", "was", "were", "has", "have", "will", "would",
            "can", "could", "should", "must", "may", "might"
        }
        
        return german_stop_words.union(english_stop_words)
    
    async def process_query(self, request: QueryRequest) -> ProcessedQuery:
        """
        Process a user query and prepare it for retrieval.
        
        Args:
            request: The original query request
            
        Returns:
            ProcessedQuery with analysis and preprocessing results
        """
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Processing query: {request.query[:100]}...")
            
            # Validate and sanitize query
            sanitized_query = self._sanitize_query(request.query)
            
            # Detect query type and intent
            query_type = self._detect_query_type(sanitized_query)
            complexity = self._assess_complexity(sanitized_query)
            
            # Extract key terms and entities
            key_terms = self._extract_key_terms(sanitized_query)
            entities = self._extract_entities(sanitized_query)
            
            # Generate query variations for better retrieval
            query_variations = self._generate_query_variations(sanitized_query, key_terms)
            
            # Prepare search terms
            search_terms = self._prepare_search_terms(sanitized_query, key_terms)
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            processed_query = ProcessedQuery(
                original_query=request.query,
                sanitized_query=sanitized_query,
                query_type=query_type.value,
                complexity=complexity.value,
                key_terms=key_terms,
                entities=entities,
                query_variations=query_variations,
                search_terms=search_terms,
                session_id=request.session_id,
                processing_time=processing_time,
                metadata={
                    "language": self._detect_language(sanitized_query),
                    "word_count": len(sanitized_query.split()),
                    "has_legal_terms": len(key_terms) > 0,
                    "timestamp": start_time.isoformat()
                }
            )
            
            logger.info(f"Query processed successfully in {processing_time:.3f}s")
            return processed_query
            
        except Exception as e:
            logger.error(f"Error processing query: {e}", exc_info=True)
            raise
    
    def _sanitize_query(self, query: str) -> str:
        """Sanitize and clean the input query."""
        # Remove excessive whitespace
        query = re.sub(r'\s+', ' ', query.strip())
        
        # Remove potentially harmful characters
        query = re.sub(r'[<>"\']', '', query)
        
        # Limit query length
        max_length = self.settings.max_query_length if hasattr(self.settings, 'max_query_length') else 1000
        if len(query) > max_length:
            query = query[:max_length]
            logger.warning(f"Query truncated to {max_length} characters")
        
        return query
    
    def _detect_query_type(self, query: str) -> QueryType:
        """Detect the type of legal query."""
        query_lower = query.lower()
        
        # Check for specific patterns
        if any(word in query_lower for word in ["definition", "bedeutung", "was ist", "what is"]):
            return QueryType.DEFINITION_REQUEST
        
        if any(word in query_lower for word in ["verfahren", "prozess", "procedure", "how to"]):
            return QueryType.PROCEDURAL_QUESTION
        
        if any(word in query_lower for word in ["fall", "urteil", "case", "judgment"]):
            return QueryType.CASE_LOOKUP
        
        if any(word in query_lower for word in ["gesetz", "paragraph", "artikel", "law", "statute"]):
            return QueryType.STATUTE_LOOKUP
        
        # Check for legal domain keywords
        for domain, keywords in self._legal_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return QueryType.LEGAL_QUESTION
        
        # Default to general legal if contains question words
        if any(word in query_lower for word in ["wie", "was", "wann", "wo", "warum", "how", "what", "when", "where", "why"]):
            return QueryType.GENERAL_LEGAL
        
        return QueryType.UNKNOWN
    
    def _assess_complexity(self, query: str) -> QueryComplexity:
        """Assess the complexity of the query."""
        word_count = len(query.split())
        
        # Count legal terms
        legal_term_count = sum(
            1 for domain_keywords in self._legal_keywords.values()
            for keyword in domain_keywords
            if keyword in query.lower()
        )
        
        # Count question words and conjunctions
        complex_indicators = ["und", "oder", "aber", "jedoch", "sowie", "and", "or", "but", "however"]
        conjunction_count = sum(1 for indicator in complex_indicators if indicator in query.lower())
        
        if word_count > 30 or legal_term_count > 3 or conjunction_count > 2:
            return QueryComplexity.COMPLEX
        elif word_count > 15 or legal_term_count > 1 or conjunction_count > 0:
            return QueryComplexity.MODERATE
        else:
            return QueryComplexity.SIMPLE
    
    def _extract_key_terms(self, query: str) -> List[str]:
        """Extract key terms from the query."""
        words = query.lower().split()
        
        # Remove stop words and short words
        key_terms = [
            word for word in words 
            if word not in self._stop_words and len(word) > 2
        ]
        
        # Add legal domain terms
        legal_terms = []
        for domain_keywords in self._legal_keywords.values():
            for keyword in domain_keywords:
                if keyword in query.lower() and keyword not in legal_terms:
                    legal_terms.append(keyword)
        
        return list(set(key_terms + legal_terms))
    
    def _extract_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract named entities from the query."""
        entities = {
            "laws": [],
            "courts": [],
            "dates": [],
            "amounts": [],
            "organizations": []
        }
        
        # Extract law references (simplified)
        law_pattern = r'\b(BGB|StGB|GG|ZPO|StPO|HGB|AO)\b'
        entities["laws"] = re.findall(law_pattern, query, re.IGNORECASE)
        
        # Extract paragraph references
        paragraph_pattern = r'§\s*(\d+[a-z]?)'
        paragraphs = re.findall(paragraph_pattern, query)
        if paragraphs:
            entities["laws"].extend([f"§ {p}" for p in paragraphs])
        
        # Extract dates (simplified)
        date_pattern = r'\b(\d{1,2}\.?\d{1,2}\.?\d{2,4})\b'
        entities["dates"] = re.findall(date_pattern, query)
        
        # Extract monetary amounts
        amount_pattern = r'\b(\d+(?:\.\d{3})*(?:,\d{2})?\s*(?:€|EUR|Euro))\b'
        entities["amounts"] = re.findall(amount_pattern, query, re.IGNORECASE)
        
        return entities
    
    def _generate_query_variations(self, query: str, key_terms: List[str]) -> List[str]:
        """Generate variations of the query for better retrieval."""
        variations = [query]
        
        # Add key terms only version
        if key_terms:
            variations.append(" ".join(key_terms))
        
        # Add question without question words
        question_words = ["wie", "was", "wann", "wo", "warum", "how", "what", "when", "where", "why"]
        words = query.split()
        filtered_words = [word for word in words if word.lower() not in question_words]
        if len(filtered_words) != len(words):
            variations.append(" ".join(filtered_words))
        
        return list(set(variations))
    
    def _prepare_search_terms(self, query: str, key_terms: List[str]) -> Dict[str, Any]:
        """Prepare search terms for different retrieval methods."""
        return {
            "vector_search": query,  # Full query for semantic search
            "keyword_search": " ".join(key_terms),  # Key terms for BM25
            "hybrid_terms": key_terms,  # Individual terms for hybrid search
            "boost_terms": [term for term in key_terms if len(term) > 4]  # Longer terms for boosting
        }
    
    def _detect_language(self, query: str) -> str:
        """Detect the language of the query (simplified)."""
        german_indicators = ["der", "die", "das", "und", "ist", "sind", "haben", "werden"]
        english_indicators = ["the", "and", "is", "are", "have", "will", "can", "should"]
        
        query_lower = query.lower()
        german_count = sum(1 for word in german_indicators if word in query_lower)
        english_count = sum(1 for word in english_indicators if word in query_lower)
        
        if german_count > english_count:
            return "de"
        elif english_count > german_count:
            return "en"
        else:
            return "unknown"
