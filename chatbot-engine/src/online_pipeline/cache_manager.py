"""
Cache management for performance optimization.

This module provides Redis-based caching for queries, embeddings, responses,
and retrieval results to improve system performance and reduce latency.
"""

import json
import hashlib
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta

import redis.asyncio as redis

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.models import ProcessedQuery, QueryResponse
from .retrieval.result_fusion import FusedResult

logger = get_logger(__name__)


class CacheManager:
    """
    Manages caching for the RAG pipeline components.
    
    This class provides caching capabilities for:
    - Query processing results
    - Retrieval results
    - Generated responses
    - Embeddings
    - Session data
    """
    
    def __init__(self):
        """Initialize the cache manager."""
        self.settings = get_settings()
        self.redis_client = None
        self._initialized = False
        
        # Cache configuration
        self.query_cache_ttl = getattr(self.settings, 'query_cache_ttl_hours', 1) * 3600
        self.response_cache_ttl = getattr(self.settings, 'response_cache_ttl_hours', 24) * 3600
        self.embedding_cache_ttl = getattr(self.settings, 'embedding_cache_ttl_hours', 168) * 3600  # 1 week
        self.retrieval_cache_ttl = getattr(self.settings, 'retrieval_cache_ttl_hours', 6) * 3600
        
        # Cache key prefixes
        self.query_prefix = "query_cache:"
        self.response_prefix = "response_cache:"
        self.embedding_prefix = "embedding_cache:"
        self.retrieval_prefix = "retrieval_cache:"
        
    async def initialize(self):
        """Initialize the cache manager."""
        if self._initialized:
            return
            
        try:
            logger.info("Initializing cache manager...")
            
            # Initialize Redis client
            self.redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self._initialized = True
            logger.info("Cache manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}", exc_info=True)
            raise
    
    def _generate_cache_key(self, prefix: str, data: Union[str, Dict[str, Any]]) -> str:
        """Generate a cache key from data."""
        if isinstance(data, str):
            content = data
        else:
            content = json.dumps(data, sort_keys=True)
        
        # Create hash of the content
        hash_object = hashlib.md5(content.encode())
        hash_hex = hash_object.hexdigest()
        
        return f"{prefix}{hash_hex}"
    
    async def cache_processed_query(self, 
                                   original_query: str, 
                                   processed_query: ProcessedQuery) -> bool:
        """
        Cache a processed query result.
        
        Args:
            original_query: The original query string
            processed_query: The processed query result
            
        Returns:
            True if cached successfully, False otherwise
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.query_prefix, original_query)
            
            # Prepare cache data
            cache_data = {
                "processed_query": {
                    "original_query": processed_query.original_query,
                    "sanitized_query": processed_query.sanitized_query,
                    "query_type": processed_query.query_type,
                    "complexity": processed_query.complexity,
                    "key_terms": processed_query.key_terms,
                    "entities": processed_query.entities,
                    "query_variations": processed_query.query_variations,
                    "search_terms": processed_query.search_terms,
                    "processing_time": processed_query.processing_time,
                    "metadata": processed_query.metadata
                },
                "cached_at": datetime.utcnow().isoformat()
            }
            
            # Cache with TTL
            await self.redis_client.setex(
                cache_key, 
                self.query_cache_ttl, 
                json.dumps(cache_data)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error caching processed query: {e}", exc_info=True)
            return False
    
    async def get_cached_processed_query(self, original_query: str) -> Optional[ProcessedQuery]:
        """
        Get a cached processed query result.
        
        Args:
            original_query: The original query string
            
        Returns:
            Cached ProcessedQuery or None if not found
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.query_prefix, original_query)
            cached_data = await self.redis_client.get(cache_key)
            
            if not cached_data:
                return None
            
            data = json.loads(cached_data)
            query_data = data["processed_query"]
            
            # Reconstruct ProcessedQuery
            processed_query = ProcessedQuery(
                original_query=query_data["original_query"],
                sanitized_query=query_data["sanitized_query"],
                query_type=query_data["query_type"],
                complexity=query_data["complexity"],
                key_terms=query_data["key_terms"],
                entities=query_data["entities"],
                query_variations=query_data["query_variations"],
                search_terms=query_data["search_terms"],
                processing_time=query_data["processing_time"],
                metadata=query_data["metadata"]
            )
            
            logger.info(f"Retrieved cached processed query for: {original_query[:50]}...")
            return processed_query
            
        except Exception as e:
            logger.error(f"Error retrieving cached processed query: {e}", exc_info=True)
            return None
    
    async def cache_retrieval_results(self,
                                    query_key: str,
                                    results: List[FusedResult]) -> bool:
        """
        Cache retrieval results.
        
        Args:
            query_key: Key identifying the query
            results: List of retrieval results
            
        Returns:
            True if cached successfully, False otherwise
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.retrieval_prefix, query_key)
            
            # Prepare cache data
            cache_data = {
                "results": [result.to_dict() for result in results],
                "cached_at": datetime.utcnow().isoformat(),
                "result_count": len(results)
            }
            
            # Cache with TTL
            await self.redis_client.setex(
                cache_key,
                self.retrieval_cache_ttl,
                json.dumps(cache_data)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error caching retrieval results: {e}", exc_info=True)
            return False
    
    async def get_cached_retrieval_results(self, query_key: str) -> Optional[List[Dict[str, Any]]]:
        """
        Get cached retrieval results.
        
        Args:
            query_key: Key identifying the query
            
        Returns:
            Cached retrieval results or None if not found
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.retrieval_prefix, query_key)
            cached_data = await self.redis_client.get(cache_key)
            
            if not cached_data:
                return None
            
            data = json.loads(cached_data)
            
            logger.info(f"Retrieved {data['result_count']} cached retrieval results")
            return data["results"]
            
        except Exception as e:
            logger.error(f"Error retrieving cached retrieval results: {e}", exc_info=True)
            return None
    
    async def cache_response(self,
                           query_key: str,
                           response: QueryResponse) -> bool:
        """
        Cache a generated response.
        
        Args:
            query_key: Key identifying the query
            response: The generated response
            
        Returns:
            True if cached successfully, False otherwise
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.response_prefix, query_key)
            
            # Prepare cache data
            cache_data = {
                "response": {
                    "answer": response.answer,
                    "sources": response.sources,
                    "processing_time": response.processing_time,
                    "metadata": response.metadata
                },
                "cached_at": datetime.utcnow().isoformat()
            }
            
            # Cache with TTL
            await self.redis_client.setex(
                cache_key,
                self.response_cache_ttl,
                json.dumps(cache_data)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error caching response: {e}", exc_info=True)
            return False
    
    async def get_cached_response(self, query_key: str, session_id: Optional[str] = None) -> Optional[QueryResponse]:
        """
        Get a cached response.
        
        Args:
            query_key: Key identifying the query
            session_id: Session ID to include in response
            
        Returns:
            Cached QueryResponse or None if not found
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.response_prefix, query_key)
            cached_data = await self.redis_client.get(cache_key)
            
            if not cached_data:
                return None
            
            data = json.loads(cached_data)
            response_data = data["response"]
            
            # Reconstruct QueryResponse
            response = QueryResponse(
                answer=response_data["answer"],
                sources=response_data["sources"],
                session_id=session_id,
                processing_time=response_data["processing_time"],
                metadata=response_data["metadata"]
            )
            
            # Add cache metadata
            response.metadata["cached"] = True
            response.metadata["cached_at"] = data["cached_at"]
            
            logger.info(f"Retrieved cached response for query")
            return response
            
        except Exception as e:
            logger.error(f"Error retrieving cached response: {e}", exc_info=True)
            return None
    
    async def cache_embedding(self, text: str, embedding: List[float]) -> bool:
        """
        Cache an embedding.
        
        Args:
            text: The text that was embedded
            embedding: The embedding vector
            
        Returns:
            True if cached successfully, False otherwise
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.embedding_prefix, text)
            
            # Prepare cache data
            cache_data = {
                "embedding": embedding,
                "text": text,
                "cached_at": datetime.utcnow().isoformat()
            }
            
            # Cache with TTL
            await self.redis_client.setex(
                cache_key,
                self.embedding_cache_ttl,
                json.dumps(cache_data)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error caching embedding: {e}", exc_info=True)
            return False
    
    async def get_cached_embedding(self, text: str) -> Optional[List[float]]:
        """
        Get a cached embedding.
        
        Args:
            text: The text to get embedding for
            
        Returns:
            Cached embedding or None if not found
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            cache_key = self._generate_cache_key(self.embedding_prefix, text)
            cached_data = await self.redis_client.get(cache_key)
            
            if not cached_data:
                return None
            
            data = json.loads(cached_data)
            
            logger.info(f"Retrieved cached embedding for text: {text[:50]}...")
            return data["embedding"]
            
        except Exception as e:
            logger.error(f"Error retrieving cached embedding: {e}", exc_info=True)
            return None
    
    async def invalidate_cache(self, pattern: str) -> int:
        """
        Invalidate cache entries matching a pattern.
        
        Args:
            pattern: Redis key pattern to match
            
        Returns:
            Number of keys deleted
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"Invalidated {deleted} cache entries matching pattern: {pattern}")
                return deleted
            return 0
            
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}", exc_info=True)
            return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self._initialized:
            await self.initialize()
            
        try:
            # Get key counts for different cache types
            query_keys = await self.redis_client.keys(f"{self.query_prefix}*")
            response_keys = await self.redis_client.keys(f"{self.response_prefix}*")
            embedding_keys = await self.redis_client.keys(f"{self.embedding_prefix}*")
            retrieval_keys = await self.redis_client.keys(f"{self.retrieval_prefix}*")
            
            return {
                "cache_counts": {
                    "queries": len(query_keys),
                    "responses": len(response_keys),
                    "embeddings": len(embedding_keys),
                    "retrievals": len(retrieval_keys)
                },
                "cache_ttls": {
                    "query_ttl_hours": self.query_cache_ttl / 3600,
                    "response_ttl_hours": self.response_cache_ttl / 3600,
                    "embedding_ttl_hours": self.embedding_cache_ttl / 3600,
                    "retrieval_ttl_hours": self.retrieval_cache_ttl / 3600
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}", exc_info=True)
            return {}
    
    async def health_check(self) -> bool:
        """Check if the cache manager is healthy."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test Redis connection
            await self.redis_client.ping()
            return True
            
        except Exception as e:
            logger.error(f"Cache manager health check failed: {e}")
            return False
    
    async def close(self):
        """Close connections and cleanup resources."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            self._initialized = False
            logger.info("Cache manager closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing cache manager: {e}", exc_info=True)
