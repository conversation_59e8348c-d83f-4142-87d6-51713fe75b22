"""Shared data models for RAG Legal Chatbot."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class SourceType(str, Enum):
    """Types of data sources."""
    WEBSITE = "website"
    PDF = "pdf"
    TEXT = "text"


class ProcessingStatus(str, Enum):
    """Processing status for data sources."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class DataSource(BaseModel):
    """Data source configuration model."""
    id: Optional[str] = None
    name: str
    source_type: SourceType
    url: Optional[str] = None
    path: Optional[str] = None
    crawl_depth: Optional[int] = 2
    enabled: bool = True
    last_processed: Optional[datetime] = None
    status: ProcessingStatus = ProcessingStatus.PENDING
    metadata: Dict[str, Any] = Field(default_factory=dict)


class DocumentChunk(BaseModel):
    """Document chunk model."""
    id: Optional[str] = None
    content: str
    source_id: str
    source_url: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)


class QueryRequest(BaseModel):
    """Query request model."""
    query: str = Field(..., min_length=1, max_length=1000)
    session_id: Optional[str] = None
    max_results: int = Field(5, ge=1, le=20)
    include_sources: bool = True


class ProcessedQuery(BaseModel):
    """Processed query model with analysis results."""
    original_query: str
    sanitized_query: str
    query_type: str
    complexity: str
    key_terms: List[str] = Field(default_factory=list)
    entities: Dict[str, List[str]] = Field(default_factory=dict)
    query_variations: List[str] = Field(default_factory=list)
    search_terms: Dict[str, Any] = Field(default_factory=dict)
    session_id: Optional[str] = None
    processing_time: float
    metadata: Dict[str, Any] = Field(default_factory=dict)


class QueryResponse(BaseModel):
    """Query response model."""
    answer: str
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    session_id: Optional[str] = None
    processing_time: float
    metadata: Dict[str, Any] = Field(default_factory=dict)


class HealthStatus(BaseModel):
    """Health check status model."""
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    services: Dict[str, str] = Field(default_factory=dict)


class AdminSourceRequest(BaseModel):
    """Admin request to add/update data source."""
    name: str = Field(..., min_length=1, max_length=200)
    source_type: SourceType
    url: Optional[str] = None
    path: Optional[str] = None
    crawl_depth: Optional[int] = Field(2, ge=1, le=10)
    enabled: bool = True
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ReindexRequest(BaseModel):
    """Request to trigger reindexing."""
    source_ids: Optional[List[str]] = None
    force: bool = False


class MetricsResponse(BaseModel):
    """System metrics response."""
    total_documents: int
    total_chunks: int
    active_sources: int
    last_update: Optional[datetime]
    system_health: Dict[str, Any] = Field(default_factory=dict)
