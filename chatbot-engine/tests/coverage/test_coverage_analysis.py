"""Test coverage analysis and enhancement for the RAG Legal Chatbot."""

import os
import sys
import subprocess
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Tuple, Any
try:
    import coverage
    from coverage.report import Reporter
    from coverage.results import Numbers
except ImportError:
    # Fallback if coverage modules are not available
    coverage = None
    Reporter = None
    Numbers = None


class CoverageAnalyzer:
    """Analyze and enhance test coverage for the project."""
    
    def __init__(self, source_dir: str = "src", test_dir: str = "tests"):
        """Initialize coverage analyzer."""
        self.source_dir = Path(source_dir)
        self.test_dir = Path(test_dir)
        self.coverage_threshold = 95.0
        self.critical_modules = [
            "online_pipeline/rag_pipeline.py",
            "online_pipeline/retrieval/hybrid_retriever.py",
            "offline_pipeline/pipeline.py",
            "admin_interface/services/system_monitor.py",
            "shared/config.py"
        ]
    
    def run_coverage_analysis(self) -> Dict[str, Any]:
        """Run comprehensive coverage analysis."""
        print("🔍 Running coverage analysis...")
        
        # Run tests with coverage
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "--cov=src",
            "--cov-report=xml:coverage.xml",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=80",
            "tests/"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ Tests failed during coverage analysis:")
            print(result.stdout)
            print(result.stderr)
            return {"error": "Tests failed"}
        
        # Parse coverage results
        coverage_data = self._parse_coverage_xml("coverage.xml")
        
        # Analyze coverage by module
        module_analysis = self._analyze_module_coverage(coverage_data)
        
        # Identify missing coverage
        missing_coverage = self._identify_missing_coverage(coverage_data)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(module_analysis, missing_coverage)
        
        return {
            "overall_coverage": coverage_data.get("line_rate", 0) * 100,
            "module_analysis": module_analysis,
            "missing_coverage": missing_coverage,
            "recommendations": recommendations,
            "critical_modules_status": self._check_critical_modules(coverage_data)
        }
    
    def _parse_coverage_xml(self, xml_file: str) -> Dict[str, Any]:
        """Parse coverage XML report."""
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            coverage_data = {
                "line_rate": float(root.get("line-rate", 0)),
                "branch_rate": float(root.get("branch-rate", 0)),
                "packages": {}
            }
            
            for package in root.findall(".//package"):
                package_name = package.get("name")
                package_data = {
                    "line_rate": float(package.get("line-rate", 0)),
                    "branch_rate": float(package.get("branch-rate", 0)),
                    "classes": {}
                }
                
                for class_elem in package.findall(".//class"):
                    class_name = class_elem.get("name")
                    class_data = {
                        "filename": class_elem.get("filename"),
                        "line_rate": float(class_elem.get("line-rate", 0)),
                        "branch_rate": float(class_elem.get("branch-rate", 0)),
                        "lines": {}
                    }
                    
                    for line in class_elem.findall(".//line"):
                        line_number = int(line.get("number"))
                        hits = int(line.get("hits", 0))
                        class_data["lines"][line_number] = hits
                    
                    package_data["classes"][class_name] = class_data
                
                coverage_data["packages"][package_name] = package_data
            
            return coverage_data
        
        except (ET.ParseError, FileNotFoundError) as e:
            print(f"⚠️  Could not parse coverage XML: {e}")
            return {}
    
    def _analyze_module_coverage(self, coverage_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Analyze coverage by module."""
        module_analysis = {}
        
        for package_name, package_data in coverage_data.get("packages", {}).items():
            for class_name, class_data in package_data.get("classes", {}).items():
                filename = class_data.get("filename", "")
                line_rate = class_data.get("line_rate", 0) * 100
                
                # Determine coverage status
                if line_rate >= self.coverage_threshold:
                    status = "excellent"
                elif line_rate >= 80:
                    status = "good"
                elif line_rate >= 60:
                    status = "needs_improvement"
                else:
                    status = "critical"
                
                module_analysis[filename] = {
                    "coverage_percentage": line_rate,
                    "status": status,
                    "total_lines": len(class_data.get("lines", {})),
                    "covered_lines": sum(1 for hits in class_data.get("lines", {}).values() if hits > 0),
                    "uncovered_lines": [
                        line_num for line_num, hits in class_data.get("lines", {}).items() if hits == 0
                    ]
                }
        
        return module_analysis
    
    def _identify_missing_coverage(self, coverage_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify areas with missing test coverage."""
        missing_coverage = []
        
        for package_name, package_data in coverage_data.get("packages", {}).items():
            for class_name, class_data in package_data.get("classes", {}).items():
                filename = class_data.get("filename", "")
                line_rate = class_data.get("line_rate", 0) * 100
                
                if line_rate < self.coverage_threshold:
                    uncovered_lines = [
                        line_num for line_num, hits in class_data.get("lines", {}).items() if hits == 0
                    ]
                    
                    missing_coverage.append({
                        "file": filename,
                        "current_coverage": line_rate,
                        "target_coverage": self.coverage_threshold,
                        "gap": self.coverage_threshold - line_rate,
                        "uncovered_lines": uncovered_lines,
                        "priority": "high" if filename in [str(self.source_dir / module) for module in self.critical_modules] else "medium"
                    })
        
        # Sort by priority and gap
        missing_coverage.sort(key=lambda x: (x["priority"] == "high", x["gap"]), reverse=True)
        
        return missing_coverage
    
    def _check_critical_modules(self, coverage_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Check coverage status of critical modules."""
        critical_status = {}
        
        for module in self.critical_modules:
            module_path = str(self.source_dir / module)
            found = False
            
            for package_name, package_data in coverage_data.get("packages", {}).items():
                for class_name, class_data in package_data.get("classes", {}).items():
                    filename = class_data.get("filename", "")
                    
                    if module_path in filename or filename.endswith(module):
                        line_rate = class_data.get("line_rate", 0) * 100
                        critical_status[module] = {
                            "coverage": line_rate,
                            "status": "pass" if line_rate >= self.coverage_threshold else "fail",
                            "filename": filename
                        }
                        found = True
                        break
                
                if found:
                    break
            
            if not found:
                critical_status[module] = {
                    "coverage": 0,
                    "status": "not_found",
                    "filename": module_path
                }
        
        return critical_status
    
    def _generate_recommendations(self, module_analysis: Dict, missing_coverage: List) -> List[Dict[str, Any]]:
        """Generate recommendations for improving test coverage."""
        recommendations = []
        
        # High priority recommendations for critical modules
        for item in missing_coverage:
            if item["priority"] == "high":
                recommendations.append({
                    "type": "critical_module",
                    "priority": "high",
                    "file": item["file"],
                    "action": f"Add tests to increase coverage from {item['current_coverage']:.1f}% to {item['target_coverage']:.1f}%",
                    "specific_lines": item["uncovered_lines"][:10],  # Show first 10 uncovered lines
                    "estimated_effort": "high"
                })
        
        # Medium priority recommendations
        for item in missing_coverage:
            if item["priority"] == "medium" and item["gap"] > 20:
                recommendations.append({
                    "type": "significant_gap",
                    "priority": "medium",
                    "file": item["file"],
                    "action": f"Improve coverage by {item['gap']:.1f}% to meet threshold",
                    "specific_lines": item["uncovered_lines"][:5],
                    "estimated_effort": "medium"
                })
        
        # Low priority recommendations
        low_coverage_files = [
            filename for filename, data in module_analysis.items()
            if data["coverage_percentage"] < 80 and data["coverage_percentage"] >= 60
        ]
        
        for filename in low_coverage_files[:5]:  # Limit to top 5
            recommendations.append({
                "type": "improvement_opportunity",
                "priority": "low",
                "file": filename,
                "action": "Consider adding more comprehensive tests",
                "estimated_effort": "low"
            })
        
        return recommendations
    
    def generate_missing_tests(self, missing_coverage: List[Dict]) -> List[str]:
        """Generate skeleton test files for missing coverage."""
        test_files = []
        
        for item in missing_coverage:
            if item["priority"] == "high":
                test_file_content = self._generate_test_skeleton(item["file"], item["uncovered_lines"])
                test_filename = self._get_test_filename(item["file"])
                test_files.append((test_filename, test_file_content))
        
        return test_files
    
    def _generate_test_skeleton(self, source_file: str, uncovered_lines: List[int]) -> str:
        """Generate a test skeleton for a source file."""
        module_name = Path(source_file).stem
        class_name = "".join(word.capitalize() for word in module_name.split("_"))
        
        test_content = f'''"""Tests for {source_file}."""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from src.{source_file.replace("/", ".").replace(".py", "")} import *


class Test{class_name}:
    """Test cases for {class_name}."""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock external dependencies."""
        # TODO: Add mocks for external dependencies
        pass
    
    def test_initialization(self):
        """Test class initialization."""
        # TODO: Test object initialization
        pass
    
    def test_main_functionality(self):
        """Test main functionality."""
        # TODO: Test core methods and functions
        pass
    
    def test_error_handling(self):
        """Test error handling scenarios."""
        # TODO: Test error conditions and edge cases
        pass
    
    @pytest.mark.asyncio
    async def test_async_operations(self):
        """Test async operations if applicable."""
        # TODO: Test async methods
        pass

# TODO: Add specific tests for uncovered lines: {uncovered_lines[:10]}
# Focus on testing:
# - Edge cases and error conditions
# - Different input scenarios
# - Integration with other components
# - Performance and resource usage
'''
        return test_content
    
    def _get_test_filename(self, source_file: str) -> str:
        """Generate test filename for a source file."""
        # Convert src/module/file.py to tests/unit/test_file.py
        path_parts = Path(source_file).parts
        if path_parts[0] == "src":
            path_parts = path_parts[1:]  # Remove 'src'
        
        filename = path_parts[-1]  # Get the filename
        module_path = "/".join(path_parts[:-1])  # Get the module path
        
        test_filename = f"test_{filename}"
        test_path = f"tests/unit/{module_path}/{test_filename}" if module_path else f"tests/unit/{test_filename}"
        
        return test_path
    
    def create_coverage_report(self, analysis_result: Dict[str, Any]) -> str:
        """Create a comprehensive coverage report."""
        report = f"""
# Test Coverage Analysis Report

## Overall Coverage: {analysis_result['overall_coverage']:.1f}%

### Critical Modules Status
"""
        
        for module, status in analysis_result.get('critical_modules_status', {}).items():
            emoji = "✅" if status['status'] == "pass" else "❌"
            report += f"- {emoji} {module}: {status['coverage']:.1f}%\n"
        
        report += "\n### Coverage by Module\n"
        
        for filename, data in analysis_result.get('module_analysis', {}).items():
            status_emoji = {
                "excellent": "🟢",
                "good": "🟡", 
                "needs_improvement": "🟠",
                "critical": "🔴"
            }.get(data['status'], "⚪")
            
            report += f"- {status_emoji} {filename}: {data['coverage_percentage']:.1f}% ({data['covered_lines']}/{data['total_lines']} lines)\n"
        
        report += "\n### Recommendations\n"
        
        for i, rec in enumerate(analysis_result.get('recommendations', [])[:10], 1):
            priority_emoji = {"high": "🔥", "medium": "⚠️", "low": "💡"}.get(rec['priority'], "📝")
            report += f"{i}. {priority_emoji} **{rec['file']}**: {rec['action']}\n"
        
        return report


def main():
    """Run coverage analysis and generate report."""
    analyzer = CoverageAnalyzer()
    
    print("🚀 Starting comprehensive test coverage analysis...")
    
    # Run analysis
    analysis_result = analyzer.run_coverage_analysis()
    
    if "error" in analysis_result:
        print("❌ Coverage analysis failed!")
        return 1
    
    # Print summary
    overall_coverage = analysis_result['overall_coverage']
    print(f"\n📊 Overall Coverage: {overall_coverage:.1f}%")
    
    if overall_coverage >= analyzer.coverage_threshold:
        print("✅ Coverage threshold met!")
    else:
        gap = analyzer.coverage_threshold - overall_coverage
        print(f"❌ Coverage gap: {gap:.1f}% below threshold")
    
    # Check critical modules
    critical_status = analysis_result['critical_modules_status']
    critical_passed = sum(1 for status in critical_status.values() if status['status'] == 'pass')
    critical_total = len(critical_status)
    
    print(f"\n🎯 Critical Modules: {critical_passed}/{critical_total} passed")
    
    # Show top recommendations
    recommendations = analysis_result['recommendations']
    if recommendations:
        print(f"\n💡 Top Recommendations:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"  {i}. {rec['file']}: {rec['action']}")
    
    # Generate report
    report = analyzer.create_coverage_report(analysis_result)
    
    # Save report
    with open("coverage_report.md", "w") as f:
        f.write(report)
    
    print(f"\n📄 Detailed report saved to: coverage_report.md")
    print(f"🌐 HTML report available at: htmlcov/index.html")
    
    # Generate missing test skeletons
    missing_coverage = analysis_result.get('missing_coverage', [])
    high_priority_missing = [item for item in missing_coverage if item['priority'] == 'high']
    
    if high_priority_missing:
        print(f"\n🔧 Generating test skeletons for {len(high_priority_missing)} high-priority files...")
        test_files = analyzer.generate_missing_tests(high_priority_missing)
        
        for test_filename, test_content in test_files:
            test_path = Path(test_filename)
            test_path.parent.mkdir(parents=True, exist_ok=True)
            
            if not test_path.exists():
                with open(test_path, "w") as f:
                    f.write(test_content)
                print(f"  📝 Created: {test_filename}")
            else:
                print(f"  ⚠️  Exists: {test_filename}")
    
    # Return exit code based on coverage
    return 0 if overall_coverage >= analyzer.coverage_threshold else 1


if __name__ == "__main__":
    sys.exit(main())
