"""Unit tests for API endpoints."""

import pytest
from fastapi.testclient import TestClient


@pytest.mark.unit
def test_root_endpoint(client: TestClient):
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "docs" in data


@pytest.mark.unit
def test_health_check(client: TestClient):
    """Test the health check endpoint."""
    response = client.get("/health/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data
    assert "timestamp" in data


@pytest.mark.unit
def test_detailed_health_check(client: TestClient):
    """Test the detailed health check endpoint."""
    response = client.get("/health/detailed")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "services" in data
    assert "version" in data


@pytest.mark.unit
def test_readiness_check(client: TestClient):
    """Test the readiness probe endpoint."""
    response = client.get("/health/ready")
    # May return 200 or 503 depending on service availability
    assert response.status_code in [200, 503]
    data = response.json()
    assert "status" in data


@pytest.mark.unit
def test_liveness_check(client: TestClient):
    """Test the liveness probe endpoint."""
    response = client.get("/health/live")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "alive"


@pytest.mark.unit
def test_chat_query(client: TestClient, sample_query_request):
    """Test the chat query endpoint."""
    response = client.post("/chat/query", json=sample_query_request)
    assert response.status_code == 200
    data = response.json()
    assert "answer" in data
    assert "sources" in data
    assert "processing_time" in data
    assert data["session_id"] == sample_query_request["session_id"]


@pytest.mark.unit
def test_chat_stream(client: TestClient, sample_query_request):
    """Test the chat streaming endpoint."""
    response = client.post("/chat/stream", json=sample_query_request)
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"


@pytest.mark.unit
def test_chat_history(client: TestClient):
    """Test the chat history endpoint."""
    session_id = "test_session_123"
    response = client.get(f"/chat/sessions/{session_id}/history")
    assert response.status_code == 200
    data = response.json()
    assert data["session_id"] == session_id
    assert "messages" in data


@pytest.mark.unit
def test_clear_chat_session(client: TestClient):
    """Test clearing a chat session."""
    session_id = "test_session_123"
    response = client.delete(f"/chat/sessions/{session_id}")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data


@pytest.mark.unit
def test_admin_list_sources(client: TestClient):
    """Test listing data sources."""
    response = client.get("/admin/sources")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


@pytest.mark.unit
def test_admin_add_source(client: TestClient, sample_admin_source_request):
    """Test adding a new data source."""
    response = client.post("/admin/sources", json=sample_admin_source_request)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == sample_admin_source_request["name"]
    assert data["source_type"] == sample_admin_source_request["source_type"]


@pytest.mark.unit
def test_admin_metrics(client: TestClient):
    """Test getting system metrics."""
    response = client.get("/admin/metrics")
    assert response.status_code == 200
    data = response.json()
    assert "total_documents" in data
    assert "total_chunks" in data
    assert "active_sources" in data
