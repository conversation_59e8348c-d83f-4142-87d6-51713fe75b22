"""Unit tests for vector store implementation."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from src.offline_pipeline.storage.vector_store import MilvusVectorStore
from src.shared.models import DocumentChunk


class TestMilvusVectorStore:
    """Test cases for MilvusVectorStore."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('src.offline_pipeline.storage.vector_store.get_settings') as mock:
            mock.return_value.milvus_host = "localhost"
            mock.return_value.milvus_port = 19530
            mock.return_value.milvus_collection_name = "test_collection"
            yield mock.return_value
    
    @pytest.fixture
    def sample_chunks(self):
        """Sample document chunks for testing."""
        return [
            DocumentChunk(
                id="chunk_1",
                content="This is a test legal document about contracts.",
                source_id="source_1",
                source_url="http://example.com/doc1.html",
                embedding=[0.1, 0.2, 0.3] + [0.0] * 765,  # 768 dimensions
                metadata={"title": "Contract Law", "section": "1"}
            ),
            DocumentChunk(
                id="chunk_2",
                content="Another legal document about property rights.",
                source_id="source_1",
                source_url="http://example.com/doc2.html",
                embedding=[0.4, 0.5, 0.6] + [0.0] * 765,  # 768 dimensions
                metadata={"title": "Property Law", "section": "2"}
            )
        ]
    
    @pytest.fixture
    def vector_store(self, mock_settings):
        """Create vector store instance for testing."""
        return MilvusVectorStore()
    
    @patch('src.offline_pipeline.storage.vector_store.connections')
    @patch('src.offline_pipeline.storage.vector_store.utility')
    @patch('src.offline_pipeline.storage.vector_store.Collection')
    async def test_initialize_new_collection(self, mock_collection_class, mock_utility, mock_connections, vector_store):
        """Test initialization with new collection creation."""
        # Mock that collection doesn't exist
        mock_utility.has_collection.return_value = False
        
        # Mock collection instance
        mock_collection = Mock()
        mock_collection_class.return_value = mock_collection
        
        await vector_store.initialize()
        
        # Verify connection was established
        mock_connections.connect.assert_called_once()
        
        # Verify collection was created and loaded
        mock_utility.has_collection.assert_called_once()
        mock_collection.load.assert_called_once()
    
    @patch('src.offline_pipeline.storage.vector_store.connections')
    @patch('src.offline_pipeline.storage.vector_store.utility')
    @patch('src.offline_pipeline.storage.vector_store.Collection')
    async def test_initialize_existing_collection(self, mock_collection_class, mock_utility, mock_connections, vector_store):
        """Test initialization with existing collection."""
        # Mock that collection exists
        mock_utility.has_collection.return_value = True
        
        # Mock collection instance
        mock_collection = Mock()
        mock_collection_class.return_value = mock_collection
        
        await vector_store.initialize()
        
        # Verify connection was established
        mock_connections.connect.assert_called_once()
        
        # Verify collection was loaded but not created
        mock_utility.has_collection.assert_called_once()
        mock_collection.load.assert_called_once()
    
    async def test_store_chunks_success(self, vector_store, sample_chunks):
        """Test successful chunk storage."""
        # Mock collection
        mock_collection = Mock()
        vector_store.collection = mock_collection
        
        result = await vector_store.store_chunks(sample_chunks)
        
        assert result is True
        mock_collection.insert.assert_called_once()
        mock_collection.flush.assert_called_once()
    
    async def test_store_chunks_empty_list(self, vector_store):
        """Test storing empty chunk list."""
        result = await vector_store.store_chunks([])
        assert result is True
    
    async def test_store_chunks_no_embeddings(self, vector_store):
        """Test storing chunks without embeddings."""
        chunks = [
            DocumentChunk(
                id="chunk_1",
                content="Test content",
                source_id="source_1",
                embedding=None  # No embedding
            )
        ]
        
        # Mock collection
        mock_collection = Mock()
        vector_store.collection = mock_collection
        
        result = await vector_store.store_chunks(chunks)
        
        # Should still return True but not insert anything
        assert result is True
        mock_collection.insert.assert_not_called()
    
    async def test_search_similar(self, vector_store):
        """Test vector similarity search."""
        # Mock collection and search results
        mock_collection = Mock()
        mock_hit = Mock()
        mock_hit.entity.get.side_effect = lambda key: {
            "id": "chunk_1",
            "content": "Test content",
            "source_id": "source_1",
            "source_url": "http://example.com",
            "metadata": '{"title": "Test"}',
            "created_at": "2024-01-01T00:00:00"
        }.get(key)
        mock_hit.score = 0.95
        
        mock_collection.search.return_value = [[mock_hit]]
        vector_store.collection = mock_collection
        
        query_embedding = [0.1, 0.2, 0.3] + [0.0] * 765
        results = await vector_store.search_similar(query_embedding, top_k=5)
        
        assert len(results) == 1
        assert results[0]["id"] == "chunk_1"
        assert results[0]["score"] == 0.95
        mock_collection.search.assert_called_once()
    
    async def test_delete_by_source(self, vector_store):
        """Test deleting chunks by source ID."""
        # Mock collection
        mock_collection = Mock()
        vector_store.collection = mock_collection
        
        result = await vector_store.delete_by_source("source_1")
        
        assert result is True
        mock_collection.delete.assert_called_once()
        mock_collection.flush.assert_called_once()
    
    async def test_get_collection_stats(self, vector_store):
        """Test getting collection statistics."""
        # Mock collection
        mock_collection = Mock()
        mock_collection.get_stats.return_value = {"row_count": 100}
        vector_store.collection = mock_collection
        
        stats = await vector_store.get_collection_stats()
        
        assert stats["total_entities"] == 100
        assert "collection_name" in stats
        assert "dimension" in stats
    
    async def test_close(self, vector_store):
        """Test closing the connection."""
        # Mock collection
        mock_collection = Mock()
        vector_store.collection = mock_collection
        
        with patch('src.offline_pipeline.storage.vector_store.connections') as mock_connections:
            await vector_store.close()
            
            mock_collection.release.assert_called_once()
            mock_connections.disconnect.assert_called_once_with("default")


@pytest.mark.asyncio
class TestVectorStoreIntegration:
    """Integration tests for vector store (requires running Milvus)."""
    
    @pytest.mark.integration
    async def test_full_workflow(self):
        """Test complete vector store workflow."""
        # This test requires a running Milvus instance
        # Skip if not available
        try:
            vector_store = MilvusVectorStore()
            await vector_store.initialize()
            
            # Create test chunk
            test_chunk = DocumentChunk(
                id="integration_test_chunk",
                content="Integration test content for vector store",
                source_id="integration_test",
                embedding=[0.1] * 768,  # Simple embedding
                metadata={"test": True}
            )
            
            # Store chunk
            success = await vector_store.store_chunks([test_chunk])
            assert success
            
            # Search for similar vectors
            results = await vector_store.search_similar([0.1] * 768, top_k=1)
            assert len(results) >= 0  # May or may not find results
            
            # Clean up
            await vector_store.delete_by_source("integration_test")
            await vector_store.close()
            
        except Exception as e:
            pytest.skip(f"Milvus not available for integration test: {e}")


class TestVectorStoreErrorHandling:
    """Test error handling in vector store."""
    
    @pytest.fixture
    def vector_store(self):
        """Create vector store for error testing."""
        with patch('src.offline_pipeline.storage.vector_store.get_settings') as mock:
            mock.return_value.milvus_host = "localhost"
            mock.return_value.milvus_port = 19530
            mock.return_value.milvus_collection_name = "test_collection"
            return MilvusVectorStore()
    
    async def test_store_chunks_collection_not_initialized(self, vector_store):
        """Test storing chunks when collection is not initialized."""
        chunks = [
            DocumentChunk(
                id="chunk_1",
                content="Test",
                source_id="source_1",
                embedding=[0.1] * 768
            )
        ]
        
        with pytest.raises(RuntimeError, match="Collection not initialized"):
            await vector_store.store_chunks(chunks)
    
    async def test_search_collection_not_initialized(self, vector_store):
        """Test searching when collection is not initialized."""
        with pytest.raises(RuntimeError, match="Collection not initialized"):
            await vector_store.search_similar([0.1] * 768)
    
    async def test_delete_collection_not_initialized(self, vector_store):
        """Test deleting when collection is not initialized."""
        with pytest.raises(RuntimeError, match="Collection not initialized"):
            await vector_store.delete_by_source("source_1")
    
    async def test_stats_collection_not_initialized(self, vector_store):
        """Test getting stats when collection is not initialized."""
        with pytest.raises(RuntimeError, match="Collection not initialized"):
            await vector_store.get_collection_stats()
