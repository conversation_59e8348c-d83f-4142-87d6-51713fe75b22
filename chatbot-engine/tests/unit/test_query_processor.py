"""Unit tests for the query processor."""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime

from src.online_pipeline.query_processor import QueryProcessor, QueryType, QueryComplexity
from src.shared.models import QueryRequest, ProcessedQuery


class TestQueryProcessor:
    """Test cases for QueryProcessor."""
    
    @pytest.fixture
    def query_processor(self):
        """Create a QueryProcessor instance for testing."""
        return QueryProcessor()
    
    @pytest.fixture
    def sample_query_request(self):
        """Create a sample query request."""
        return QueryRequest(
            query="Was ist ein Vertrag nach deutschem Recht?",
            session_id="test-session-123",
            max_results=5
        )
    
    @pytest.mark.asyncio
    async def test_process_query_basic(self, query_processor, sample_query_request):
        """Test basic query processing."""
        result = await query_processor.process_query(sample_query_request)
        
        assert isinstance(result, ProcessedQuery)
        assert result.original_query == sample_query_request.query
        assert result.sanitized_query is not None
        assert result.query_type is not None
        assert result.complexity is not None
        assert isinstance(result.key_terms, list)
        assert isinstance(result.entities, dict)
        assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_sanitize_query(self, query_processor):
        """Test query sanitization."""
        # Test with malicious input
        malicious_query = "What is <script>alert('xss')</script> a contract?"
        sanitized = query_processor._sanitize_query(malicious_query)
        
        assert "<script>" not in sanitized
        assert "alert" in sanitized  # Content should remain
        
        # Test with excessive whitespace
        whitespace_query = "What   is    a     contract?"
        sanitized = query_processor._sanitize_query(whitespace_query)
        
        assert "What is a contract?" == sanitized
    
    def test_detect_query_type_definition(self, query_processor):
        """Test query type detection for definition requests."""
        definition_queries = [
            "Was ist ein Vertrag?",
            "What is a contract?",
            "Definition von Eigentum",
            "Bedeutung von Haftung"
        ]
        
        for query in definition_queries:
            query_type = query_processor._detect_query_type(query)
            assert query_type == QueryType.DEFINITION_REQUEST
    
    def test_detect_query_type_legal_section(self, query_processor):
        """Test query type detection for legal section lookups."""
        section_queries = [
            "§ 433 BGB",
            "Paragraph 123 StGB",
            "Artikel 1 GG",
            "Was steht in § 242 BGB?"
        ]
        
        for query in section_queries:
            query_type = query_processor._detect_query_type(query)
            assert query_type == QueryType.STATUTE_LOOKUP
    
    def test_detect_query_type_procedural(self, query_processor):
        """Test query type detection for procedural questions."""
        procedural_queries = [
            "Wie läuft ein Gerichtsverfahren ab?",
            "What is the procedure for filing a lawsuit?",
            "Verfahren zur Anmeldung einer Marke",
            "How to register a company?"
        ]
        
        for query in procedural_queries:
            query_type = query_processor._detect_query_type(query)
            assert query_type == QueryType.PROCEDURAL_QUESTION
    
    def test_assess_complexity_simple(self, query_processor):
        """Test complexity assessment for simple queries."""
        simple_queries = [
            "Was ist ein Vertrag?",
            "BGB § 433",
            "Contract definition"
        ]
        
        for query in simple_queries:
            complexity = query_processor._assess_complexity(query)
            assert complexity == QueryComplexity.SIMPLE
    
    def test_assess_complexity_complex(self, query_processor):
        """Test complexity assessment for complex queries."""
        complex_queries = [
            "Wie unterscheiden sich die Haftungsregelungen im Kaufrecht von denen im Werkvertragsrecht und welche Auswirkungen hat dies auf die Gewährleistungsansprüche?",
            "What are the differences between tort liability and contractual liability in cases involving multiple parties and how does this affect damage calculations?"
        ]
        
        for query in complex_queries:
            complexity = query_processor._assess_complexity(query)
            assert complexity == QueryComplexity.COMPLEX
    
    def test_extract_key_terms(self, query_processor):
        """Test key term extraction."""
        query = "Was ist die Haftung bei Vertragsbruch im deutschen Recht?"
        key_terms = query_processor._extract_key_terms(query)
        
        assert "haftung" in key_terms
        assert "vertragsbruch" in key_terms
        assert "deutschen" in key_terms
        assert "recht" in key_terms
        
        # Stop words should be filtered out
        assert "ist" not in key_terms
        assert "die" not in key_terms
        assert "bei" not in key_terms
    
    def test_extract_entities_legal_references(self, query_processor):
        """Test entity extraction for legal references."""
        query = "Nach § 433 BGB und § 280 BGB besteht eine Haftung von 1000 Euro am 01.01.2024"
        entities = query_processor._extract_entities(query)
        
        assert "laws" in entities
        assert "§ 433" in entities["laws"]
        assert "§ 280" in entities["laws"]
        assert "BGB" in entities["laws"]
        
        assert "dates" in entities
        assert "01.01.2024" in entities["dates"]
        
        assert "amounts" in entities
        assert "1000 Euro" in entities["amounts"]
    
    def test_generate_query_variations(self, query_processor):
        """Test query variation generation."""
        query = "Wie ist die Haftung bei Vertragsbruch geregelt?"
        key_terms = ["haftung", "vertragsbruch", "geregelt"]
        
        variations = query_processor._generate_query_variations(query, key_terms)
        
        assert query in variations
        assert "haftung vertragsbruch geregelt" in variations
        
        # Should have variations without question words
        question_removed = any("ist" not in var and "wie" not in var for var in variations)
        assert question_removed
    
    def test_prepare_search_terms(self, query_processor):
        """Test search terms preparation."""
        query = "Was ist die Haftung bei Vertragsbruch?"
        key_terms = ["haftung", "vertragsbruch"]
        
        search_terms = query_processor._prepare_search_terms(query, key_terms)
        
        assert "vector_search" in search_terms
        assert "keyword_search" in search_terms
        assert "hybrid_terms" in search_terms
        assert "boost_terms" in search_terms
        
        assert search_terms["vector_search"] == query
        assert search_terms["keyword_search"] == "haftung vertragsbruch"
        assert search_terms["hybrid_terms"] == key_terms
    
    def test_detect_language_german(self, query_processor):
        """Test German language detection."""
        german_queries = [
            "Was ist ein Vertrag nach deutschem Recht?",
            "Die Haftung ist im BGB geregelt",
            "Wie werden Schäden berechnet?"
        ]
        
        for query in german_queries:
            language = query_processor._detect_language(query)
            assert language == "de"
    
    def test_detect_language_english(self, query_processor):
        """Test English language detection."""
        english_queries = [
            "What is a contract under German law?",
            "The liability is regulated in the BGB",
            "How are damages calculated?"
        ]
        
        for query in english_queries:
            language = query_processor._detect_language(query)
            assert language == "en"
    
    @pytest.mark.asyncio
    async def test_process_query_with_session_id(self, query_processor):
        """Test query processing with session ID."""
        request = QueryRequest(
            query="Test query",
            session_id="test-session-456"
        )
        
        result = await query_processor.process_query(request)
        
        assert result.session_id == "test-session-456"
    
    @pytest.mark.asyncio
    async def test_process_query_metadata(self, query_processor):
        """Test that processed query includes proper metadata."""
        request = QueryRequest(query="Was ist ein Vertrag?")
        
        result = await query_processor.process_query(request)
        
        assert "language" in result.metadata
        assert "word_count" in result.metadata
        assert "has_legal_terms" in result.metadata
        assert "timestamp" in result.metadata
        
        assert result.metadata["word_count"] > 0
        assert isinstance(result.metadata["has_legal_terms"], bool)
    
    @pytest.mark.asyncio
    async def test_process_query_error_handling(self, query_processor):
        """Test error handling in query processing."""
        # Test with None query (should raise an error)
        with pytest.raises(Exception):
            request = QueryRequest(query=None)
            await query_processor.process_query(request)
    
    def test_legal_keywords_loading(self, query_processor):
        """Test that legal keywords are properly loaded."""
        keywords = query_processor._legal_keywords
        
        assert isinstance(keywords, dict)
        assert "contract" in keywords
        assert "tort" in keywords
        assert "property" in keywords
        
        # Check that keywords contain both German and English terms
        contract_keywords = keywords["contract"]
        assert "vertrag" in contract_keywords
        assert "contract" in contract_keywords
    
    def test_stop_words_loading(self, query_processor):
        """Test that stop words are properly loaded."""
        stop_words = query_processor._stop_words
        
        assert isinstance(stop_words, set)
        
        # Check German stop words
        assert "der" in stop_words
        assert "die" in stop_words
        assert "und" in stop_words
        
        # Check English stop words
        assert "the" in stop_words
        assert "and" in stop_words
        assert "or" in stop_words
