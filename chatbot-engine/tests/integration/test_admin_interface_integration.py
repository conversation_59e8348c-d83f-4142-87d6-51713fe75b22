"""
Integration tests for admin interface.

This module contains integration tests that verify the complete
admin interface functionality including API endpoints and service
integration.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from fastapi.testclient import TestClient

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from online_pipeline.api.main import create_app


class TestAdminIntegration:
    """Integration tests for admin interface."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        app = create_app()
        return TestClient(app)
    
    @pytest.fixture
    def admin_headers(self):
        """Headers for admin authentication."""
        return {"Authorization": "Bearer test_admin_key"}
    
    def test_admin_health_endpoint(self, client):
        """Test admin health check endpoint."""
        response = client.get("/admin/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "admin_interface" in data
    
    def test_system_status_endpoint(self, client, admin_headers):
        """Test system status endpoint."""
        with patch('admin_interface.services.system_monitor.SystemMonitor') as mock_monitor:
            # Mock the system monitor
            mock_instance = AsyncMock()
            mock_monitor.return_value = mock_instance
            
            mock_status = Mock()
            mock_status.overall_health = "healthy"
            mock_status.services = []
            mock_status.metrics = Mock()
            mock_status.uptime_seconds = 3600
            mock_status.version = "1.0.0"
            
            mock_instance.get_system_status.return_value = mock_status
            mock_instance.get_system_status_dict.return_value = {
                "overall_health": "healthy",
                "services": [],
                "metrics": {},
                "uptime_seconds": 3600,
                "version": "1.0.0"
            }
            
            response = client.get("/admin/system/status", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert data["overall_health"] == "healthy"
    
    def test_sources_list_endpoint(self, client, admin_headers):
        """Test sources listing endpoint."""
        with patch('admin_interface.services.source_manager.AdminSourceManager') as mock_manager:
            # Mock the source manager
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            
            mock_sources = [
                Mock(id="1", name="Test Source 1", source_type="website", enabled=True),
                Mock(id="2", name="Test Source 2", source_type="pdf", enabled=False)
            ]
            mock_instance.list_sources.return_value = mock_sources
            
            response = client.get("/admin/sources", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
    
    def test_pipeline_start_endpoint(self, client, admin_headers):
        """Test pipeline start endpoint."""
        with patch('admin_interface.services.pipeline_manager.PipelineManager') as mock_manager:
            # Mock the pipeline manager
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            
            mock_result = {
                "task_id": "test_task_123",
                "pipeline_task_id": "pipeline_test_123",
                "status": "started",
                "mode": "full",
                "source_ids": None,
                "started_at": datetime.now().isoformat()
            }
            mock_instance.start_pipeline.return_value = mock_result
            
            response = client.post("/admin/pipeline/start?mode=full", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test_task_123"
            assert data["status"] == "started"
    
    def test_analytics_summary_endpoint(self, client, admin_headers):
        """Test analytics summary endpoint."""
        with patch('admin_interface.services.analytics_service.AnalyticsService') as mock_service:
            # Mock the analytics service
            mock_instance = AsyncMock()
            mock_service.return_value = mock_instance
            
            mock_summary = {
                "queries": {"today": 50, "this_week": 300, "this_month": 1200},
                "sessions": {"active_sessions": 10, "avg_turns_per_session": 3.5},
                "performance": {"avg_response_time_ms": 250, "p95_response_time_ms": 500},
                "top_query_types": {"definition": 100, "statute_lookup": 80},
                "top_languages": {"en": 150, "de": 50},
                "last_updated": datetime.now().isoformat()
            }
            mock_instance.get_usage_summary.return_value = mock_summary
            
            response = client.get("/admin/analytics/summary", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert "queries" in data
            assert "sessions" in data
            assert "performance" in data
    
    def test_config_get_endpoint(self, client, admin_headers):
        """Test configuration get endpoint."""
        with patch('admin_interface.services.config_manager.ConfigManager') as mock_manager:
            # Mock the config manager
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            
            mock_config = {
                "app": {"name": "RAG Legal Chatbot", "version": "1.0.0"},
                "api": {"port": 8000, "host": "0.0.0.0"}
            }
            mock_instance.get_config.return_value = mock_config
            
            response = client.get("/admin/config/app_config", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert "app_config" in data
            assert data["app_config"]["app"]["name"] == "RAG Legal Chatbot"
    
    def test_config_validation_endpoint(self, client, admin_headers):
        """Test configuration validation endpoint."""
        with patch('admin_interface.services.config_manager.ConfigManager') as mock_manager:
            # Mock the config manager
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            
            mock_validation = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "config_name": "app_config"
            }
            mock_instance.validate_config.return_value = mock_validation
            
            test_config = {
                "app": {"name": "Test App"},
                "api": {"port": 8000}
            }
            
            response = client.post(
                "/admin/config/app_config/validate",
                json=test_config,
                headers=admin_headers
            )
            assert response.status_code == 200
            data = response.json()
            assert data["valid"] is True
            assert data["config_name"] == "app_config"
    
    def test_unauthorized_access(self, client):
        """Test that endpoints require authentication."""
        # Test without headers
        response = client.get("/admin/system/status")
        assert response.status_code == 403  # Should be forbidden without auth
        
        # Test with invalid token
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/admin/system/status", headers=invalid_headers)
        assert response.status_code == 401  # Should be unauthorized with invalid token
    
    def test_error_handling(self, client, admin_headers):
        """Test error handling in admin endpoints."""
        with patch('admin_interface.services.system_monitor.SystemMonitor') as mock_monitor:
            # Mock the system monitor to raise an exception
            mock_instance = AsyncMock()
            mock_monitor.return_value = mock_instance
            mock_instance.get_system_status.side_effect = Exception("Test error")
            
            response = client.get("/admin/system/status", headers=admin_headers)
            assert response.status_code == 500
            data = response.json()
            assert "detail" in data


class TestAdminWorkflow:
    """Test complete admin workflows."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        app = create_app()
        return TestClient(app)
    
    @pytest.fixture
    def admin_headers(self):
        """Headers for admin authentication."""
        return {"Authorization": "Bearer test_admin_key"}
    
    def test_source_management_workflow(self, client, admin_headers):
        """Test complete source management workflow."""
        with patch('admin_interface.services.source_manager.AdminSourceManager') as mock_manager:
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            
            # Test creating a source
            new_source_data = {
                "name": "Test Legal Website",
                "source_type": "website",
                "url": "https://example-legal.com",
                "enabled": True,
                "crawl_depth": 2
            }
            
            mock_created_source = Mock(
                id="new_source_123",
                name="Test Legal Website",
                source_type="website",
                url="https://example-legal.com",
                enabled=True
            )
            mock_instance.create_source.return_value = mock_created_source
            
            response = client.post("/admin/sources", json=new_source_data, headers=admin_headers)
            assert response.status_code == 200
            
            # Test getting the source
            mock_instance.get_source.return_value = mock_created_source
            response = client.get("/admin/sources/new_source_123", headers=admin_headers)
            assert response.status_code == 200
            
            # Test updating the source
            updated_data = new_source_data.copy()
            updated_data["enabled"] = False
            
            mock_updated_source = Mock(**updated_data, id="new_source_123")
            mock_instance.update_source.return_value = mock_updated_source
            
            response = client.put(
                "/admin/sources/new_source_123",
                json=updated_data,
                headers=admin_headers
            )
            assert response.status_code == 200
            
            # Test deleting the source
            mock_instance.delete_source.return_value = True
            response = client.delete("/admin/sources/new_source_123", headers=admin_headers)
            assert response.status_code == 200
    
    def test_pipeline_management_workflow(self, client, admin_headers):
        """Test complete pipeline management workflow."""
        with patch('admin_interface.services.pipeline_manager.PipelineManager') as mock_manager:
            mock_instance = AsyncMock()
            mock_manager.return_value = mock_instance
            
            # Test starting a pipeline
            mock_start_result = {
                "task_id": "pipeline_task_123",
                "pipeline_task_id": "pipeline_full_20250714_120000",
                "status": "started",
                "mode": "full",
                "source_ids": None,
                "started_at": datetime.now().isoformat()
            }
            mock_instance.start_pipeline.return_value = mock_start_result
            
            response = client.post("/admin/pipeline/start?mode=full", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            task_id = data["task_id"]
            
            # Test getting task status
            mock_status = {
                "task_id": task_id,
                "status": "STARTED",
                "progress": 50,
                "message": "Processing documents..."
            }
            mock_instance.get_task_status.return_value = mock_status
            
            response = client.get(f"/admin/pipeline/tasks/{task_id}/status", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == task_id
            assert data["progress"] == 50
            
            # Test getting active tasks
            mock_active_tasks = [mock_status]
            mock_instance.get_active_tasks.return_value = mock_active_tasks
            
            response = client.get("/admin/pipeline/tasks/active", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert len(data["active_tasks"]) == 1
            
            # Test cancelling the task
            mock_cancel_result = {
                "task_id": task_id,
                "status": "cancelled",
                "message": "Task cancelled successfully"
            }
            mock_instance.cancel_task.return_value = mock_cancel_result
            
            response = client.post(f"/admin/pipeline/tasks/{task_id}/cancel", headers=admin_headers)
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "cancelled"


if __name__ == "__main__":
    pytest.main([__file__])
