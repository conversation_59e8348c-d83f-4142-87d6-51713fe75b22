# Application Configuration for RAG Legal Chatbot

app:
  name: "RAG Legal Chatbot"
  version: "1.0.0"
  description: "Advanced Legal Chatbot Engine with RAG capabilities"
  debug: false
  
api:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  reload: false
  access_log: true
  
# LLM Configuration
llm:
  provider: "google"
  model: "gemini-1.5-flash"
  temperature: 0.1
  max_tokens: 2048
  streaming: true
  
# Embedding Configuration
embedding:
  provider: "google"
  model: "models/text-embedding-004"
  dimension: 768
  batch_size: 100
  
# Retrieval Configuration
retrieval:
  vector_search:
    k: 20
    similarity_threshold: 0.7
  keyword_search:
    enabled: true
    k: 10
  hybrid:
    enabled: true
    vector_weight: 0.7
    keyword_weight: 0.3
  reranking:
    enabled: true
    provider: "cohere"
    top_n: 5
    
# Text Processing Configuration
text_processing:
  chunking:
    strategy: "recursive"
    chunk_size: 1000
    chunk_overlap: 200
    separators: ["\n\n", "\n", "§", ".", ",", " "]
    min_chunk_size: 100
    max_chunk_size: 2000
  language: "de"  # German
  
# Database Configuration
databases:
  milvus:
    host: "localhost"
    port: 19530
    collection_name: "legal_docs_v1"
    index_type: "IVF_FLAT"
    metric_type: "COSINE"
    nlist: 1024
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    
# Crawling Configuration
crawling:
  user_agent: "RAG-Legal-Chatbot/1.0"
  delay_between_requests: 1.0
  max_concurrent_requests: 5
  timeout: 30
  respect_robots_txt: true
  max_retries: 3
  
# Security Configuration
security:
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["*"]
  rate_limiting:
    enabled: true
    requests_per_minute: 60
  authentication:
    admin_endpoints_require_auth: true
    
# Monitoring Configuration
monitoring:
  metrics:
    enabled: true
    port: 9090
  logging:
    level: "INFO"
    format: "detailed"
    file_rotation: true
    max_file_size: "10MB"
    backup_count: 5
    
# Background Jobs Configuration
celery:
  broker_url: "redis://localhost:6379/0"
  result_backend: "redis://localhost:6379/0"
  task_serializer: "json"
  accept_content: ["json"]
  result_serializer: "json"
  timezone: "UTC"
  
# Data Storage Paths
paths:
  data_root: "./chatbot-engine/data"
  sources: "./chatbot-engine/data/sources"
  crawled: "./chatbot-engine/data/crawled"
  processed: "./chatbot-engine/data/processed"
  indexes: "./chatbot-engine/data/indexes"
  logs: "./chatbot-engine/logs"
  config: "./chatbot-engine/config"
