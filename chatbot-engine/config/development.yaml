# Development Environment Configuration

app:
  debug: true
  
api:
  reload: true
  workers: 1
  
# Development-specific LLM settings
llm:
  temperature: 0.2  # Slightly higher for development
  max_tokens: 1024  # Lower for faster responses during development
  
# Development retrieval settings
retrieval:
  vector_search:
    k: 10  # Fewer results for faster development
  reranking:
    top_n: 3  # Fewer reranked results
    
# Development text processing
text_processing:
  chunking:
    chunk_size: 500  # Smaller chunks for development
    chunk_overlap: 100
    
# Development crawling settings
crawling:
  delay_between_requests: 0.5  # Faster crawling for development
  max_concurrent_requests: 3
  timeout: 15
  
# Development security (more permissive)
security:
  rate_limiting:
    enabled: false  # Disabled for development
  authentication:
    admin_endpoints_require_auth: false  # Disabled for development
    
# Development monitoring
monitoring:
  logging:
    level: "DEBUG"
    
# Development data paths (relative to project root)
paths:
  data_root: "./chatbot-engine/data"
  sources: "./chatbot-engine/data/sources"
  crawled: "./chatbot-engine/data/crawled"
  processed: "./chatbot-engine/data/processed"
  indexes: "./chatbot-engine/data/indexes"
  logs: "./chatbot-engine/logs"
