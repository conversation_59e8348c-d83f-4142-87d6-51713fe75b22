# Data Sources Configuration for RAG Legal Chatbot

websites:
  - url: "https://www.gesetze-im-internet.de/bgb/"
    name: "BGB - Bürgerliches Gesetzbuch"
    crawl_depth: 3
    enabled: true
    last_crawled: null
    
  - url: "https://www.gesetze-im-internet.de/stgb/"
    name: "StGB - Strafgesetzbuch"
    crawl_depth: 3
    enabled: true
    last_crawled: null
    
  - url: "https://www.gesetze-im-internet.de/gg/"
    name: "GG - Grundgesetz"
    crawl_depth: 2
    enabled: true
    last_crawled: null

local_pdfs:
  - path: "./chatbot-engine/data/sources/legal_commentary_vol1.pdf"
    name: "Legal Commentary Volume 1"
    enabled: true
    last_processed: null
    
  - path: "./chatbot-engine/data/sources/landmark_cases_2024.pdf"
    name: "Landmark Cases 2024"
    enabled: true
    last_processed: null

# Crawling Configuration
crawl_settings:
  delay_between_requests: 1.0  # seconds
  max_concurrent_requests: 5
  timeout: 30  # seconds
  user_agent: "RAG-Legal-Chatbot/1.0"
  respect_robots_txt: true
  
# Processing Configuration
processing_settings:
  chunk_size: 1000
  chunk_overlap: 200
  separators: ["\n\n", "\n", "§", ".", ",", " "]
  min_chunk_size: 100
  max_chunk_size: 2000
  
# Storage Configuration
storage_settings:
  vector_collection_name: "legal_docs_v1"
  keyword_index_name: "bm25_legal_index"
  backup_enabled: true
  backup_frequency: "daily"
