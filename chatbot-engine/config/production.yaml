# Production Environment Configuration

app:
  debug: false
  
api:
  reload: false
  workers: 4  # Multiple workers for production
  
# Production LLM settings
llm:
  temperature: 0.1  # Lower temperature for consistent responses
  max_tokens: 2048
  
# Production retrieval settings
retrieval:
  vector_search:
    k: 20
    similarity_threshold: 0.75  # Higher threshold for production
  reranking:
    top_n: 5
    
# Production text processing
text_processing:
  chunking:
    chunk_size: 1000
    chunk_overlap: 200
    
# Production crawling settings
crawling:
  delay_between_requests: 2.0  # More respectful crawling
  max_concurrent_requests: 3
  timeout: 60
  respect_robots_txt: true
  
# Production security (strict)
security:
  cors:
    allow_origins: ["https://yourdomain.com"]  # Restrict origins
  rate_limiting:
    enabled: true
    requests_per_minute: 30  # Stricter rate limiting
  authentication:
    admin_endpoints_require_auth: true
    
# Production monitoring
monitoring:
  metrics:
    enabled: true
  logging:
    level: "INFO"
    file_rotation: true
    
# Production database settings
databases:
  milvus:
    # Production Milvus cluster settings
    index_type: "HNSW"  # Better performance for production
    metric_type: "COSINE"
    nlist: 2048
  redis:
    # Production Redis settings with potential clustering
    password: "${REDIS_PASSWORD}"
    
# Production Celery settings
celery:
  worker_concurrency: 4
  worker_prefetch_multiplier: 1
  task_acks_late: true
  worker_disable_rate_limits: false
