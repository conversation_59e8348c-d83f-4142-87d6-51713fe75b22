# RAG Chatbot Architecture Overview

## System Architecture

The RAG Chatbot system follows a hybrid architecture pattern with two distinct but interconnected pipelines:

### 1. Offline Pipeline (Data Ingestion & Indexing)
This background processing system handles data collection, processing, and storage.

### 2. Online Pipeline (Real-Time Query Processing)
This real-time system handles user queries and generates responses.

## Detailed Component Architecture

### Offline Pipeline Components

```mermaid
graph TD
    subgraph "Data Sources"
        A[Legal Websites]
        B[PDF Documents]
        C[Configuration Files]
    end
    
    subgraph "Ingestion Layer"
        D[Source Manager]
        E[Crawl4AI Engine]
        F[Document Loaders]
    end
    
    subgraph "Processing Layer"
        G[Text Chunking]
        H[Graph Extractor]
        I[Metadata Enrichment]
        J[Embedding Generator]
    end
    
    subgraph "Storage Layer"
        K[Milvus Vector DB]
        L[BM25 Keyword Index]
        M[Metadata Store]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    J --> L
    I --> M
```

### Online Pipeline Components

```mermaid
graph TD
    subgraph "API Layer"
        N[FastAPI Server]
        O[Request Validation]
        P[Response Streaming]
    end
    
    subgraph "Retrieval Layer"
        Q[Hybrid Retriever]
        R[Vector Search]
        S[Keyword Search]
        T[Result Fusion]
    end
    
    subgraph "Enhancement Layer"
        U[Cohere Reranker]
        V[Context Formatter]
        W[Citation Manager]
    end
    
    subgraph "Generation Layer"
        X[Gemini 1.5 Flash]
        Y[Prompt Templates]
        Z[Response Formatter]
    end
    
    N --> O
    O --> Q
    Q --> R
    Q --> S
    R --> T
    S --> T
    T --> U
    U --> V
    V --> W
    W --> Y
    Y --> X
    X --> Z
    Z --> P
    P --> N
```

## Data Flow Architecture

### Offline Data Processing Flow

1. **Source Configuration**: Admin configures data sources in `sources.yaml`
2. **Web Crawling**: Crawl4AI processes websites with JavaScript rendering
3. **Document Loading**: LangChain loaders process HTML and PDF files
4. **Text Chunking**: Legal-specific text splitting with appropriate separators
5. **Entity Extraction**: Graph extractor identifies legal entities and relationships
6. **Metadata Enrichment**: Structural and semantic metadata assignment
7. **Embedding Generation**: Gemini text-embedding-004 creates vector representations
8. **Dual Storage**: Vectors stored in Milvus, keywords in BM25 index

### Online Query Processing Flow

1. **Query Reception**: FastAPI receives user query with session context
2. **Hybrid Retrieval**: Parallel vector and keyword search execution
3. **Result Fusion**: Combination and deduplication of search results
4. **Reranking**: Cohere Rerank optimizes document relevance
5. **Context Preparation**: Format documents with citations for LLM
6. **Response Generation**: Gemini 1.5 Flash generates streaming response
7. **Citation Integration**: Automatic source attribution in response
8. **Response Streaming**: Real-time delivery to client

## Technology Stack Details

### Core Framework
- **LangChain**: Orchestration framework with LCEL
- **FastAPI**: High-performance async web framework
- **Pydantic**: Data validation and serialization

### AI/ML Components
- **Gemini 1.5 Flash**: Primary language model for generation
- **Gemini text-embedding-004**: Vector embedding generation
- **Cohere Rerank**: Document relevance optimization

### Data Storage
- **Milvus**: Scalable vector database for embeddings
- **BM25**: Keyword-based search index
- **PostgreSQL**: Metadata and configuration storage (optional)

### Infrastructure
- **Docker**: Containerization platform
- **Redis/RabbitMQ**: Message broker for background jobs
- **Celery**: Distributed task queue for offline processing

## Scalability Considerations

### Horizontal Scaling
- **API Servers**: Multiple FastAPI instances behind load balancer
- **Worker Processes**: Distributed Celery workers for background tasks
- **Database Sharding**: Milvus collection partitioning for large datasets

### Performance Optimization
- **Caching**: Redis caching for frequent queries
- **Connection Pooling**: Database connection optimization
- **Async Processing**: Non-blocking I/O throughout the pipeline

### Resource Management
- **Memory Optimization**: Efficient embedding storage and retrieval
- **CPU Utilization**: Parallel processing for batch operations
- **Storage Efficiency**: Compressed vector storage in Milvus

## Security Architecture

### Authentication & Authorization
- **API Key Management**: Secure storage and rotation
- **Role-Based Access**: Different permissions for admin and user endpoints
- **Session Management**: Secure conversation tracking

### Data Protection
- **Encryption**: At-rest and in-transit data encryption
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: Protection against abuse and DoS attacks

### Privacy Considerations
- **Data Anonymization**: Removal of sensitive information
- **Audit Logging**: Comprehensive access and operation logging
- **Compliance**: GDPR and legal data handling requirements

## Monitoring and Observability

### Application Metrics
- **Response Times**: API latency tracking
- **Error Rates**: Failed request monitoring
- **Throughput**: Queries per second measurement

### System Metrics
- **Resource Usage**: CPU, memory, disk utilization
- **Database Performance**: Milvus query performance
- **Network Metrics**: Bandwidth and connection monitoring

### Business Metrics
- **Query Success Rate**: Percentage of successful responses
- **User Satisfaction**: Response quality metrics
- **System Availability**: Uptime and reliability tracking

## Deployment Architecture

### Development Environment
- **Docker Compose**: Local development stack
- **Hot Reloading**: Fast development iteration
- **Test Data**: Synthetic legal documents for testing

### Production Environment
- **Kubernetes**: Container orchestration (optional)
- **Load Balancing**: High availability and performance
- **Auto-scaling**: Dynamic resource allocation

### CI/CD Pipeline
- **Automated Testing**: Unit, integration, and performance tests
- **Build Automation**: Docker image creation and versioning
- **Deployment Automation**: Zero-downtime deployments

This architecture provides a robust, scalable foundation for the RAG chatbot system while maintaining flexibility for future enhancements and optimizations.
