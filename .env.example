# API Keys
GOOGLE_API_KEY=your_google_ai_api_key_here
COHERE_API_KEY=your_cohere_api_key_here

# Milvus Configuration
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=legal_docs_v1

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Application Configuration
APP_NAME=RAG Legal Chatbot
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Embedding Configuration
EMBEDDING_MODEL=models/text-embedding-004
EMBEDDING_DIMENSION=768

# LLM Configuration
LLM_MODEL=gemini-1.5-flash
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2048

# Retrieval Configuration
VECTOR_SEARCH_K=20
RERANK_TOP_N=5
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Data Paths
DATA_DIR=./chatbot-engine/data
SOURCES_CONFIG=./chatbot-engine/config/sources.yaml
CRAWLED_DATA_DIR=./chatbot-engine/data/crawled
PROCESSED_DATA_DIR=./chatbot-engine/data/processed
INDEXES_DIR=./chatbot-engine/data/indexes

# Security
SECRET_KEY=your_secret_key_here
ADMIN_API_KEY=your_admin_api_key_here

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
