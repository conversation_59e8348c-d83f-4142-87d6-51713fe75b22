# TLS Certificate Management for RAG Legal Chatbot

# Certificate Issuer for Let's Encrypt (Production)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>  # Replace with your email
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
    - dns01:
        cloudflare:  # Example for Cloudflare DNS
          email: <EMAIL>
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token
---
# Certificate Issuer for Let's Encrypt (Staging)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
spec:
  acme:
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: <EMAIL>  # Replace with your email
    privateKeySecretRef:
      name: letsencrypt-staging
    solvers:
    - http01:
        ingress:
          class: nginx
---
# Self-signed Certificate Issuer (for development)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: selfsigned-issuer
spec:
  selfSigned: {}
---
# Certificate for RAG Chatbot (Production)
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: rag-chatbot-tls
  namespace: rag-chatbot
spec:
  secretName: rag-chatbot-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - rag-chatbot.yourdomain.com
  - api.rag-chatbot.yourdomain.com
  - admin.rag-chatbot.yourdomain.com
---
# Certificate for Internal Services
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: rag-chatbot-internal-tls
  namespace: rag-chatbot
spec:
  secretName: rag-chatbot-internal-tls
  issuerRef:
    name: selfsigned-issuer
    kind: ClusterIssuer
  dnsNames:
  - rag-chatbot-api-service.rag-chatbot.svc.cluster.local
  - redis-service.rag-chatbot.svc.cluster.local
  - milvus-service.rag-chatbot.svc.cluster.local
  - nginx-service.rag-chatbot.svc.cluster.local
---
# Cloudflare API Token Secret (for DNS validation)
apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-api-token
  namespace: cert-manager
type: Opaque
data:
  api-token: ""  # Base64 encoded Cloudflare API token
---
# TLS Configuration for Nginx
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-tls-config
  namespace: rag-chatbot
data:
  ssl.conf: |
    # Modern TLS configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # SSL session settings
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/ssl/certs/ca-certificates.crt;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    
    # Hide server information
    server_tokens off;
    more_clear_headers Server;
    
    # DH parameters for perfect forward secrecy
    ssl_dhparam /etc/nginx/ssl/dhparam.pem;
---
# Certificate Monitoring
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager-metrics
  namespace: cert-manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
  endpoints:
  - port: tcp-prometheus-servicemonitor
    interval: 60s
    path: /metrics
---
# Certificate Expiry Alert
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: certificate-expiry
  namespace: rag-chatbot
spec:
  groups:
  - name: certificate-expiry
    rules:
    - alert: CertificateExpiringSoon
      expr: certmanager_certificate_expiration_timestamp_seconds - time() < 7 * 24 * 3600
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "Certificate expiring soon"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} expires in less than 7 days"
    
    - alert: CertificateExpired
      expr: certmanager_certificate_expiration_timestamp_seconds - time() < 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Certificate expired"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} has expired"
