{"timestamp": "2025-07-14T13:26:44.172187", "overall_status": "fail", "total_time": 1.6188879013061523, "summary": {"total_checks": 12, "passed": 4, "failed": 7, "warnings": 0, "skipped": 1}, "results": [{"name": "Code Formatting", "status": "fail", "message": "Code formatting issues found", "execution_time": 0.11572480201721191, "details": {"diff": ""}}, {"name": "Flake8 Linting", "status": "fail", "message": "Found 1 linting issues", "execution_time": 0.08401298522949219, "details": {"issues": ["src/:0:1: E902 FileNotFoundError: [Errno 2] No such file or directory: 'src/'"]}}, {"name": "Pylint Analysis", "status": "pass", "message": "Code quality score: 10.0/10.0", "execution_time": 0.021264314651489258, "details": {"score": 10.0, "issues": []}}, {"name": "Type Checking", "status": "fail", "message": "Found 0 type errors", "execution_time": 0.21231794357299805, "details": {"errors": []}}, {"name": "Security Analysis (Bandit)", "status": "pass", "message": "Security score: 10.0/10.0 (0 issues)", "execution_time": 0.020650148391723633, "details": {"score": 10.0, "issues": []}}, {"name": "Dependency Security (Safety)", "status": "fail", "message": "Found 0 vulnerable dependencies", "execution_time": 0.02068305015563965, "details": {"vulnerabilities": []}}, {"name": "Unit Tests", "status": "fail", "message": "Some unit tests failed", "execution_time": 0.3624088764190674, "details": {"summary": [], "output": "============================= test session starts ==============================\nplatform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/RAG-Chatbot-Augmentcode/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/RAG-Chatbot-Augmentcode\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, cov-6.2.1, langsmith-0.4.5, asyncio-1.0.0\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 0 items\n\n============================ no tests ran in 0.00s =============================\n"}}, {"name": "Integration Tests", "status": "fail", "message": "Some integration tests failed", "execution_time": 0.3571441173553467, "details": {"output": "============================= test session starts ==============================\nplatform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0 -- /Users/<USER>/RAG-Chatbot-Augmentcode/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/RAG-Chatbot-Augmentcode\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, cov-6.2.1, langsmith-0.4.5, asyncio-1.0.0\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 0 items\n\n============================ no tests ran in 0.00s =============================\n"}}, {"name": "Test Coverage", "status": "fail", "message": "Coverage: 0.0% (target: 95.0%)", "execution_time": 0.4226078987121582, "details": {"coverage": 0.0, "output": "============================= test session starts ==============================\nplatform darwin -- Python 3.9.6, pytest-8.4.1, pluggy-1.6.0\nrootdir: /Users/<USER>/RAG-Chatbot-Augmentcode\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, cov-6.2.1, langsmith-0.4.5, asyncio-1.0.0\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollected 0 items\n\n============================ no tests ran in 0.00s =============================\n"}}, {"name": "Performance Tests", "status": "pass", "message": "Performance tests completed successfully", "execution_time": 0.0, "details": {"note": "Placeholder - implement actual performance tests"}}, {"name": "Documentation Coverage", "status": "skip", "message": "No Python files found", "execution_time": 0.0, "details": null}, {"name": "Dependency Check", "status": "pass", "message": "Found 51 dependencies in requirements.txt", "execution_time": 0.0015673637390136719, "details": {"count": 51}}]}