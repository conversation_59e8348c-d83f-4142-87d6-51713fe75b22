groups:
  - name: rag-chatbot-alerts
    rules:
      # High-level service availability alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second for service {{ $labels.job }}."

      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High latency detected"
          description: "95th percentile latency is {{ $value }}s for service {{ $labels.job }}."

  - name: system-alerts
    rules:
      # System resource alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on instance {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on instance {{ $labels.instance }}."

      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is {{ $value }}% on {{ $labels.device }} at {{ $labels.instance }}."

      - alert: DiskSpaceRunningOut
        expr: predict_linear(node_filesystem_avail_bytes{fstype!="tmpfs"}[6h], 24*60*60) < 0
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Disk space running out"
          description: "Disk {{ $labels.device }} at {{ $labels.instance }} will run out of space in less than 24 hours."

  - name: container-alerts
    rules:
      # Container-specific alerts
      - alert: ContainerHighCPU
        expr: rate(container_cpu_usage_seconds_total{name!=""}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container high CPU usage"
          description: "Container {{ $labels.name }} CPU usage is {{ $value }}%."

      - alert: ContainerHighMemory
        expr: (container_memory_usage_bytes{name!=""} / container_spec_memory_limit_bytes{name!=""}) * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container high memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%."

      - alert: ContainerRestarting
        expr: rate(container_last_seen{name!=""}[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Container restarting"
          description: "Container {{ $labels.name }} is restarting frequently."

  - name: application-alerts
    rules:
      # RAG Chatbot specific alerts
      - alert: APIResponseTimeHigh
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="rag-chatbot-api"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "API response time is high"
          description: "95th percentile API response time is {{ $value }}s."

      - alert: HighRequestRate
        expr: rate(http_requests_total{job="rag-chatbot-api"}[5m]) > 100
        for: 5m
        labels:
          severity: info
          component: api
        annotations:
          summary: "High request rate"
          description: "API is receiving {{ $value }} requests per second."

      - alert: CeleryQueueBacklog
        expr: celery_queue_length > 100
        for: 5m
        labels:
          severity: warning
          component: worker
        annotations:
          summary: "Celery queue backlog"
          description: "Celery queue has {{ $value }} pending tasks."

      - alert: CeleryWorkerDown
        expr: celery_workers_active == 0
        for: 2m
        labels:
          severity: critical
          component: worker
        annotations:
          summary: "No active Celery workers"
          description: "All Celery workers are down."

  - name: database-alerts
    rules:
      # Redis alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
          component: redis
        annotations:
          summary: "Redis is down"
          description: "Redis instance is not responding."

      - alert: RedisHighMemoryUsage
        expr: (redis_memory_used_bytes / redis_memory_max_bytes) * 100 > 90
        for: 5m
        labels:
          severity: warning
          component: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value }}%."

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          component: redis
        annotations:
          summary: "Redis high connection count"
          description: "Redis has {{ $value }} connected clients."

      # Milvus alerts
      - alert: MilvusDown
        expr: up{job="milvus"} == 0
        for: 1m
        labels:
          severity: critical
          component: milvus
        annotations:
          summary: "Milvus is down"
          description: "Milvus vector database is not responding."

      - alert: MilvusHighQueryLatency
        expr: milvus_query_latency_seconds > 5
        for: 5m
        labels:
          severity: warning
          component: milvus
        annotations:
          summary: "Milvus high query latency"
          description: "Milvus query latency is {{ $value }}s."

  - name: security-alerts
    rules:
      # Security-related alerts
      - alert: TooManyFailedLogins
        expr: rate(http_requests_total{status="401"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          type: security
        annotations:
          summary: "Too many failed login attempts"
          description: "{{ $value }} failed login attempts per second."

      - alert: SuspiciousActivity
        expr: rate(http_requests_total{status="403"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          type: security
        annotations:
          summary: "Suspicious activity detected"
          description: "{{ $value }} forbidden requests per second."

  - name: business-alerts
    rules:
      # Business logic alerts
      - alert: LowQuerySuccessRate
        expr: (rate(http_requests_total{status="200",endpoint="/chat"}[5m]) / rate(http_requests_total{endpoint="/chat"}[5m])) * 100 < 95
        for: 10m
        labels:
          severity: warning
          type: business
        annotations:
          summary: "Low query success rate"
          description: "Query success rate is {{ $value }}%."

      - alert: NoQueriesReceived
        expr: rate(http_requests_total{endpoint="/chat"}[5m]) == 0
        for: 15m
        labels:
          severity: warning
          type: business
        annotations:
          summary: "No queries received"
          description: "No chat queries received in the last 15 minutes."
