apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
      manageAlerts: true
      alertmanagerUid: "alertmanager"
    secureJsonData: {}

  # Loki datasource for logs
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      timeout: 60
      derivedFields:
        - datasourceUid: "jaeger"
          matcherRegex: "traceID=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"

  # Jaeger datasource for tracing
  - name: Jaeger
    type: jaeger
    uid: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: "loki"
        tags: ["job", "instance", "pod", "namespace"]
        mappedTags: [
          { key: "service.name", value: "service" }
        ]
        mapTagNamesEnabled: false
        spanStartTimeShift: "1h"
        spanEndTimeShift: "1h"
        filterByTraceID: false
        filterBySpanID: false

  # AlertManager datasource
  - name: AlertManager
    type: alertmanager
    uid: alertmanager
    access: proxy
    url: http://alertmanager:9093
    editable: true
    jsonData:
      implementation: "prometheus"
