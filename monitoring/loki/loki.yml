auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://alertmanager:9093

# Limits configuration
limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h
  ingestion_rate_mb: 16
  ingestion_burst_size_mb: 32
  max_streams_per_user: 10000
  max_line_size: 256000
  max_entries_limit_per_query: 5000
  max_global_streams_per_user: 5000
  unordered_writes: true

# Chunk store configuration
chunk_store_config:
  max_look_back_period: 0s

# Table manager configuration
table_manager:
  retention_deletes_enabled: true
  retention_period: 168h

# Compactor configuration
compactor:
  working_directory: /loki/boltdb-shipper-compactor
  shared_store: filesystem

# Analytics
analytics:
  reporting_enabled: false
