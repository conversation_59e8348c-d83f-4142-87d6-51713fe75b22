# Performance Optimization Configuration for RAG Legal Chatbot

# Redis Performance Optimization
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-performance-config
  namespace: rag-chatbot
data:
  redis.conf: |
    # Memory optimization
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    maxmemory-samples 10
    
    # Performance tuning
    tcp-keepalive 300
    timeout 0
    tcp-backlog 511
    
    # Persistence optimization
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    
    # AOF optimization
    appendonly yes
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    aof-load-truncated yes
    aof-use-rdb-preamble yes
    
    # Lazy freeing
    lazyfree-lazy-eviction yes
    lazyfree-lazy-expire yes
    lazyfree-lazy-server-del yes
    replica-lazy-flush yes
    
    # Client optimization
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    
    # Slow log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Hash optimization
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    
    # List optimization
    list-max-ziplist-size -2
    list-compress-depth 0
    
    # Set optimization
    set-max-intset-entries 512
    
    # Sorted set optimization
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    
    # HyperLogLog optimization
    hll-sparse-max-bytes 3000
    
    # Stream optimization
    stream-node-max-bytes 4096
    stream-node-max-entries 100
    
    # Active rehashing
    activerehashing yes
    
    # Hz frequency
    hz 10
    dynamic-hz yes
    
    # Incremental fsync
    aof-rewrite-incremental-fsync yes
    rdb-save-incremental-fsync yes
---
# Milvus Performance Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: milvus-performance-config
  namespace: rag-chatbot
data:
  milvus.yaml: |
    # Milvus performance configuration
    etcd:
      endpoints:
        - etcd-service:2379
      rootPath: by-dev
      metaSubPath: meta
      kvSubPath: kv
      log:
        level: info
    
    minio:
      address: minio-service
      port: 9000
      accessKeyID: minioadmin
      secretAccessKey: minioadmin
      useSSL: false
      bucketName: a-bucket
      rootPath: files
      useIAM: false
      cloudProvider: aws
      iamEndpoint: 
    
    # Query node configuration
    queryNode:
      cacheSize: **********  # 2GB cache
      loadMemoryUsageFactor: 3
      enableDisk: true
      diskCacheSize: **********  # 4GB disk cache
    
    # Index node configuration
    indexNode:
      scheduler:
        buildParallel: 1
    
    # Data node configuration
    dataNode:
      flush:
        insertBufSize: 16777216  # 16MB
        deleteBufBytes: 67108864  # 64MB
    
    # Root coordinator configuration
    rootCoord:
      dmlChannelNum: 256
      maxPartitionNum: 4096
      minSegmentSizeToEnableIndex: 1024
    
    # Proxy configuration
    proxy:
      timeTickInterval: 200
      msgStreamTimeTickBufSize: 512
      maxNameLength: 255
      maxFieldNum: 256
      maxDimension: 32768
      maxShardNum: 256
      maxTaskNum: 1024
    
    # Common configuration
    common:
      defaultPartitionName: _default
      defaultIndexName: _default_idx
      entityExpiration: -1
      indexSliceSize: 16
      threadCoreCoefficient: 10
      
    # Log configuration
    log:
      level: info
      file:
        rootPath: /var/lib/milvus/logs
        maxSize: 300
        maxAge: 10
        maxBackups: 20
      format: text
---
# Application Performance Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-performance-config
  namespace: rag-chatbot
data:
  performance.yaml: |
    # FastAPI performance settings
    fastapi:
      workers: 4
      worker_class: uvicorn.workers.UvicornWorker
      worker_connections: 1000
      max_requests: 1000
      max_requests_jitter: 100
      timeout: 60
      keepalive: 5
      preload_app: true
    
    # Database connection pooling
    database:
      redis:
        max_connections: 100
        retry_on_timeout: true
        socket_keepalive: true
        socket_keepalive_options:
          TCP_KEEPIDLE: 1
          TCP_KEEPINTVL: 3
          TCP_KEEPCNT: 5
        connection_pool_kwargs:
          max_connections: 50
          retry_on_timeout: true
      
      milvus:
        pool_size: 10
        max_overflow: 20
        timeout: 30
        retry_times: 3
    
    # Caching configuration
    caching:
      ttl: 3600  # 1 hour
      max_size: 1000
      strategy: lru
      compression: true
      
      # Query result caching
      query_cache:
        enabled: true
        ttl: 1800  # 30 minutes
        max_entries: 500
      
      # Embedding caching
      embedding_cache:
        enabled: true
        ttl: 7200  # 2 hours
        max_entries: 1000
    
    # Async processing
    async:
      max_workers: 10
      queue_size: 100
      timeout: 300
    
    # Rate limiting
    rate_limiting:
      requests_per_minute: 60
      burst_size: 10
      
    # Memory management
    memory:
      max_memory_usage: 2048  # MB
      gc_threshold: 0.8
      
    # Logging optimization
    logging:
      level: INFO
      async_logging: true
      buffer_size: 1000
      flush_interval: 5
---
# Nginx Performance Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-performance-config
  namespace: rag-chatbot
data:
  nginx.conf: |
    worker_processes auto;
    worker_rlimit_nofile 65535;
    worker_cpu_affinity auto;
    
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 4096;
        use epoll;
        multi_accept on;
        accept_mutex off;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Performance optimizations
        sendfile on;
        sendfile_max_chunk 1m;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        keepalive_requests 1000;
        
        # Buffer sizes
        client_body_buffer_size 128k;
        client_max_body_size 10m;
        client_header_buffer_size 1k;
        large_client_header_buffers 4 4k;
        output_buffers 1 32k;
        postpone_output 1460;
        
        # Timeouts
        client_header_timeout 3m;
        client_body_timeout 3m;
        send_timeout 3m;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # Brotli compression (if available)
        # brotli on;
        # brotli_comp_level 6;
        # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Caching
        open_file_cache max=1000 inactive=20s;
        open_file_cache_valid 30s;
        open_file_cache_min_uses 2;
        open_file_cache_errors on;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;
        limit_req_zone $binary_remote_addr zone=chat:10m rate=10r/s;
        limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
        
        # Upstream configuration
        upstream api_backend {
            least_conn;
            keepalive 32;
            keepalive_requests 1000;
            keepalive_timeout 60s;
            
            server rag-chatbot-api-service:8000 max_fails=3 fail_timeout=30s weight=1;
            # Add more servers for load balancing
        }
        
        # Proxy cache
        proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m use_temp_path=off;
        
        server {
            listen 80;
            server_name _;
            
            # Security headers
            add_header X-Frame-Options DENY always;
            add_header X-Content-Type-Options nosniff always;
            add_header X-XSS-Protection "1; mode=block" always;
            
            # API endpoints
            location /api/ {
                limit_req zone=api burst=30 nodelay;
                limit_conn conn_limit_per_ip 10;
                
                proxy_pass http://api_backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                # Proxy timeouts
                proxy_connect_timeout 10s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
                
                # Proxy buffering
                proxy_buffering on;
                proxy_buffer_size 4k;
                proxy_buffers 8 4k;
                proxy_busy_buffers_size 8k;
                
                # Caching for GET requests
                proxy_cache api_cache;
                proxy_cache_valid 200 302 10m;
                proxy_cache_valid 404 1m;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
                proxy_cache_lock on;
                proxy_cache_lock_timeout 5s;
                
                add_header X-Cache-Status $upstream_cache_status;
            }
            
            # Chat streaming endpoint
            location /chat/stream {
                limit_req zone=chat burst=15 nodelay;
                limit_conn conn_limit_per_ip 5;
                
                proxy_pass http://api_backend/chat/stream;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Disable buffering for streaming
                proxy_buffering off;
                proxy_cache off;
                proxy_read_timeout 600s;
                proxy_send_timeout 600s;
            }
            
            # Health check
            location /health {
                proxy_pass http://api_backend/health;
                proxy_cache api_cache;
                proxy_cache_valid 200 30s;
                access_log off;
            }
            
            # Static files (if any)
            location /static/ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                access_log off;
            }
        }
    }
