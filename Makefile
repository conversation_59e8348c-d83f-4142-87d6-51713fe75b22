.PHONY: help install install-dev test test-unit test-integration test-e2e lint format type-check clean build docker-build docker-up docker-down logs check-services init-services start-pipeline

# Default target
help:
	@echo "Available commands:"
	@echo ""
	@echo "📦 Installation:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-e2e         Run end-to-end tests only"
	@echo ""
	@echo "🔍 Code Quality:"
	@echo "  lint             Run linting (flake8)"
	@echo "  format           Format code (black, isort)"
	@echo "  type-check       Run type checking (mypy)"
	@echo ""
	@echo "🐳 Docker:"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-up        Start services with Docker Compose"
	@echo "  docker-down      Stop services with Docker Compose"
	@echo "  logs             Show Docker Compose logs"
	@echo ""
	@echo "🔧 Services:"
	@echo "  check-services   Check health of all services"
	@echo "  init-services    Initialize services and data stores"
	@echo "  start-api        Start FastAPI server"
	@echo "  start-worker     Start Celery worker"
	@echo "  start-flower     Start Flower monitoring"
	@echo ""
	@echo "🚀 Pipeline:"
	@echo "  start-pipeline   Run offline processing pipeline"
	@echo "  validate-config  Validate configuration"
	@echo ""
	@echo "🛠️  Build:"
	@echo "  clean            Clean build artifacts"
	@echo "  build            Build package"

# Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install -e ".[dev]"

# Testing
test:
	pytest

test-unit:
	pytest -m unit

test-integration:
	pytest -m integration

test-e2e:
	pytest -m e2e

# Code quality
lint:
	flake8 chatbot-engine/src

format:
	black chatbot-engine/src chatbot-engine/tests
	isort chatbot-engine/src chatbot-engine/tests

type-check:
	mypy chatbot-engine/src

# Build and clean
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

# Docker commands
docker-build:
	docker build -t rag-legal-chatbot .

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down

logs:
	docker-compose logs -f

# Development workflow
dev-setup: install-dev
	cp .env.example .env
	@echo "Please edit .env file with your API keys"

dev-check: format lint type-check test-unit

# Service management
check-services:
	cd chatbot-engine && python scripts/check_services.py

init-services:
	cd chatbot-engine && python scripts/init_services.py

validate-config:
	cd chatbot-engine && python scripts/validate_config.py

# Application services
start-api:
	cd chatbot-engine && uvicorn src.online_pipeline.api.main:app --host 0.0.0.0 --port 8000 --reload

start-worker:
	cd chatbot-engine && celery -A src.shared.celery_app worker --loglevel=info --queues=default,crawling,processing,embeddings,storage,orchestration

start-flower:
	cd chatbot-engine && celery -A src.shared.celery_app flower --port=5555

# Pipeline operations
start-pipeline:
	cd chatbot-engine && python -c "import asyncio; from src.offline_pipeline.pipeline import create_pipeline, PipelineMode; asyncio.run(create_pipeline().run_pipeline(PipelineMode.FULL))"

start-pipeline-incremental:
	cd chatbot-engine && python -c "import asyncio; from src.offline_pipeline.pipeline import create_pipeline, PipelineMode; asyncio.run(create_pipeline().run_pipeline(PipelineMode.INCREMENTAL))"

validate-pipeline:
	cd chatbot-engine && python -c "import asyncio; from src.offline_pipeline.pipeline import create_pipeline, PipelineMode; asyncio.run(create_pipeline().run_pipeline(PipelineMode.VALIDATE))"
